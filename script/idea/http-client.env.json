{"local": {"baseUrl": "http://127.0.0.1:48080/admin-api", "systemBaseUrl": "http://127.0.0.1:48081/admin-api", "infaBaseUrl": "http://127.0.0.1:48082/admin-api", "token": "test1", "adminTenentId": "1", "tag": "${HOSTNAME}", "appApi": "http://127.0.0.1:48080/app-api", "appToken": "test1", "appTenentId": "1"}, "gateway": {"baseUrl": "http://127.0.0.1:48080/admin-api", "systemBaseUrl": "http://127.0.0.1:48080/admin-api", "infaBaseUrl": "http://127.0.0.1:48080/admin-api", "token": "test1", "adminTenentId": "1", "tag": "${HOSTNAME}", "appApi": "http://127.0.0.1:8888/app-api", "appToken": "test1", "appTenentId": "1"}}