<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.powerchina.bjy</groupId>
        <artifactId>cloud-institute-plan</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>cloud-hydrologic</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>cloud-hydrologic-api</module>
        <module>cloud-hydrologic-biz</module>
    </modules>

    <name>${project.artifactId}</name>
    <description>
        plan 模块 ： 规划院 - 小工具集
        hydrologic ：水文数据管理平台
        ess : 8760
    </description>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://**********:8083/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://**********:8083/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


</project>