package cn.powerchina.bjy.cloud.institute.hydrologic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * “文件树类型”枚举
 */
@Getter
@AllArgsConstructor
public enum FileTreeTypeEnum {

    /**
     * 资源站点
     */
    RESOURCE_SITE(0),

    /**
     * 常规水电
     */
    PROJECT(1);


    private final Integer type;
}