package cn.powerchina.bjy.cloud.institute.hydrologic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * “水文站点类型编码”与“角色权限适用项目绑定类型编码”的映射枚举
 */
@Getter
@AllArgsConstructor
public enum HydrologicStationTypeMapRoleApplyBindTypeEnum {

    STATION_WEATHER(3, 2),
    STATION_RAINFALL(1, 3),
    STATION_HYDROLOGIC(2, 4);


    private final Integer hydrologicStationType;
    private final Integer roleApplyBindType;

    private static final Map<Integer, HydrologicStationTypeMapRoleApplyBindTypeEnum> hydrologicStationTypeMap =
            Arrays.stream(HydrologicStationTypeMapRoleApplyBindTypeEnum.values()).collect(Collectors.toMap(temp -> temp.getHydrologicStationType(), temp -> temp));

    /**
     * 根据水文站点类型编码获取角色权限适用项目绑定类型编码
     * @param hydrologicStationType
     * @return
     */
    public static Integer getBindType(Integer hydrologicStationType) {
        return Optional.ofNullable(hydrologicStationTypeMap.get(hydrologicStationType))
                .map(HydrologicStationTypeMapRoleApplyBindTypeEnum::getRoleApplyBindType).orElse(null);
    }
}