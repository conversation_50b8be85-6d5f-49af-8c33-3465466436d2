package cn.powerchina.bjy.cloud.institute.hydrologic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * “资源站点类型编码”与“角色权限适用项目绑定类型编码”的映射枚举
 */
@Getter
@AllArgsConstructor
public enum ResourceSiteTypeMapRoleApplyBindTypeEnum {

    /**
     * 抽水蓄能
     */
    pumped_storage(4, 6),

    /**
     * 常规水电
     */
    conventional_hydro(5, 7);


    private final Integer hydrologicStationType;
    private final Integer roleApplyBindType;

    private static final Map<Integer, ResourceSiteTypeMapRoleApplyBindTypeEnum> hydrologicStationTypeMap =
            Arrays.stream(ResourceSiteTypeMapRoleApplyBindTypeEnum.values()).collect(Collectors.toMap(temp -> temp.getHydrologicStationType(), temp -> temp));

    /**
     * 根据水文站点类型编码获取角色权限适用项目绑定类型编码
     * @param hydrologicStationType
     * @return
     */
    public static Integer getBindType(Integer hydrologicStationType) {
        return Optional.ofNullable(hydrologicStationTypeMap.get(hydrologicStationType))
                .map(ResourceSiteTypeMapRoleApplyBindTypeEnum::getRoleApplyBindType).orElse(null);
    }
}