package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherstation.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 气象站分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WeatherStationPageReqVO extends PageParam {

    @Schema(description = "站名", example = "赵六")
    private String hydrologicName;

    @Schema(description = "国")
    private String country;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "县")
    private String county;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "设立机构")
    private String establishInstitution;

    @Schema(description = "经度（度）")
    private String longitude;

    @Schema(description = "纬度（度）")
    private String latitude;

    @Schema(description = "海拔高度（米）")
    private String altitude;

    @Schema(description = "站址迁移")
    private String siteRelocation;

    @Schema(description = "开始观测年份")
    private String[] startObservationYear;

    @Schema(description = "开始观测月份")
    private String[] startObservationMonth;

    @Schema(description = "缺测时间段")
    private String missingTestingTimePeriod;

    @Schema(description = "观测方式")
    private String observationMethod;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}