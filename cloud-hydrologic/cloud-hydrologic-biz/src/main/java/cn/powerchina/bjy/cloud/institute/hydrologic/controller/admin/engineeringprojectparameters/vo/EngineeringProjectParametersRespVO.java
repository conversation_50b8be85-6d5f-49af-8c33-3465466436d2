package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 工程项目信息-设计参数 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EngineeringProjectParametersRespVO {
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "27401")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "项目关联id", example = "5737")
    @ExcelProperty("项目关联id")
    private String projectId;

    @Schema(description = "参数")
    @ExcelProperty("参数")
    private String value1;

    @Schema(description = "规划")
    @ExcelProperty("规划")
    private String value2;

    @Schema(description = "规划备注")
    @ExcelProperty("规划备注")
    private String value3;

    @Schema(description = "预可")
    @ExcelProperty("预可")
    private String value4;

    @Schema(description = "预可备注")
    @ExcelProperty("预可备注")
    private String value5;

    @Schema(description = "三专")
    @ExcelProperty("三专")
    private String value6;

    @Schema(description = "三专备注")
    @ExcelProperty("三专备注")
    private String value7;

    @Schema(description = "可研")
    @ExcelProperty("可研")
    private String value8;

    @Schema(description = "可研备注")
    @ExcelProperty("可研备注")
    private String value9;

    @Schema(description = "详图")
    @ExcelProperty("详图")
    private String value10;

    @Schema(description = "详图备注")
    @ExcelProperty("详图备注")
    private String value11;

    @Schema(description = "已建")
    @ExcelProperty("已建")
    private String value12;

    @Schema(description = "已建备注")
    @ExcelProperty("已建备注")
    private String value13;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
