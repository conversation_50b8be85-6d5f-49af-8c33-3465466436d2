package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.migrationtree.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.migrationtree.MigrationTreeDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;
import java.util.*;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 备份与还原tree Response VO")
@Data
@ExcelIgnoreUnannotated
public class MigrationTreeRespVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -7405502905506442141L;
    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15746")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "层级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("层级")
    private Integer level;

    @Schema(description = "名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("名")
    private String name;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型")
    private String type;

    @Schema(description = "是否虚拟节点", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否虚拟节点")
    private Boolean virtualNode;

    @Schema(description = "parent id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30725")
    @ExcelProperty("parent id")
    private Long parentId;

    @Schema(description = "是否选中")
    private boolean selected;

    @Schema(description = "子节点")
    private List<MigrationTreeRespVO> children = new ArrayList<>();

}