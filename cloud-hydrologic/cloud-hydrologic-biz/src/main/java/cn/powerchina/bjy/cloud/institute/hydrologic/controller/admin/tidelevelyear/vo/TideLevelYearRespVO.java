package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 水文站—潮位年统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TideLevelYearRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "7528")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2151")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站", example = "2")
    @ExcelProperty("站点类型，1：雨量站，2：水文站")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "年")
    @ExcelProperty("年")
    private Integer year;

    @Schema(description = "一月")
    @ExcelProperty("一月")
    private String value1;

    @Schema(description = "二月")
    @ExcelProperty("二月")
    private String value2;

    @Schema(description = "三月")
    @ExcelProperty("三月")
    private String value3;

    @Schema(description = "四月")
    @ExcelProperty("四月")
    private String value4;

    @Schema(description = "五月")
    @ExcelProperty("五月")
    private String value5;

    @Schema(description = "六月")
    @ExcelProperty("六月")
    private String value6;

    @Schema(description = "七月")
    @ExcelProperty("七月")
    private String value7;

    @Schema(description = "八月")
    @ExcelProperty("八月")
    private String value8;

    @Schema(description = "九月")
    @ExcelProperty("九月")
    private String value9;

    @Schema(description = "十月")
    @ExcelProperty("十月")
    private String value10;

    @Schema(description = "十一月")
    @ExcelProperty("十一月")
    private String value11;

    @Schema(description = "十二月")
    @ExcelProperty("十二月")
    private String value12;

    @Schema(description = "全年")
    @ExcelProperty("全年")
    private String value13;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "记录时间")
    @ExcelProperty("记录时间")
    private LocalDate currentDay;

    @Schema(description = "版本")
    @ExcelProperty("版本")
    private Integer version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}