package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.migrationtree.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 备份与还原tree分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MigrationTreePageReqVO extends PageParam {

    @Schema(description = "层级")
    private Integer level;

    @Schema(description = "名", example = "王五")
    private String name;

    @Schema(description = "类型", example = "2")
    private String type;

    @Schema(description = "是否虚拟节点")
    private Boolean virtualNode;

    @Schema(description = "parent id", example = "30725")
    private Long parentId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}