package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelguarantee.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水文站-水位-各保证率水位 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WaterLevelGuaranteeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "754")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32149")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "最高")
    @ExcelProperty("最高")
    private String maxValue;

    @Schema(description = "第15天")
    @ExcelProperty("第15天")
    private String day15;

    @Schema(description = "第30天")
    @ExcelProperty("第30天")
    private String day30;

    @Schema(description = "第90天")
    @ExcelProperty("第90天")
    private String day90;

    @Schema(description = "第180天")
    @ExcelProperty("第180天")
    private String day180;

    @Schema(description = "第270天")
    @ExcelProperty("第270天")
    private String day270;

    @Schema(description = "最低")
    @ExcelProperty("最低")
    private String minValue;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}