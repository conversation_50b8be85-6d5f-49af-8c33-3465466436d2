package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainday;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo.StationDayDataPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainday.vo.StationRainDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainday.vo.StationRainDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainday.vo.StationRainDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainday.StationRainDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationrainday.StationRainDayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 降水-日降水量")
@RestController
@RequestMapping("/plan/hydrologic/station/rain/day")
@Validated
public class StationRainDayController {

    @Resource
    private StationRainDayService stationRainDayService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-降水-日降水量序列格式分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:query-rain')")
    public CommonResult<PageResult<StationRainDayRespVO>> getStationRainDayPage(@Valid StationRainDayPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setCreateTime(null);
        PageResult<StationRainDayDO> pageResult = stationRainDayService.getStationRainDayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainDayRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入降水-日降水量序列格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importRainStationRainMonthFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                        @RequestParam("file") MultipartFile file) {
        stationRainDayService.importStationRainDayExcel(stationId, StationEnum.RAIN.getType(), file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新降水-日降水量")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:update-rain')")
    public CommonResult<Boolean> updateStationRainDay(@Valid @RequestBody StationRainDaySaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.RAIN.getType());
        stationRainDayService.updateStationRainDay(updateReqVO);
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-降水-日降水量序列格式分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:query-hydrologic')")
    public CommonResult<PageResult<StationRainDayRespVO>> getStationHydrologicDayPage(@Valid StationRainDayPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setCreateTime(null);
        PageResult<StationRainDayDO> pageResult = stationRainDayService.getStationRainDayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainDayRespVO.class));
    }

    @PostMapping("/import/hydrologic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入降水-日降水量序列格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    public CommonResult<Boolean> importRainStationHydrologicDayFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                            @RequestParam("file") MultipartFile file) {
        stationRainDayService.importStationRainDayExcel(stationId, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新降水-日降水量")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:update-hydrologic')")
    public CommonResult<Boolean> updateStationHydrologicDay(@Valid @RequestBody StationRainDaySaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        stationRainDayService.updateStationRainDay(updateReqVO);
        return success(true);
    }

    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出降水-日降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:export-rain')")
    public void exportStationRainDayExcel(@Valid StationRainDayPageReqVO pageReqVO,
                                          HttpServletResponse response) {
        pageReqVO.setCreateTime(null);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainDayService.exportStationRainDayExcel(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出降水-日降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:export-hydrologic')")
    public void exportStationHydrologicDayExcel(@Valid StationRainDayPageReqVO pageReqVO,
                                                HttpServletResponse response) {
        pageReqVO.setCreateTime(null);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainDayService.exportStationRainDayExcel(response, pageReqVO);
    }

    @GetMapping("/page/data/rain")
    @Operation(summary = "雨量站-数据检索-降水-日降水量序列格式分页")
    @HydrologicOperation(stationType = StationEnum.RAIN, dataType = StationDataTypeEnum.STATION_RAIN_DAY)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:query-data-rain')")
    public CommonResult<PageResult<StationRainDayRespVO>> getStationRainDayDataPage(@Valid StationDayDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationRainDayPageReqVO pageReqVO1 = new StationRainDayPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationRainDayDO> pageResult = stationRainDayService.getStationRainDayPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationRainDayRespVO.class));
    }

    @GetMapping("/page/data/hydrologic")
    @Operation(summary = "水文站-数据检索-降水-日降水量序列格式分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_RAIN_DAY)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:query-data-hydrologic')")
    public CommonResult<PageResult<StationRainDayRespVO>> getStationHydrologicDayDataPage(@Valid StationDayDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationRainDayPageReqVO pageReqVO1 = new StationRainDayPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationRainDayDO> pageResult = stationRainDayService.getStationRainDayPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationRainDayRespVO.class));
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出降水-日降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:export-data-rain')")
    public void exportStationRainDayDataExcel(@Valid StationDayDataPageReqVO pageReqVO,
                                              HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationRainDayPageReqVO pageReqVO1 = new StationRainDayPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setLogId(null);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainDayService.exportStationRainDayExcel(response, pageReqVO1);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出降水-日降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-day:export-data-hydrologic')")
    public void exportStationHydrologicDayDataExcel(@Valid StationDayDataPageReqVO pageReqVO,
                                                    HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationRainDayPageReqVO pageReqVO1 = new StationRainDayPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setLogId(null);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainDayService.exportStationRainDayExcel(response, pageReqVO1);
    }

}