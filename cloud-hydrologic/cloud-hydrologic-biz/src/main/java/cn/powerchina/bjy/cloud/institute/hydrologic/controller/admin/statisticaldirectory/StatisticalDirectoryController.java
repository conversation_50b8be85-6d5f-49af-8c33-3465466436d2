package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticaldirectory;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticaldirectory.vo.StatisticalDirectoryPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticaldirectory.vo.StatisticalDirectoryRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticaldirectory.vo.StatisticalDirectorySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo.StatisticalInfoRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticaldirectory.StatisticalDirectoryDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticalinfo.StatisticalInfoDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticaldirectory.StatisticalDirectoryService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 水文-统计表-目录")
@RestController
@RequestMapping("/plan/hydrologic/statistical-directory")
@Validated
public class StatisticalDirectoryController {

    @Resource
    private StatisticalDirectoryService statisticalDirectoryService;

    @PostMapping("/create")
    @Operation(summary = "创建水文-统计表-目录")
   // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-directory:create')")
    public CommonResult<Long> createStatisticalDirectory(@Valid @RequestBody StatisticalDirectorySaveReqVO createReqVO) {
        return success(statisticalDirectoryService.createStatisticalDirectory(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水文-统计表-目录")
   // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-directory:update')")
    public CommonResult<Boolean> updateStatisticalDirectory(@Valid @RequestBody StatisticalDirectorySaveReqVO updateReqVO) {
        statisticalDirectoryService.updateStatisticalDirectory(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水文-统计表-目录")
    @Parameter(name = "id", description = "编号", required = true)
   // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-directory:delete')")
    public CommonResult<Boolean> deleteStatisticalDirectory(@RequestParam("id") Long id) {
        statisticalDirectoryService.deleteStatisticalDirectory(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水文-统计表-目录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
   // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-directory:query')")
    public CommonResult<StatisticalDirectoryRespVO> getStatisticalDirectory(@RequestParam("id") Long id) {
        StatisticalDirectoryDO statisticalDirectory = statisticalDirectoryService.getStatisticalDirectory(id);
        return success(BeanUtils.toBean(statisticalDirectory, StatisticalDirectoryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水文-统计表-目录分页")
   // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-directory:query')")
    public CommonResult<PageResult<StatisticalDirectoryRespVO>> getStatisticalDirectoryPage(@Valid StatisticalDirectoryPageReqVO pageReqVO) {
        PageResult<StatisticalDirectoryDO> pageResult = statisticalDirectoryService.getStatisticalDirectoryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StatisticalDirectoryRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水文-统计表-目录 Excel")
   // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-directory:export')")
//    @OperateLog(type = EXPORT)
    public void exportStatisticalDirectoryExcel(@Valid StatisticalDirectoryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<StatisticalDirectoryDO> list = statisticalDirectoryService.getStatisticalDirectoryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "水文-统计表-目录.xls", "数据", StatisticalDirectoryRespVO.class,
                        BeanUtils.toBean(list, StatisticalDirectoryRespVO.class));
    }


}