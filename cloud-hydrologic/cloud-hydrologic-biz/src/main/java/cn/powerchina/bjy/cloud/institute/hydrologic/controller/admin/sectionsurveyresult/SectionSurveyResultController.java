package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sectionsurveyresult.SectionSurveyResultService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sectionsurveyresult.dataprocessor.SectionSurveyResultProcessorImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;
@Tag(name = "管理后台 - 实测大断面成果表")
@RestController
@RequestMapping("/plan/hydrologic/section-survey-result")
public class SectionSurveyResultController {
    @Autowired
    private SectionSurveyResultService sectionSurveyResultService;
    @Autowired
    private SectionSurveyResultProcessorImpl sectionSurveyResultProcessor;

    // 1. 分页查询接口
    @GetMapping("/page")
    @Operation(summary = "获得实测大断面成果分页")
    @HydrologicOperation
    public CommonResult<PageResult<SectionSurveyRespVO>> getSectionSurveyPage(@Valid SectionSurveyPageReqVO pageReqVO) {
        PageResult<SectionSurveyRespVO> pageResult = sectionSurveyResultService.getSectionSurveyPage(pageReqVO);
        return success(pageResult);
    }

    // 2. 创建接口
    @PostMapping("/create")
    @Operation(summary = "创建实测大断面成果")
    public CommonResult<Long> createSectionSurvey(@Valid @RequestBody SectionSurveySaveReqVO createReqVO) {
        return success(sectionSurveyResultService.createSectionSurvey(createReqVO));
    }

    // 3. 更新接口
    @PutMapping("/update")
    @Operation(summary = "更新实测大断面成果")
    public CommonResult<Boolean> updateSectionSurvey(@Valid @RequestBody SectionSurveyUpdateReqVO updateReqVO) throws IOException{
        sectionSurveyResultService.updateSectionSurvey(updateReqVO);
        return success(true);
    }
//    @PutMapping("/update")
//    @Operation(summary = "更新实测大断面成果")
//    public CommonResult<Boolean> updateSectionSurvey(@Valid @RequestBody SectionSurveySaveReqVO updateReqVO) {
//        sectionSurveyResultService.updateSectionSurvey(updateReqVO);
//        return success(true);
//    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除实测大断面成果")
    public CommonResult<Boolean> deleteSectionSurvey(@Valid @RequestBody SectionSurveyDeleteReqVO deleteReqVO) {
        sectionSurveyResultService.deleteSectionSurvey(deleteReqVO);
        return success(true);
    }



    // 5. 获取详情接口
    @GetMapping("/get/{id}")
    @Operation(summary = "获得实测大断面成果详情")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<SectionSurveyRespVO> getSectionSurvey(@PathVariable("id") Long id) {
        return success(sectionSurveyResultService.getSectionSurvey(id));
    }




    @PostMapping("/import")
    @ExcelImportCheck
    @Operation(summary = "导入大断面成果表")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "站id", required = true)
    public CommonResult<Boolean> importExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam("stationId") Long stationId,
            @RequestParam("stationType") Integer stationType,
            @RequestParam("dataType") Integer dataType

    )  throws IOException{
        sectionSurveyResultService.importSectionSurveyExcel(file, stationId, stationType, dataType);
        return success(true);
    }




    @GetMapping("/export-excel")
    @Operation(summary = "导出实测大断面成果 Excel")
    public void exportSectionSurveyExcel(@Valid SectionSurveyExportPageReqVO exportReqVO,
                                         HttpServletResponse response) throws IOException {
        sectionSurveyResultService.exportSectionSurveyExcel(response, exportReqVO);
    }
}
