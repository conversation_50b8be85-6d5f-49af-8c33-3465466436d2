package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.bedsedimentgradingresult.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 实测床沙颗粒级配成果 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BedSedimentGradingResultRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15581")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11040")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("站点类型")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "年份", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("年份")
    private Integer year;

    @Schema(description = "月份", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("月份")
    private Integer month;

    @Schema(description = "日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("日")
    private Integer day;

    @Schema(description = "0.002mm粒径级百分比")
    @ExcelProperty("0.002mm粒径级百分比")
    private BigDecimal value002;

    @Schema(description = "0.004mm粒径级百分比")
    @ExcelProperty("0.004mm粒径级百分比")
    private BigDecimal value004;

    @Schema(description = "0.008mm粒径级百分比")
    @ExcelProperty("0.008mm粒径级百分比")
    private BigDecimal value008;

    @Schema(description = "0.016mm粒径级百分比")
    @ExcelProperty("0.016mm粒径级百分比")
    private BigDecimal value016;

    @Schema(description = "0.031mm粒径级百分比")
    @ExcelProperty("0.031mm粒径级百分比")
    private BigDecimal value031;

    @Schema(description = "0.062mm粒径级百分比")
    @ExcelProperty("0.062mm粒径级百分比")
    private BigDecimal value062;

    @Schema(description = "0.125mm粒径级百分比")
    @ExcelProperty("0.125mm粒径级百分比")
    private BigDecimal value125;

    @Schema(description = "0.25mm粒径级百分比")
    @ExcelProperty("0.25mm粒径级百分比")
    private BigDecimal value250;

    @Schema(description = "0.5mm粒径级百分比")
    @ExcelProperty("0.5mm粒径级百分比")
    private BigDecimal value500;

    @Schema(description = "1.0mm粒径级百分比")
    @ExcelProperty("1.0mm粒径级百分比")
    private BigDecimal value1000;

    @Schema(description = "2.0mm粒径级百分比")
    @ExcelProperty("2.0mm粒径级百分比")
    private BigDecimal value2000;

    @Schema(description = "4.0mm粒径级百分比")
    @ExcelProperty("4.0mm粒径级百分比")
    private BigDecimal value4000;

    @Schema(description = "8.0mm粒径级百分比")
    @ExcelProperty("8.0mm粒径级百分比")
    private BigDecimal value8000;

    @Schema(description = "中数粒径(mm)")
    @ExcelProperty("中数粒径(mm)")
    private BigDecimal medianDiameter;

    @Schema(description = "平均粒径(mm)")
    @ExcelProperty("平均粒径(mm)")
    private BigDecimal meanDiameter;

    @Schema(description = "最大粒径(mm)")
    @ExcelProperty("最大粒径(mm)")
    private BigDecimal maxDiameter;

    @Schema(description = "取样方法")
    @ExcelProperty("取样方法")
    private String sampleMethod;

    @Schema(description = "分析方法")
    @ExcelProperty("分析方法")
    private String analysisMethod;

    @Schema(description = "备注/附注", example = "你说的对")
    @ExcelProperty("备注/附注")
    private String remark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}