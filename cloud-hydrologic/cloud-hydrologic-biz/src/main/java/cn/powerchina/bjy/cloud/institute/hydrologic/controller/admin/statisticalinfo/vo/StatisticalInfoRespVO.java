package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 水文-统计表索引数据信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StatisticalInfoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "29319")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "站点id", example = "6060")
    @ExcelProperty("站点id")
    private Long stationId;

    @Schema(description = "统计表id", example = "12517")
    @ExcelProperty("统计表id")
    private Long statsId;

    @Schema(description = "开始年")
    @ExcelProperty("开始年")
    private String startYear;

    @Schema(description = "结束年")
    @ExcelProperty("结束年")
    private String endYear;

    @Schema(description = "年份数量", example = "16265")
    @ExcelProperty("年份数量")
    private Integer yearCount;

    @Schema(description = "年份数据 例如：2021;2022;2023")
    @ExcelProperty("年份数据 例如：2021;2022;2023")
    private String years;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "2")
    @ExcelProperty("站点类型，1：雨量站，2：水文站，3：气象站")
    private String stationType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}