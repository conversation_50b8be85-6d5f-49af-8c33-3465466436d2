package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDate;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 水文站—潮位年统计新增/修改 Request VO")
@Data
public class TideLevelYearSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "7528")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2151")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站", example = "2")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "一月")
    private String value1;

    @Schema(description = "二月")
    private String value2;

    @Schema(description = "三月")
    private String value3;

    @Schema(description = "四月")
    private String value4;

    @Schema(description = "五月")
    private String value5;

    @Schema(description = "六月")
    private String value6;

    @Schema(description = "七月")
    private String value7;

    @Schema(description = "八月")
    private String value8;

    @Schema(description = "九月")
    private String value9;

    @Schema(description = "十月")
    private String value10;

    @Schema(description = "十一月")
    private String value11;

    @Schema(description = "十二月")
    private String value12;

    @Schema(description = "全年")
    private String value13;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "记录时间")
    private LocalDate currentDay;

    @Schema(description = "版本")
    private Integer version;

}