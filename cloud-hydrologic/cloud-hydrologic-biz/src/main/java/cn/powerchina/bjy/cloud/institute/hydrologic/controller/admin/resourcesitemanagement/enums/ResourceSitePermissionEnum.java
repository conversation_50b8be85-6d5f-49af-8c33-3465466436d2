package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源站点权限枚举
 */
@Getter
@AllArgsConstructor
public enum ResourceSitePermissionEnum {

    VIEW("resource-site:view", "查看资源站点"),
    CREATE("resource-site:create", "创建资源站点"),
    UPDATE("resource-site:update", "更新资源站点"),
    DELETE("resource-site:delete", "删除资源站点");

    /**
     * 权限标识
     */
    private final String permission;

    /**
     * 权限描述
     */
    private final String desc;
}