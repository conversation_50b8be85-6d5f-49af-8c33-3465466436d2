package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationphoto.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 站点照片管理新增/修改 Request VO")
@Data
public class StationPhotoSaveReqVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "站点id")
    @NotNull(message = "请输入站点id")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站")
    @NotNull(message = "请输入站点类型")
    private Integer stationType;

    @Schema(description = "10100:降水-逐日降水量年鉴，10101:降水-逐日降水量序列，10102:降水-月降水特征值，10103:降水-年降水特征值，" +
            "10104:降水-降水量摘录表，10105:降水-各时段最大降水-按日，10106:降水-各时段最大降水-按小时，10107:降水-各时段最大降水-按分钟，" +
            "10108:蒸发-逐日水面蒸发年鉴，10109:蒸发-逐日水面蒸发序列，10110:蒸发-月水面蒸发特征值，10111:蒸发-年水面蒸发特征值，" +
            "20100：水文站-冰情-冰情统计，20101：水文站-冰情-冰情摘录，20102：水文站-冰情-逐日水温序列，20103：水文站-冰情-月水温特征值表，" +
            "20104：水文站-冰情-年水温特征值表，20106：水文站-冰情-逐日水温年鉴，20107：水文站-颗粒级配-月平均悬移质颗粒级配表，20108：水文站-颗粒级配-实测悬移质颗粒级配表" +
            "20109：水文站-颗粒级配-实测悬移质单位水样颗粒级配表，20110：水文站-流量-逐日平均流量表序列，20111：水文站-流量-逐月流量特征表，20112：水文站-流量-年流量特征表" +
            "20113：水文站-流量-洪水水文要素摘录表，20114：水文站-流量-实测流量成果表，20115：水文站-流量-逐日平均流量表年鉴，20116：水文站-水位-逐日平均水位表" +
            "20117：水文站-水位-月水位特征值表，20118：水文站-水位-年水位特征值表，20119：水文站-水位-逐日平均水位表(年鉴)，20120：水文站-输沙率-逐日平均表(年鉴)" +
            "20121：水文站-输沙率-逐日平均表(序列)，20122：水文站-输沙率-月输沙率特征表，20123：水文站-输沙率-年输沙率特征表，20124：水文站-输沙率-输沙率成果表" +
            "20125：水文站-含沙量-逐日平均表(年鉴)，20126：水文站-含沙量-逐日平均表(序列)，20127：水文站-含沙量-月含沙量特征表，20128：水文站-含沙量-年含沙量特征表" +
            "30100：气象站-逐日气温表，30101：气象站-气象资料统计表"
            , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "请输入数据类型")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "照片列表")
    @NotEmpty(message = "请至少选择一张图片")
    private List<ImageInfo> imageList;

    @Schema(description = "管理后台 - 站点照片信息 Request VO")
    @Data
    public static class ImageInfo {

        @Schema(description = "照片名称")
        @NotBlank(message = "照片名称不能为空")
        private String imageName;

        @Schema(description = "照片路径")
        @NotBlank(message = "照片路径不能为空")
        private String imagePath;

    }

}