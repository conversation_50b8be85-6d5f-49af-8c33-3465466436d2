package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.graininfomonth.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 水文-月年平均悬移质颗粒级配分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GrainInfoMonthPageReqVO extends PageParam {

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11254")
    @NotNull(message = "站点id不能为空")
    private Long stationId;

    @Schema(description = "月")
    private String month;

    @Schema(description = "年")
    private String year;

    @Schema(description = "0.005粒径级（mm）")
    private String grain005;

    @Schema(description = "0.007粒径级（mm）")
    private String grain007;

    @Schema(description = "0.01粒径级（mm）")
    private String grain01;

    @Schema(description = "0.025粒径级（mm）")
    private String grain025;

    @Schema(description = "0.05粒径级（mm）")
    private String grain05;

    @Schema(description = "0.1粒径级（mm）")
    private String grain1;

    @Schema(description = "0.25粒径级（mm）")
    private String grain25;

    @Schema(description = "0.5粒径级（mm）")
    private String grain5;

    @Schema(description = "1.0粒径级（mm）")
    private String grain10;

    @Schema(description = "2.0粒径级（mm）")
    private String grain20;

    @Schema(description = "3.0粒径级（mm）")
    private String grain30;

    @Schema(description = "中数粒径(mm)", example = "24567")
    private String grainMid;

    @Schema(description = "平均粒径(mm)")
    private String grainAverage;

    @Schema(description = "最大粒径(mm)")
    private String grainMax;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "记录id")
    private Long logId;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}