package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedgrading.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 月年平均悬移质颗粒级配成果新增/修改 Request VO")
@Data
public class SuspendedGradingResultSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24141")
    private Long id;

    @Schema(description = "站点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16047")
    @NotEmpty(message = "站点ID不能为空")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "站点类型不能为空")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;

    @Schema(description = "年份(4位数字)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "年份(4位数字)不能为空")
    private Integer year;

    @Schema(description = "月份(1-12,0表示全年)")
    private Integer month;

    @Schema(description = "0.005mm粒径百分数")
    private String value0005;

    @Schema(description = "0.007mm粒径百分数")
    private String value0007;

    @Schema(description = "0.01mm粒径百分数")
    private String value001;

    @Schema(description = "0.025mm粒径百分数")
    private String value0025;

    @Schema(description = "0.05mm粒径百分数")
    private String value005;

    @Schema(description = "0.1mm粒径百分数")
    private String value01;

    @Schema(description = "0.25mm粒径百分数")
    private String value025;

    @Schema(description = "0.5mm粒径百分数")
    private String value05;

    @Schema(description = "1.0mm粒径百分数")
    private String value10;

    @Schema(description = "2.0mm粒径百分数")
    private String value20;

    @Schema(description = "3.0mm粒径百分数")
    private String value30;

    @Schema(description = "中数粒径(mm)")
    private String medianSize;

    @Schema(description = "平均粒径(mm)")
    private String meanSize;

    @Schema(description = "最大粒径(mm)")
    private String maxSize;

    @Schema(description = "是否为最新版本(1是,0否)")
    private Boolean latest;

    @Schema(description = "版本号")
    private Integer version;
    /**
     * 需要删除的记录ID列表
     */
    private List<Long> deleteIds;

    @Schema(description = "是否删除 true是 ")
    private Boolean isDelete;
}