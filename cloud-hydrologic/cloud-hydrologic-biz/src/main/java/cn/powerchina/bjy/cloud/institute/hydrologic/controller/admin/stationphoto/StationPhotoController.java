package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationphoto;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationphoto.vo.StationPhotoPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationphoto.vo.StationPhotoRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationphoto.vo.StationPhotoSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationphoto.vo.StationPhotoUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationphoto.StationPhotoDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationphoto.StationPhotoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 站点照片管理")
@RestController
@RequestMapping("/plan/hydrologic/station/photo")
@Validated
public class StationPhotoController {

    @Resource
    private StationPhotoService stationPhotoService;

    @PostMapping("/create")
    @Operation(summary = "创建站点照片管理")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-photo:create')")
    public CommonResult<Integer> createStationPhoto(@Valid @RequestBody StationPhotoSaveReqVO createReqVO) {
        return success(stationPhotoService.createStationPhoto(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新站点照片管理")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-photo:update')")
    public CommonResult<Boolean> updateStationPhoto(@Valid @RequestBody StationPhotoUpdateReqVO updateReqVO) {
        stationPhotoService.updateStationPhoto(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除站点照片管理")
    @Parameter(name = "id", description = "主键", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-photo:delete')")
    public CommonResult<Boolean> deleteStationPhoto(@RequestParam("id") Long id) {
        stationPhotoService.deleteStationPhoto(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得站点照片管理")
    @Parameter(name = "id", description = "主键", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-photo:query')")
    public CommonResult<StationPhotoRespVO> getStationPhoto(@RequestParam("id") Long id) {
        StationPhotoDO stationPhoto = stationPhotoService.getStationPhoto(id);
        return success(BeanUtils.toBean(stationPhoto, StationPhotoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得站点照片管理分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-photo:query')")
    public CommonResult<PageResult<StationPhotoRespVO>> getStationPhotoPage(@Valid StationPhotoPageReqVO pageReqVO) {
        PageResult<StationPhotoDO> pageResult = stationPhotoService.getStationPhotoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationPhotoRespVO.class));
    }

}