package cn.powerchina.bjy.cloud.institute.hydrologic.service.icestatistics;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics.vo.IceStatisticsPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics.vo.IceStatisticsRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics.vo.IceStatisticsSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.icestatistics.IceStatisticsDO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 冰情统计 Service 接口
 *
 * <AUTHOR>
 */
public interface IceStatisticsService {

    /**
     * 创建冰情统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createIceStatistics(@Valid IceStatisticsSaveReqVO createReqVO);

    /**
     * 更新冰情统计
     *
     * @param updateReqVO 更新信息
     */
    void updateIceStatistics(@Valid IceStatisticsSaveReqVO updateReqVO);

    /**
     * 删除冰情统计
     *
     * @param id 编号
     */
    void deleteIceStatistics(Long id);

    /**
     * 获得冰情统计
     *
     * @param id 编号
     * @return 冰情统计
     */
    IceStatisticsDO getIceStatistics(Long id);

    /**
     * 获得冰情统计分页
     *
     * @param pageReqVO 分页查询
     * @return 冰情统计分页
     */
    PageResult<IceStatisticsRespVO> getIceStatisticsPage(IceStatisticsPageReqVO pageReqVO);

    /**
     * 文件导入
     *
     * @param file        文件
     * @param stationId   站点id
     * @param stationType
     * @param dataType
     */
    void importData(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException;

    /**
     * 导出数据
     *
     * @param response
     * @param pageReqVO
     */
    void exportExcel(HttpServletResponse response, IceStatisticsPageReqVO pageReqVO);

    List<IceStatisticsDO> listByStationIds(List<Long> stationIds);

    void insertList(List<IceStatisticsDO> iceStatisticsDOS);

    IceStatisticsDO selectOne(Long stationId, Integer year);

    List<IceStatisticsDO> selectListTemporaryData(Long stationId);

    Integer getLatestVersion(Long stationId);

    void batchInsert(List<IceStatisticsDO> list, int batchSize);

    void updateBatchById(List<IceStatisticsDO> updateList, int batchSize);

    void deleteIceStatisticsData(IceStatisticsSaveReqVO updateReqVO);

    List<IceStatisticsDO> listCurrent(Long stationId);
}