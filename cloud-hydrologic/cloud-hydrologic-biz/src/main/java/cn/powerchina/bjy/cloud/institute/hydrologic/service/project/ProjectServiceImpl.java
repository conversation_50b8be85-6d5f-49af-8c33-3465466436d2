package cn.powerchina.bjy.cloud.institute.hydrologic.service.project;

import cn.hutool.core.util.BooleanUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.project.vo.ProjectPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.project.vo.ProjectRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.project.vo.ProjectSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filetree.FileTreeDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.project.ProjectMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.PlanningDesignConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.ProjectManageImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.filedocument.FileDocumentService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.filetree.FileTreeService;
import cn.powerchina.bjy.cloud.institute.hydrologic.task.AreaManager;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.PROJECT_NOT_EXISTS;

/**
 * 项目管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProjectServiceImpl implements ProjectService {

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private FileTreeService fileTreeService;

    @Autowired
    private FileDocumentService fileDocumentService;

    @Autowired
    private AreaManager areaManager;

    /** 参数校验 */
    private static final Map<String,String> map=new HashMap<>(30);

    static {
        map.put("powerStationName","电站名称");
        map.put("provinceCityCounty","省市区");
        map.put("address","详细地址");
        map.put("designStage","设计阶段");
        map.put("developWay","开发方式");
        map.put("longitude","经度");
        map.put("latitude","纬度");
        map.put("totalCapacity","总装机");
        map.put("storageCapacity","机组");
        map.put("ratedHead","额定水头");
        map.put("fullShippingHours","满发小时数");
        map.put("earthquakeIntensity","地震烈度");
        map.put("evaporation","蒸发（万m3）");
        map.put("leakage","渗漏(万m3)");
        map.put("waterLossReserveStorage","水损备用库(万m3)");
        map.put("frozenStorageCapacity","冰冻库容(万m3)");
        map.put("wheelRadius","转轮半径(m)");
        map.put("ratedSpeed","额定转速(r/min)");
        map.put("suckingHeight","吸出高度(m)");
        map.put("upAntiSeepageForm","上水库-防渗形式");
        map.put("upMaximumDamHeight","上水库-最大坝高");
        map.put("upNormalWaterStorageLevel","上水库-正常蓄水位");
        map.put("upDeadWaterLevel","上水库-死水位");
        map.put("upDeadStorageCapacity","上水库-死库容");
        map.put("upDamLength","上水库-坝长");
        map.put("downAntiSeepageForm","下水库-防渗形式");
        map.put("downMaximumDamHeight","下水库-最大坝高");
        map.put("downNormalWaterStorageLevel","下水库-正常蓄水位");
        map.put("downDeadWaterLevel","下水库-死水位");
        map.put("downDeadStorageCapacity","下水库-死库容");
        map.put("downDamLength","下水库-坝长");
        map.put("adjustingStorageCapacity","调节库容");
        map.put("lengthOfWaterConveyanceSystem","输水系统长度");
        map.put("distanceToHeightRatio","距高比");
        map.put("duration","工期(月)");
        map.put("engineeringInvestment","工程投资(亿元)");
        map.put("staticInvestment","单位千瓦静态投资(元/kW)");
        map.put("coalSavingAmount","节煤量(万t)");
        map.put("economicInternalRateOfReturn","经济内部收益率");
        map.put("capitalRatio","资本金占比");
        map.put("capacityPayment","容量电价(含税)");
        map.put("annualCapacityElectricityFee","年容量电费(万元)");
        map.put("underConstructionCapacity","在建容量(MW)");
        map.put("approvalTime","核准时间");
        map.put("approvalAuthority","核准机关");
        map.put("startTime","开工时间");
        map.put("builtCapacity","建成容量(MW)");
        map.put("firstProductionTime","首台机组投产时间");
        map.put("allProductionTime","全部机组投产时间");
    }



    @Override
    public Long createProject(ProjectSaveReqVO createReqVO) {
        ProjectDO stationDO = findProjectByPowerStationName(createReqVO.getPowerStationName());
        if (Objects.nonNull(stationDO)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_NAME_SAVE_EXISTS_ERROR);
        }
        // 插入
        ProjectDO project = BeanUtils.toBean(createReqVO, ProjectDO.class);
        projectMapper.insert(project);
        //插入文件树
        fileTreeService.addFileTree(project.getId(), project.getPowerStationName());
        // 返回
        return project.getId();
    }

    @Override
    public void updateProject(ProjectSaveReqVO updateReqVO) {
        // 校验存在
        validateProjectExists(updateReqVO.getId());
        ProjectDO stationDO = findProjectByPowerStationName(updateReqVO.getPowerStationName());
        if (Objects.nonNull(stationDO) && !Objects.equals(stationDO.getId(), updateReqVO.getId())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_NAME_SAVE_EXISTS_ERROR);
        }
        // 更新
        ProjectDO updateObj = BeanUtils.toBean(updateReqVO, ProjectDO.class);
        projectMapper.updateById(updateObj);
        //更新文件树
        fileTreeService.modifyFileTreeNameByProjectId(updateObj.getId(), updateObj.getPowerStationName());
    }

    @Override
    public void deleteProject(Long id) {
        // 校验存在
        validateProjectExists(id);
        //校验是否存在文件资料
        FileTreeDO fileTreeDO = fileTreeService.selectFirstLevelFileTreeDO(id,"1");
        if (Objects.nonNull(fileTreeDO) && !CollectionUtils.isEmpty(fileDocumentService.findFileDocumentDOByTreeId(fileTreeDO.getId()))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_DELETE_EXISTS_ERROR);
        }
        // 删除
        projectMapper.deleteById(id);
        //删除一级子树
        fileTreeService.deleteFileTreeByProjectId(id);
    }

    private void validateProjectExists(Long id) {
        if (projectMapper.selectById(id) == null) {
            throw exception(PROJECT_NOT_EXISTS);
        }
    }

    @Override
    public ProjectRespVO getProject(Long id) {
        ProjectDO projectDO = projectMapper.selectById(id);
        ProjectRespVO projectRespVO = BeanUtils.toBean(projectDO, ProjectRespVO.class);
        projectRespVO.setProvinceName(areaManager.getProvinceNameByCode(projectDO.getProvince()));
        if (StringUtils.isBlank(projectDO.getProvince())) {
            throw exception(ErrorCodeConstants.PARAM_ERROR, "省");
        }
        projectRespVO.setCityName(areaManager.getCityNameByCode(projectDO.getCity()));
        if (StringUtils.isBlank(projectDO.getCity())) {
            projectRespVO.setCityName(null);
        }
        if (StringUtils.isNotBlank(projectDO.getCounty())) {
            projectRespVO.setCountyName(areaManager.getAreaNameByCode(projectDO.getCounty()));
            if (StringUtils.isBlank(projectDO.getCounty())) {
                projectRespVO.setCountyName(null);
            }
        }
        return projectRespVO;
    }

    @Override
    public PageResult<ProjectRespVO> getProjectPage(ProjectPageReqVO pageReqVO) {
        PageResult<ProjectDO> doPageResult = projectMapper.selectPage(pageReqVO);
        if (null == doPageResult || org.apache.commons.collections4.CollectionUtils.isEmpty(doPageResult.getList())) {
            return PageResult.empty();
        }
        PageResult<ProjectRespVO> result = PageResult.empty();
        List<ProjectRespVO> data = new ArrayList<>(doPageResult.getList().size());
        result.setTotal(doPageResult.getTotal());
        doPageResult.getList().forEach(item -> {
            ProjectRespVO vo = BeanUtils.toBean(item, ProjectRespVO.class);
            vo.setProvince(areaManager.getProvinceNameByCode(item.getProvince()));
            vo.setCity(areaManager.getCityNameByCode(item.getCity()));
            vo.setCounty(areaManager.getAreaNameByCode(item.getCounty()));
            data.add(vo);
        });
        result.setList(data);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importProject(MultipartFile file) {
        //增加分布式锁，只能有一个导入
        if (!redisTemplate.opsForValue().setIfAbsent(PlanningDesignConstants.PROJECT_MANAGE_IMPORT_KEY, 1, 60, TimeUnit.SECONDS)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EXISTS_ERROR);
        }
        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }
            List<ProjectManageImportModel> importDataList = EasyExcel.read(file.getInputStream(), ProjectManageImportModel.class, null).sheet().headRowNumber(PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE).doReadSync();
            if (CollectionUtils.isEmpty(importDataList)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
            }
            //校验数据
            List<ProjectDO> projectDOList = validateProjectManageExistsImport(importDataList);
            List<FileTreeDO> treeDOList = new ArrayList<>();
            projectDOList.forEach(item -> {
                projectMapper.insert(item);
                FileTreeDO treeDO = new FileTreeDO();
                treeDO.setTreeLevel(1);
                treeDO.setProjectId(item.getId());
                treeDO.setTreeName(item.getPowerStationName());
                treeDOList.add(treeDO);
            });
            //插入文件树
            fileTreeService.insertBatch(treeDOList,"1");
//            projectMapper.insertBatch(projectDOList);

        } catch (IOException e) {
            log.error("importRainfallStation--->error.", e);
        } finally {
            redisTemplate.delete(PlanningDesignConstants.PROJECT_MANAGE_IMPORT_KEY);
        }
    }

    @Override
    public ProjectDO findProjectByPowerStationCode(String powerStationCode) {
        return projectMapper.selectOne("powerstation_code", powerStationCode);
    }

    //站名编码
    @Override
    public ProjectDO findProjectByPowerStationName(String powerStationName) {
        QueryWrapper<ProjectDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("power_station_name", powerStationName);
        return projectMapper.selectOne(queryWrapper,false);
    }

    @Override
    public List<ProjectDO> findAllProject() {
        return projectMapper.selectAllProject();
    }

    @Override
    public List<ProjectDO> listProjectByIds(List<Long> projectIdList) {
        return projectMapper.selectBatchIds(projectIdList);
    }

    @Override
    public List<ProjectDO> selectListByStationNames(List<String> stationNames) {
        return projectMapper.selectListByStationNames(stationNames);
    }

    @Override
    public void batchInsert(List<ProjectDO> projectDOList) {
        projectMapper.insertList(projectDOList);
    }

    @Override
    public void deleteTemporaryData() {
        projectMapper.deleteTemporaryData();
    }

    @Override
    public List<ProjectDO> selectListTemporaryData() {
        return projectMapper.selectListTemporaryData();
    }

    @Override
    public ProjectDO selectOneByName(String powerStationName) {
        QueryWrapper<ProjectDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("power_station_name",powerStationName);
        return projectMapper.selectOne(queryWrapper,false);
    }

    @Override
    public void updateProject(ProjectDO projectDO) {
        // 校验存在
        validateProjectExists(projectDO.getId());
        ProjectDO stationDO = findProjectByPowerStationName(projectDO.getPowerStationName());
        if (Objects.nonNull(stationDO) && !Objects.equals(stationDO.getId(), projectDO.getId())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_NAME_SAVE_EXISTS_ERROR);
        }
        // 更新
        projectMapper.updateById(projectDO);
    }

    @Override
    public int insert(ProjectDO projectDO) {
        return projectMapper.insert(projectDO);
    }

    @Override
    public void updateCreator() {
        projectMapper.updateCreator();
    }

    /**
     * 校验导入的项目编码是否存在
     *
     * @param importDataList
     */
    private List<ProjectDO> validateProjectManageExistsImport(List<ProjectManageImportModel> importDataList) {
        if (CollectionUtils.isEmpty(importDataList)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
        }

        /** 省市县间需用"/"隔开，如:河南省/郑州市/金水区，其中省为必填详细位置为区后的详细信息，内容为选填且最多录入30字;
         * 东经的格式为:113.13213221，导入最高输入小数点后9位，超出部分不寻入;
         * 北纬的格式为:34.13213221，最高输入小数点后9位，超出部分不
         * 表头标绿的部分为必填项。
         *，
         *         且需保证省市县文字正确，否则会导入失败
         */

        List<ProjectDO> projectDOList = new ArrayList<>();  // 入库数据集合
        List<String> powerStationNameList = new ArrayList<>();
        for (int i = 0; i < importDataList.size(); i++) {
            ProjectManageImportModel importModel = importDataList.get(i);

            //电站名称
            if (StringUtils.isBlank(importModel.getPowerStationName())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_STATION_NAME_SYSTEM_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
            }
            //校验站名重复
            if (powerStationNameList.contains(importModel.getPowerStationName())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_STATION_REPEAT_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
            }
            //校验站名存在
            if (Objects.nonNull(findProjectByPowerStationName(importModel.getPowerStationName()))) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_NAME_EXISTS_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
            }
            //校验电站地点
            if (StringUtils.isBlank(importModel.getProvince())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_PROVINCE_EMPTY_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
            }
//            String[] provinceCityCounty = importModel.getProvinceCityCounty().split("/");
            //处理省市县
            String provinceName = importModel.getProvince();
            String cityName = importModel.getCity();
            String countyName = StringUtils.isNotBlank(importModel.getCounty()) ? importModel.getCounty() : null;
            String province = areaManager.getProvinceCodeByName(provinceName);
            String city = areaManager.getCityCodeByName(provinceName, cityName);
            String county = areaManager.getAreaCodeByName(countyName);
            if (StringUtils.isBlank(province)){
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_PROVINCE_MATCH_ERROR,i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1,provinceName);
            }
            if (StringUtils.isNotBlank(cityName) && (StringUtils.isBlank(city) || !areaManager.checkProvinceCityExists(province, city))) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_PROVINCECITY_MATCH_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1, provinceName, cityName);
            }
            if (StringUtils.isNotBlank(countyName) && (StringUtils.isBlank(county) || !areaManager.checkCityAreaExists(city, county))) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_PROVINCECITYCOUNTY_MATCH_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1, provinceName, cityName, countyName);
            }
            // 详细地址的长度
            if (StringUtils.isNotBlank(importModel.getAddress()) && importModel.getAddress().length() > 30) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_DETAILED_LOCATION_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
            }
            //检验设计阶段
            if (StringUtils.isBlank(importModel.getDesignStage())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DESIGN_SYSTEM_EMPTY_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
            }
            if (importModel.getDesignStage().length()>10) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DESIGN_LENGTH_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
            }
            //检验开发方式
            if (StringUtils.isBlank(importModel.getDevelopWay())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_DEVELOP_WAY_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
            }
            if (importModel.getDevelopWay().length() > 10) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_DEVELOP_WAY_LENGTH_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
            }
            //校验东经
            if (StringUtils.isBlank(importModel.getLongitude())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_LONGITUDE_EMPTY_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
            }
            importModel.setLongitude(importModel.getLongitude().replace("°", ".").replace("'", ""));
            //校验北纬
            if (StringUtils.isBlank(importModel.getLatitude())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_LATITUDE_EMPTY_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
            }
            importModel.setLatitude(importModel.getLatitude().replace("°", ".").replace("'", ""));
            //总装机容量
            if (StringUtils.isBlank(importModel.getTotalCapacity())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_TOTAL_CAPACITY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
            }
            //库容
            if (StringUtils.isBlank(importModel.getStorageCapacity())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_STOTAGE_CAPACITY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
            }
            //额定水头
            if (StringUtils.isBlank(importModel.getRatedHead())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_RATED_HEAD_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
            }
            //满发小时数
            if (StringUtils.isBlank(importModel.getFullShippingHours())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_FULL_SHIPPING_HOURS_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
            }
            //字符长度校验
            int limit=30;
            Object[] objects = cn.powerchina.bjy.cloud.institute.hydrologic.util.StringUtils.areAllPropertiesWithinLimit(importModel, limit, map);
            if(BooleanUtil.isFalse((Boolean) objects[0])){
                throw exception(ErrorCodeConstants.LENGTH_EXCEEDS_LIMIT, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1,objects[1],limit);
            }

            ProjectDO projectDO = new ProjectDO();
            org.springframework.beans.BeanUtils.copyProperties(importDataList.get(i), projectDO);
            projectDO.setProvince(province);
            projectDO.setCity(city);
            projectDO.setCounty(county);
            powerStationNameList.add(importDataList.get(i).getPowerStationName());
            projectDOList.add(projectDO);
        }
        return projectDOList;
    }
}