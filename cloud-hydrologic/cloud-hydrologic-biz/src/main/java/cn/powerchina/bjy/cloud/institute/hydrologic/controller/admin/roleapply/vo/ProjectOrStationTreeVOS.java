package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.roleapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "管理后台 - 站点")
public class ProjectOrStationTreeVOS {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "站点名称")
    private String stationName;

    @Schema(description = "绑定类型（1：项目；2：气象站；3：雨量站；4：水文站）")
    private Integer bindType;

    @Schema(description = "code")
    private Long code;

    @Schema(description = "子节点")
    private List<ProjectOrStationTreeVO> children;


    @Data
    @Schema(description = "管理后台 - 站点")
    public static class ProjectOrStationTreeVO {

        @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long id;

        @Schema(description = "站点名称")
        private String stationName;

        @Schema(description = "code")
        private Long code;

        @Schema(description = "子节点")
        private List<ProjectOrStationTreeVO> children;

    }
}