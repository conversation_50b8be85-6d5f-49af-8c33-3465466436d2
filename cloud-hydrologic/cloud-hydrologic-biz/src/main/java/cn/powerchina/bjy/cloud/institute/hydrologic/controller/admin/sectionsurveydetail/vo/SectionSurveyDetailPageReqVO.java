package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveydetail.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 实测大断面成果垂线明细数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SectionSurveyDetailPageReqVO extends PageParam {

    @Schema(description = "水文站id", example = "8778")
    private Long stationId;

    @Schema(description = "站点类型", example = "2")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "施测年份")
    private Integer year;

    @Schema(description = "汛期类型：1-汛前，2-汛后", example = "1")
    private Integer periodType;

    @Schema(description = "垂线号，不大于4个字")
    private String verticalNo;

    @Schema(description = "起点距，5位整数+1位小数")
    private BigDecimal startDistance;

    @Schema(description = "河底高程，4位整数+2位小数")
    private BigDecimal riverbedElevation;

    @Schema(description = "备注/附注", example = "你猜")
    private String remark;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "是否最新（1最新0历史）")
    private Integer latest;

    @Schema(description = "检索记录判断", example = "1")
    private Long indexed;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}