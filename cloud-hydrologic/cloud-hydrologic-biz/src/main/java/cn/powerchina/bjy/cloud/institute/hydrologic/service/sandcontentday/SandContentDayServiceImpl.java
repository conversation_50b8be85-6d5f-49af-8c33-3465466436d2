package cn.powerchina.bjy.cloud.institute.hydrologic.service.sandcontentday;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentday.vo.SandContentDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentday.vo.SandContentDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentday.vo.SandContentDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sandcontentday.SandContentDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sandcontentmonth.SandContentMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sandcontentyear.SandContentYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.hydrologicstation.HydrologicStationMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.sandcontentday.SandContentDayMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.*;

import cn.powerchina.bjy.cloud.institute.hydrologic.service.BaseExcelProcessorAdapter;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.facade.SandFacade;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.DATA_REPEAT;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.SAND_CONTENT_DAY_NOT_EXISTS;
/**
 * 水文站—输沙率逐日平均含沙量 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SandContentDayServiceImpl extends BaseExcelProcessorAdapter<SandContentDayDO, SandContentDayImportModel> implements SandContentDayService {

    @Resource
    private SandFacade facade;
    @Resource
    private SandContentDayMapper sandContentDayMapper;
    @Resource
    private HydrologicStationMapper hydrologicStationMapper;

    @Override
    public Long createSandContentDay(SandContentDaySaveReqVO createReqVO) {
        // 插入
        SandContentDayDO sandContentDay = BeanUtils.toBean(createReqVO, SandContentDayDO.class);
        sandContentDayMapper.insert(sandContentDay);
        // 返回
        return sandContentDay.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSandContentDay(SandContentDaySaveReqVO updateReqVO) {
        if (null == updateReqVO || null == updateReqVO.getStationId() || CollectionUtils.isEmpty(updateReqVO.getDataList())) {
            return;
        }
        List<SandContentDayDO> updateDoList = new ArrayList<>(updateReqVO.getDataList().size());
        Set<Long> ids = new HashSet<>(updateReqVO.getDataList().size());
        updateReqVO.getDataList().forEach(item -> {
            SandContentDayDO updateDO = BeanUtils.toBean(item, SandContentDayDO.class);
            updateDoList.add(updateDO);
            ids.add(item.getId());
        });
        validateDosExists(ids, SAND_CONTENT_DAY_NOT_EXISTS);
        try {
            EXCEL.set(updateReqVO.getStationId());
            dataProcess(updateDoList, ShowTypeEnum.LIST);
            sandContentDayMapper.insertBatch(updateDoList);
        } finally {
            EXCEL.remove();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modifyYearBookList(Long stationId, List<YearForm> dataMode) {
        if (CollectionUtils.isEmpty(dataMode)) {
            return;
        }
        // 查询站点是否存在
        if (Objects.isNull(stationId) || Objects.isNull(hydrologicStationMapper.selectById(stationId))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        // 增加分布式锁，只能有一个导入,针对站点增加锁，站点的所有类型，只能有一个导入,页面新增时也要增加锁控制
        String lockKey = String.format(PlanningDesignConstants.HYDROLOGIC_SAND_DAY_IMPORT_KEY, stationId);
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, 1, 60, TimeUnit.SECONDS))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EXISTS_ERROR);
        }
        try {
            //数据处理
            EXCEL.set(stationId);
            processYearModel(dataMode, 0, 2, 0);
        } finally {
            EXCEL.remove();
            redisTemplate.delete(lockKey);
        }
    }

    @Override
    public void deleteSandContentDay(Long id) {
        // 校验存在
        validateSandContentDayExists(id);
        // 删除
        sandContentDayMapper.deleteById(id);
    }

    private void validateSandContentDayExists(Long id) {
        if (sandContentDayMapper.selectById(id) == null) {
            throw exception(SAND_CONTENT_DAY_NOT_EXISTS);
        }
    }

    @Override
    public SandContentDayDO getSandContentDay(Long id) {
        return sandContentDayMapper.selectById(id);
    }

    @Override
    public PageResult<SandContentDayRespVO> getSandContentDayPage(SandContentDayPageReqVO pageReqVO) {
        // 查询最新的
        StationDataLogDO dataLogDO;
        if (Objects.nonNull(pageReqVO.getLogId())) {
            dataLogDO = stationDataLogService.findById(pageReqVO.getLogId());
            if (Objects.isNull(dataLogDO)) {
                return PageResult.empty();
            }
            pageReqVO.setVersion(dataLogDO.getCurrentVersion());
            pageReqVO.setLatest(null);
        } else {
            pageReqVO.setLatest(LatestEnum.LATEST.getType());
            pageReqVO.setVersion(null);
        }
        return BeanUtils.toBean(sandContentDayMapper.selectPage(pageReqVO), SandContentDayRespVO.class);
    }

    @Override
    public List<YearForm> getYearBookList(Long stationId, Long logId, String year) {
        if (null == stationId) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        Integer stationType = StationEnum.HYDROLOGIC.getType();
        // 查询最新的
        StationDataLogDO dataLogDO;
        if (StringUtils.isNotBlank(year)) {
            dataLogDO = stationDataLogService.findStationDataLogDOLatestByStationTypeAndYear(stationId, stationType, StationDataTypeEnum.HYDROLOGIC_STATION_SAND_BOOK.getType(), year);
        } else if (Objects.isNull(logId)) {
            dataLogDO = stationDataLogService.findStationDataLogDOLatestByStationType(stationId, stationType, StationDataTypeEnum.HYDROLOGIC_STATION_SAND_BOOK.getType());
        } else {
            dataLogDO = stationDataLogService.findById(logId);
            if (Objects.nonNull(dataLogDO) && !Objects.equals(stationType, dataLogDO.getStationType())) {
                dataLogDO = null;
            }
        }
        if (Objects.isNull(dataLogDO)) {
            return new ArrayList<>();
        }
        // 获取日序列数据
        List<SandContentDayDO> dayDOList = findDOByVersionAndYear(stationId, dataLogDO.getYear(), dataLogDO.getDayVersion());
        Map<Integer, List<SandContentDayDO>> dayMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dayDOList)) {
            dayMap.putAll(dayDOList.stream().collect(Collectors.groupingBy(SandContentDayDO::getDay, Collectors.mapping(Function.identity(), Collectors.toList()))));
        }
        // 获取年数据
        SandContentYearDO yearDO = facade.findYearDOByVersionAndYear(stationId, dataLogDO.getYear(), dataLogDO.getYearVersion());
        // 获取月数据
        Map<Integer, SandContentMonthDO> monthDOMap = new HashMap<>();
        List<SandContentMonthDO> monthDOList = facade.findMonthDOByVersionAndYear(stationId, dataLogDO.getYear(), dataLogDO.getMonthVersion());
        if (CollectionUtils.isNotEmpty(monthDOList)) {
            monthDOMap.putAll(monthDOList.stream().collect(Collectors.toMap(SandContentMonthDO::getMonth, Function.identity())));
        }
        // 组装数据
        List<YearForm> yearModelList = new ArrayList<>();
        generateFrontDataHead(dataLogDO.getYear(), yearModelList);
        // 插入日序列数据
        for (int i = 1; i < 32; i++) {
            yearModelList.add(generateFrontDataDay(i, dayMap.get(i)));
        }
        // 月数据填充
        generateFrontMonthData(yearModelList, monthDOMap);
        // 年数据填充
        generateFrontYearData(yearModelList, yearDO);
        // 填充附注
        generateFrontYearRemark(yearModelList, dataLogDO);
        return yearModelList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importData(MultipartFile file, Long stationId, Integer type) throws IOException {
        EXCEL.set(stationId);
        String lockKey = String.format(PlanningDesignConstants.HYDROLOGIC_SAND_DAY_IMPORT_KEY, stationId);

        if (ShowTypeEnum.LIST.getType().equals(type)) {
            importData(file, lockKey, 1, null);
        } else {
            importYearData(file, lockKey, 0, 2, 0);
        }
    }

    @Override
    public Integer getLatestVersion(Long stationId) {
        return sandContentDayMapper.getLatestVersion(stationId);
    }

    @Override
    public List<SandContentDayDO> findDOByVersionAndYear(Long stationId, String year, Integer version) {
        QueryWrapper<SandContentDayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("station_id", stationId);
        queryWrapper.eq("year", year);
        queryWrapper.eq("version", version);
        queryWrapper.orderByAsc("month");
        return sandContentDayMapper.selectList(queryWrapper);
    }

    @Override
    public void exportExcel(HttpServletResponse response, SandContentDayPageReqVO pageReqVO) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(StationDataTypeEnum.HYDROLOGIC_STATION_SAND_DAY.getDesc() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            PageResult<SandContentDayRespVO> pageResult = getSandContentDayPage(pageReqVO);
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + StationDataTypeEnum.HYDROLOGIC_STATION_SAND_DAY.getType() + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream())
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            excelWriter.fill(pageResult.getList(), writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("exportExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }

    @Override
    public void exportBookExcel(HttpServletResponse response, Long stationId, Long logId, String year) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(StationDataTypeEnum.HYDROLOGIC_STATION_SAND_BOOK.getDesc() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            List<YearForm> yearModelList = getYearBookList(stationId, logId, year);
            if (org.springframework.util.CollectionUtils.isEmpty(yearModelList)) {
                log.error("exportDischargeDayBookExcel--->没有数据，不导出。stationId={},logId={},year={}", stationId, logId, year);
                return;
            }
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + StationDataTypeEnum.HYDROLOGIC_STATION_SAND_BOOK.getType() + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream()) // 利用模板的输出流
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, yearModelList.get(0).getValue1().replace("年", "")).build();
            excelWriter.fill(yearModelList.subList(2, 38), writeSheet);
            Map<String, String> otherMap = new HashMap<>();
            otherMap.put("otherYear", yearModelList.get(0).getValue1());
            writeSheet.setSheetName(otherMap.get("otherYear"));
            otherMap.put("otherAverageFlow", yearModelList.get(38).getValue3());
            otherMap.put("otherAverageDischargeRate", yearModelList.get(38).getValue7());
            otherMap.put("otherAverageSandContent", yearModelList.get(38).getValue11());

            otherMap.put("maxSectionAverage", yearModelList.get(39).getValue3());
            otherMap.put("maxSectionAverageDate", yearModelList.get(39).getValue5());
            otherMap.put("minSectionAverage", yearModelList.get(39).getValue9());
            otherMap.put("minSectionAverageDate", yearModelList.get(39).getValue11());

            otherMap.put("otherRemark", yearModelList.get(40).getValue1());
            excelWriter.fill(otherMap, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("exportBookExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }

    @Override
    protected BaseMapperX<SandContentDayDO> getMapper() {
        return sandContentDayMapper;
    }

    @Override
    protected void dataProcess(List<SandContentDayDO> sandContentDayDOS, ShowTypeEnum source) {
        if (CollectionUtils.isEmpty(sandContentDayDOS)) {
            return;
        }
        Set<String> currentIdentify = new HashSet<>(sandContentDayDOS.size());
        List<Integer> years = new ArrayList<>();
        // 默认版本为0
        for (int i = 0; i < sandContentDayDOS.size(); i++) {
            SandContentDayDO item = sandContentDayDOS.get(i);
            // 校验日期
            if (source == ShowTypeEnum.LIST && item.getId() == null) {
                validateFull(item.getYear(), item.getMonth(), item.getDay(), i + 2);
            } else if (source == ShowTypeEnum.LIST) {
                validateFull(item.getYear(), item.getMonth(), item.getDay());
            }
            if (!years.contains(item.getYear())) {
                years.add(item.getYear());
            }
            // 判断为更新接口用
            if (null == item.getStationId()) {
                item.setStationId(EXCEL.get());
            }
            String uniKey = item.getYear() + "-" + item.getMonth() + "-" + item.getDay();
            item.setId(null);
            item.setCurrentDay(DateUtils.parseDate(uniKey, "yyyy-MM-dd"));
            item.setVersion(0);
            item.setLatest(LatestEnum.LATEST.getType());
            if (!currentIdentify.add(uniKey)) {
                if (source == ShowTypeEnum.LIST && item.getId() == null) {
                    throw exception(DATA_REPEAT, "第" + (i + 2) + "行");
                } else if (source == ShowTypeEnum.LIST) {
                    throw exception(DATA_REPEAT, uniKey);
                }
            }
        }
        sandContentDayDOS.removeIf(item -> !Objects.equals(item.getMonth(), DateUtils.getYearAndMonth(item.getCurrentDay(), "yyyy-MM-dd")));
        // 没有历史数据则不处理
        List<SandContentDayDO> beforeData = getBeforeData(SandContentDayDO::getStationId, SandContentDayDO::getLatest, SandContentDayDO::getDeleted);
        if (CollectionUtils.isEmpty(beforeData)) {
            if (source == ShowTypeEnum.LIST) {
                saveDataLog(years, 0);
            }
            return;
        }
        // 赋默认值，避免全量导入时无法正常赋值情况
        AtomicInteger oldVersion = new AtomicInteger(beforeData.get(0).getVersion());
        // 过滤掉本次新增的数据，并加入入库集合中
        beforeData.stream().filter(item -> !currentIdentify.contains(item.getYear() + "-" + item.getMonth() + "-" + item.getDay())).forEach(oldData -> {
            oldVersion.set(oldData.getVersion());
            oldData.setId(null);
            oldData.setLatest(LatestEnum.LATEST.getType());
            sandContentDayDOS.add(oldData);
        });
        // 将版本号加一
        sandContentDayDOS.forEach(item -> {
            item.setId(null);
            item.setCurrentDay(DateUtils.parseDate(item.getYear() + "-" + item.getMonth() + "-" + item.getDay(), "yyyy-MM-dd"));
            item.setLatest(LatestEnum.LATEST.getType());
            item.setVersion(oldVersion.get() + 1);
        });

        // 更新历史数据
        UpdateWrapper<SandContentDayDO> updateWrapper = getUpdateWrapper(
                SandContentDayDO::getStationId, SandContentDayDO::getLatest, SandContentDayDO::getVersion,
                SandContentDayDO::getDeleted, oldVersion.get());
        SandContentDayDO entity = new SandContentDayDO();
        entity.setLatest(LatestEnum.HISTORY.getType());
        sandContentDayMapper.update(entity, updateWrapper);
        if (source == ShowTypeEnum.LIST) {
            // 保存操作记录
            saveDataLog(years, oldVersion.get() + 1);
        }

    }

    private void saveDataLog(List<Integer> years, Integer version) {
        if (CollectionUtils.isEmpty(years)) {
            return;
        }
        // 插入变更记录
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get())
                .currentVersion(version).dataType(StationDataTypeEnum.HYDROLOGIC_STATION_SAND_DAY.getType()).build());
        // 获取年鉴最新版本
        Integer yearMarkCurrentVersion = stationDataLogService.findStationDataLogDOLatestCurrentVersionByStationType(EXCEL.get(), StationEnum.HYDROLOGIC.getType(), StationDataTypeEnum.HYDROLOGIC_STATION_SAND_BOOK.getType());
        // 获取年版本
        Integer yearVersion = facade.getYearLatestVersion(EXCEL.get());
        // 获取月版本
        Integer monthVersion = facade.getMonthLatestVersion(EXCEL.get());
        years.sort(Comparator.comparing(Integer::valueOf));
        for (int i = 0; i < years.size(); i++) {
            Integer item = years.get(i);
            // 生成年鉴记录、日操作记录
            stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().year(String.valueOf(item))
                    .stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get()).currentVersion(yearMarkCurrentVersion + 1 + i)
                    .dataType(StationDataTypeEnum.HYDROLOGIC_STATION_SAND_BOOK.getType()).dayVersion(version).monthVersion(monthVersion).yearVersion(yearVersion).build());
        }
    }

    @Override
    protected void processOtherData(String year, String yearRemark, List<YearForm> excelData) {
        List<SandContentMonthImportModel> monthImportModels = generateMonthList(year, excelData, 0);
        facade.processImportMonthModel(monthImportModels);

        SandContentYearImportModel yearImportModel = generateYearModel(year, excelData);
        facade.processImportYearModel(Lists.newArrayList(yearImportModel));

        Integer dayVersion = getLatestVersion(EXCEL.get());
        // 插入日变更记录
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get())
                .currentVersion(dayVersion).dataType(StationDataTypeEnum.HYDROLOGIC_STATION_SAND_DAY.getType()).build());
        Integer monthVersion = facade.getMonthLatestVersion(EXCEL.get());
        // 插入月变更记录
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get())
                .currentVersion(monthVersion).dataType(StationDataTypeEnum.HYDROLOGIC_STATION_SAND_MONTH.getType()).build());
        Integer yearVersion = facade.getYearLatestVersion(EXCEL.get());
        // 插入年变更记录
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get())
                .currentVersion(yearVersion).dataType(StationDataTypeEnum.HYDROLOGIC_STATION_SAND_YEAR.getType()).build());
        // 生成年鉴记录
        Integer currentVersion = stationDataLogService.findStationDataLogDOLatestCurrentVersionByStationType(EXCEL.get(), StationEnum.HYDROLOGIC.getType(), StationDataTypeEnum.HYDROLOGIC_STATION_SAND_BOOK.getType());
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(StationEnum.HYDROLOGIC.getType()).stationId(EXCEL.get())
                .year(year).yearRemark(yearRemark).currentVersion(currentVersion + 1)
                .dataType(StationDataTypeEnum.HYDROLOGIC_STATION_SAND_BOOK.getType()).dayVersion(dayVersion).monthVersion(monthVersion)
                .yearVersion(yearVersion).build());
    }

    private List<SandContentMonthImportModel> generateMonthList(String year, List<YearForm> importDataList, int headRemarkLine) {
        Map<String, SandContentMonthImportModel> monthModelMap = new HashMap<>();
        for (int i = 33 + headRemarkLine; i < 38 + headRemarkLine && i < importDataList.size(); i++) {
            YearForm importModel = importDataList.get(i);
            if (importModel == null) {
                continue;
            }
            fillMonthModel(monthModelMap, year, 1 + "", importModel.getValue1(), i);
            fillMonthModel(monthModelMap, year, 2 + "", importModel.getValue2(), i);
            fillMonthModel(monthModelMap, year, 3 + "", importModel.getValue3(), i);
            fillMonthModel(monthModelMap, year, 4 + "", importModel.getValue4(), i);
            fillMonthModel(monthModelMap, year, 5 + "", importModel.getValue5(), i);
            fillMonthModel(monthModelMap, year, 6 + "", importModel.getValue6(), i);
            fillMonthModel(monthModelMap, year, 7 + "", importModel.getValue7(), i);
            fillMonthModel(monthModelMap, year, 8 + "", importModel.getValue8(), i);
            fillMonthModel(monthModelMap, year, 9 + "", importModel.getValue9(), i);
            fillMonthModel(monthModelMap, year, 10 + "", importModel.getValue10(), i);
            fillMonthModel(monthModelMap, year, 11 + "", importModel.getValue11(), i);
            fillMonthModel(monthModelMap, year, 12 + "", importModel.getValue12(), i);
        }
        return new ArrayList<>(monthModelMap.values());
    }

    private void fillMonthModel(Map<String, SandContentMonthImportModel> monthImportModelMap, String year, String month, String value, int lineNumber) {
        SandContentMonthImportModel monthImportModel = monthImportModelMap.get(month);
        if (Objects.isNull(monthImportModel)) {
            monthImportModel = new SandContentMonthImportModel();
        }
        monthImportModel.setYear(year);
        monthImportModel.setMonth(month);
        if (lineNumber == 33) {
            monthImportModel.setAverageValue(value);
        }
        if (lineNumber == 34) {
            monthImportModel.setMaxValue(value);
        }
        if (lineNumber == 35) {
            monthImportModel.setMaxValueDate(value);
        }
        if (lineNumber == 36) {
            monthImportModel.setMinValue(value);
        }
        if (lineNumber == 37) {
            monthImportModel.setMinValueDate(value);
        }
        monthImportModelMap.put(month, monthImportModel);
    }

    private SandContentYearImportModel generateYearModel(String year, List<YearForm> importDataList) {
        SandContentYearImportModel yearImportModel = new SandContentYearImportModel();
        YearForm yearData1 = importDataList.get(38);
        YearForm yearData2 = importDataList.get(39);
        YearForm yearData3 = importDataList.get(40);
        yearImportModel.setYear(Integer.valueOf(year));
        yearImportModel.setAverageFlow(yearData1.getValue3());
        yearImportModel.setAverageDischargeRate(yearData1.getValue7());
        yearImportModel.setAverageSandContent(yearData1.getValue11());

        yearImportModel.setMaxSectionAverage(yearData2.getValue3());
        yearImportModel.setMaxSectionAverageDate(yearData2.getValue5());
        yearImportModel.setMinSectionAverage(yearData2.getValue9());
        yearImportModel.setMinSectionAverageDate(yearData2.getValue11());

        yearImportModel.setRemark(yearData3.getValue1());

        return yearImportModel;
    }

    private void generateFrontDataHead(String year, List<YearForm> yearModelList) {
        //生成表头
        yearModelList.add(YearForm.builder().columnName("年份").columnSecondName("").value1(year + "年").build());
        yearModelList.add(YearForm.builder().columnName("").value1("一月").value2("二月").value3("三月").value4("四月").value5("五月")
                .value6("六月").value7("七月").value8("八月").value9("九月").value10("十月").value11("十一月").value12("十二月").build());
    }

    /**
     * 生成前端日序列数据
     *
     * @param index
     * @param dayDOS
     * @return
     */
    private YearForm generateFrontDataDay(Integer index, List<SandContentDayDO> dayDOS) {
        Map<Integer, String> dayMonthMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dayDOS)) {
            dayDOS.forEach(item->{
                dayMonthMap.put(item.getMonth(),StringUtils.isBlank(item.getValue())?"":item.getValue());
            });
        }
        return YearForm.builder().columnName(index + "").value1(generateFrontDataDayValue(dayMonthMap, 1)).value2(generateFrontDataDayValue(dayMonthMap, 2))
                .value3(generateFrontDataDayValue(dayMonthMap, 3)).value4(generateFrontDataDayValue(dayMonthMap, 4)).value5(generateFrontDataDayValue(dayMonthMap, 5))
                .value6(generateFrontDataDayValue(dayMonthMap, 6)).value7(generateFrontDataDayValue(dayMonthMap, 7)).value8(generateFrontDataDayValue(dayMonthMap, 8))
                .value9(generateFrontDataDayValue(dayMonthMap, 9)).value10(generateFrontDataDayValue(dayMonthMap, 10)).value11(generateFrontDataDayValue(dayMonthMap, 11))
                .value12(generateFrontDataDayValue(dayMonthMap, 12)).build();
    }

    /**
     * 生成前端月数据
     *
     * @param yearModelList
     * @param monthDOMap
     */
    private void generateFrontMonthData(List<YearForm> yearModelList, Map<Integer, SandContentMonthDO> monthDOMap) {
        // 月平均
        yearModelList.add(YearForm.builder().columnName("月统计").columnSecondName("平均")
                .value1(generateFrontMonthDataValue(monthDOMap, 1, 1))
                .value2(generateFrontMonthDataValue(monthDOMap, 2, 1))
                .value3(generateFrontMonthDataValue(monthDOMap, 3, 1))
                .value4(generateFrontMonthDataValue(monthDOMap, 4, 1))
                .value5(generateFrontMonthDataValue(monthDOMap, 5, 1))
                .value6(generateFrontMonthDataValue(monthDOMap, 6, 1))
                .value7(generateFrontMonthDataValue(monthDOMap, 7, 1))
                .value8(generateFrontMonthDataValue(monthDOMap, 8, 1))
                .value9(generateFrontMonthDataValue(monthDOMap, 9, 1))
                .value10(generateFrontMonthDataValue(monthDOMap, 10, 1))
                .value11(generateFrontMonthDataValue(monthDOMap, 11, 1))
                .value12(generateFrontMonthDataValue(monthDOMap, 12, 1)).build());
        // 月最大值
        yearModelList.add(YearForm.builder().columnSecondName("最大")
                .value1(generateFrontMonthDataValue(monthDOMap, 1, 2))
                .value2(generateFrontMonthDataValue(monthDOMap, 2, 2))
                .value3(generateFrontMonthDataValue(monthDOMap, 3, 2))
                .value4(generateFrontMonthDataValue(monthDOMap, 4, 2))
                .value5(generateFrontMonthDataValue(monthDOMap, 5, 2))
                .value6(generateFrontMonthDataValue(monthDOMap, 6, 2))
                .value7(generateFrontMonthDataValue(monthDOMap, 7, 2))
                .value8(generateFrontMonthDataValue(monthDOMap, 8, 2))
                .value9(generateFrontMonthDataValue(monthDOMap, 9, 2))
                .value10(generateFrontMonthDataValue(monthDOMap, 10, 2))
                .value11(generateFrontMonthDataValue(monthDOMap, 11, 2))
                .value12(generateFrontMonthDataValue(monthDOMap, 12, 2)).build());
        // 月最大值出现日期
        yearModelList.add(YearForm.builder().columnSecondName("日期")
                .value1(generateFrontMonthDataValue(monthDOMap, 1, 3))
                .value2(generateFrontMonthDataValue(monthDOMap, 2, 3))
                .value3(generateFrontMonthDataValue(monthDOMap, 3, 3))
                .value4(generateFrontMonthDataValue(monthDOMap, 4, 3))
                .value5(generateFrontMonthDataValue(monthDOMap, 5, 3))
                .value6(generateFrontMonthDataValue(monthDOMap, 6, 3))
                .value7(generateFrontMonthDataValue(monthDOMap, 7, 3))
                .value8(generateFrontMonthDataValue(monthDOMap, 8, 3))
                .value9(generateFrontMonthDataValue(monthDOMap, 9, 3))
                .value10(generateFrontMonthDataValue(monthDOMap, 10, 3))
                .value11(generateFrontMonthDataValue(monthDOMap, 11, 3))
                .value12(generateFrontMonthDataValue(monthDOMap, 12, 3)).build());
        // 月最小值
        yearModelList.add(YearForm.builder().columnSecondName("最小")
                .value1(generateFrontMonthDataValue(monthDOMap, 1, 4))
                .value2(generateFrontMonthDataValue(monthDOMap, 2, 4))
                .value3(generateFrontMonthDataValue(monthDOMap, 3, 4))
                .value4(generateFrontMonthDataValue(monthDOMap, 4, 4))
                .value5(generateFrontMonthDataValue(monthDOMap, 5, 4))
                .value6(generateFrontMonthDataValue(monthDOMap, 6, 4))
                .value7(generateFrontMonthDataValue(monthDOMap, 7, 4))
                .value8(generateFrontMonthDataValue(monthDOMap, 8, 4))
                .value9(generateFrontMonthDataValue(monthDOMap, 9, 4))
                .value10(generateFrontMonthDataValue(monthDOMap, 10, 4))
                .value11(generateFrontMonthDataValue(monthDOMap, 11, 4))
                .value12(generateFrontMonthDataValue(monthDOMap, 12, 4)).build());
        // 最小值出现日期
        yearModelList.add(YearForm.builder().columnSecondName("日期")
                .value1(generateFrontMonthDataValue(monthDOMap, 1, 5))
                .value2(generateFrontMonthDataValue(monthDOMap, 2, 5))
                .value3(generateFrontMonthDataValue(monthDOMap, 3, 5))
                .value4(generateFrontMonthDataValue(monthDOMap, 4, 5))
                .value5(generateFrontMonthDataValue(monthDOMap, 5, 5))
                .value6(generateFrontMonthDataValue(monthDOMap, 6, 5))
                .value7(generateFrontMonthDataValue(monthDOMap, 7, 5))
                .value8(generateFrontMonthDataValue(monthDOMap, 8, 5))
                .value9(generateFrontMonthDataValue(monthDOMap, 9, 5))
                .value10(generateFrontMonthDataValue(monthDOMap, 10, 5))
                .value11(generateFrontMonthDataValue(monthDOMap, 11, 5))
                .value12(generateFrontMonthDataValue(monthDOMap, 12, 5)).build());
    }

    /**
     * 生成前端年统计表
     *
     * @param yearModelList
     * @param yearDO
     */
    private void generateFrontYearData(List<YearForm> yearModelList, SandContentYearDO yearDO) {
        yearModelList.add(YearForm.builder().columnName("年统计")
                .value1("平均流量").value3(Objects.nonNull(yearDO) ? yearDO.getAverageFlow() : null).value4("m3/s")
                .value5("平均输沙率").value7(Objects.nonNull(yearDO) ? (yearDO.getAverageDischargeRate()) : null).value8("kg/s")
                .value9("平均含沙量：").value11(Objects.nonNull(yearDO) ? (yearDO.getAverageSandContent()) : null).value12("kg/m3").build());

        yearModelList.add(YearForm.builder().columnName("")
                .value1("最大断面平均含沙量：").value3(Objects.nonNull(yearDO) ? yearDO.getMaxSectionAverage() : null)
                .value4("kg/m³").value5(Objects.nonNull(yearDO) ? yearDO.getMaxSectionAverageDate() : null)
                .value7("最小断面平均含沙量：").value9(Objects.nonNull(yearDO) ? yearDO.getMinSectionAverage() : null)
                .value10("kg/m³").value11(Objects.nonNull(yearDO) ? yearDO.getMinSectionAverageDate() : null)
                .build());

    }

    /**
     * 生成前端年统计附注
     *
     * @param yearModelList
     * @param dataLogDO
     */
    private void generateFrontYearRemark(List<YearForm> yearModelList, StationDataLogDO dataLogDO) {
        yearModelList.add(YearForm.builder().columnName("附注").value1(dataLogDO.getYearRemark()).build());
    }

    /**
     * 生成前端单元格内容
     *
     * @param dayMonthMap
     * @param month
     * @return
     */
    private String generateFrontDataDayValue(Map<Integer, String> dayMonthMap, int month) {
        return dayMonthMap.get(month);
    }

    /**
     * 生成前端月数据-月统计-月数据组装
     *
     * @param monthDOMap
     * @param month
     * @return
     */
    private String generateFrontMonthDataValue(Map<Integer, SandContentMonthDO> monthDOMap, int month, Integer type) {
        if (null == type) {
            return null;
        }
        SandContentMonthDO monthDO = monthDOMap.get(month);
        return switch (type) {
            case 1 -> Objects.nonNull(monthDO) ? monthDO.getAverageValue() : null;
            case 2 -> Objects.nonNull(monthDO) ? monthDO.getMaxValue() : null;
            case 3 -> Objects.nonNull(monthDO) ? monthDO.getMaxValueDate() : null;
            case 4 -> Objects.nonNull(monthDO) ? monthDO.getMinValue() : null;
            case 5 -> Objects.nonNull(monthDO) ? monthDO.getMinValueDate() : null;
            default -> null;
        };
    }
}