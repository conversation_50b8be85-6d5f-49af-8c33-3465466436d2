package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargemonth.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水文站-输沙率-月输沙率特征值 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DischargeMonthRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10675")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26444")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private String month;

    @Schema(description = "月平均输沙率(kg/m3)")
    @ExcelProperty("月平均输沙率(kg/m3)")
    private String averageValue;

    @Schema(description = "月平均输沙率备注", example = "随便")
    @ExcelProperty("月平均输沙率备注")
    private String averageValueRemark;

    @Schema(description = "最大日输沙率(kg/m3)")
    @ExcelProperty("最大日输沙率(kg/m3)")
    private String maxValue;

    @Schema(description = "最大日输沙率出现日期")
    @ExcelProperty("最大日输沙率出现日期")
    private String maxValueDate;

    @Schema(description = "最大日输沙率备注", example = "你说的对")
    @ExcelProperty("最大日输沙率备注")
    private String maxValueRemark;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}