package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainmonth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 降水-月降水特征值修改 Request VO")
@Data
public class StationRainMonthSaveReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationRainMonthData> dataList;

    @Schema(description = "管理后台 - 降水-月降水量特征值数据")
    @Data
    public static class StationRainMonthData {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "年")
        private String year;

        @Schema(description = "月")
        private String month;

        @Schema(description = "月降水量(mm)")
        private String value;

        @Schema(description = "月降水日数")
        private Integer valueDays;

        @Schema(description = "最大日降水量(mm)")
        private String maxDayValue;

        @Schema(description = "备注", example = "随便")
        private String remark;
    }

}