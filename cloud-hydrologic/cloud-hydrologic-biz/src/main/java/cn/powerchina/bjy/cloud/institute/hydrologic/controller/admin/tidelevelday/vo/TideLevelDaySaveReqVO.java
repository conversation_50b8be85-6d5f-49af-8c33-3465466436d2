package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDate;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 水文站—潮位日统计新增/修改 Request VO")
@Data
public class TideLevelDaySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12233")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21525")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站", example = "1")
    private Integer stationType;

    @Schema(description = "数据类型", example = "2")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "月")
    private Integer month;

    @Schema(description = "日")
    private Integer day;

    @Schema(description = "潮别")
    private String value1;

    @Schema(description = "潮位")
    private String value2;

    @Schema(description = "时分")
    private String value3;

    @Schema(description = "潮差")
    private String value4;

    @Schema(description = "历时")
    private String value5;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "记录时间")
    private LocalDate currentDay;

    @Schema(description = "版本")
    private Integer version;

}