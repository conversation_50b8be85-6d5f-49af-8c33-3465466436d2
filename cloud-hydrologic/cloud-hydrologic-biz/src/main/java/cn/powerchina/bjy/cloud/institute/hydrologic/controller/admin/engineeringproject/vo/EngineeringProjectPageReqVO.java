package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringproject.vo;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 工程项目信息-项目基本情况分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EngineeringProjectPageReqVO extends PageParam {

    @Schema(description = "项目名称", example = "王五")
    private String name;

    @Schema(description = "所属国家")
    private String country;

    @Schema(description = "所属省级行政区")
    private String province;

    @Schema(description = "所属市级行政区")
    private String city;

    @Schema(description = "所属县级行政区")
    private String county;

    @Schema(description = "详细位置")
    private String address;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "开发方式")
    private String developWay;

    @Schema(description = "是否纳规")
    private String designStage;

    @Schema(description = "工作进展")
    private String progress;

    @Schema(description = "业主单位")
    private String ownerUnit;

    @Schema(description = "重大变更情况说明")
    private String explanation;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}