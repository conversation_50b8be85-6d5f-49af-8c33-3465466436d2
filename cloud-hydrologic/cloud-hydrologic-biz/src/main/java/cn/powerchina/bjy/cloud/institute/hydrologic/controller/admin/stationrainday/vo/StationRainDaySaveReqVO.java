package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 降水-日降水量修改 Request VO")
@Data
public class StationRainDaySaveReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationRainDayData> dataList;

    @Schema(description = "管理后台 - 降水-日降水量数据 Request VO")
    @Data
    public static class StationRainDayData {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "年")
        private Integer year;

        @Schema(description = "月")
        private Integer month;

        @Schema(description = "日")
        private Integer day;

        @Schema(description = "降水量(mm)")
        private String value;

        @Schema(description = "备注", example = "你说的对")
        private String remark;
    }

}