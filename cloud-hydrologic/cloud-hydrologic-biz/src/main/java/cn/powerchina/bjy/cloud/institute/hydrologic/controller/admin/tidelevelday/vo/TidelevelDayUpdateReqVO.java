package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Schema(description = "管理后台 - 降水-各时段最大降水量(分钟时段)新增/修改 Request VO")
@Data
public class TidelevelDayUpdateReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "年")
    private String year;

    @Schema(description = "月")
    private String month;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<TidelevelDayUpdateData> dataList;

    @Schema(description = "要删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "管理后台 - 降水-各时段最大降水量(小时时段)数据")
    @Data
    public static class TidelevelDayUpdateData {

        @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12233")
        private Long id;

        @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21525")
        @NotNull(message = "水文站id不能为空")
        private Long stationId;

        @Schema(description = "站点类型，1：雨量站，2：水文站", example = "1")
        private Integer stationType;

        @Schema(description = "数据类型", example = "2")
        private Integer dataType;

        @Schema(description = "年")
        private String year;

        @Schema(description = "月")
        private String month;

        @Schema(description = "日")
//        @NotNull(message = "日不能为空")
        private Integer day;

        @Schema(description = "潮别")
        private String value2;

        @Schema(description = "日")
        @NotNull(message = "日不能为空")
        private String value1;
        @Schema(description = "潮位")
        private String value3;

        @Schema(description = "时分")
        private String value4;

        @Schema(description = "潮差")
        private String value5;

        @Schema(description = "历时")
        private String value6;


        @Schema(description = "备注", example = "随便")
        private String remark;

        @Schema(description = "记录时间")
        private LocalDate currentDay;

        @Schema(description = "版本")
        private Integer version;
    }

}