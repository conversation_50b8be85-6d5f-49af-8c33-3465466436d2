package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticaldirectory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 水文-统计表-目录新增/修改 Request VO")
@Data
public class StatisticalDirectorySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28356")
    private Long id;

    @Schema(description = "parentId", example = "18273")
    private Long parentId;

    @Schema(description = "目录、统计表、页签的名称", example = "张三")
    private String name;

    @Schema(description = "页签的code")
    private String code;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "1")
    private String stationType;

    @Schema(description = "前端组件name", example = "芋艿")
    private String componentName;

    @Schema(description = "是否标签页，0否，1是，默认为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否标签页，0否，1是，默认为0不能为空")
    private Boolean isLabel;

}