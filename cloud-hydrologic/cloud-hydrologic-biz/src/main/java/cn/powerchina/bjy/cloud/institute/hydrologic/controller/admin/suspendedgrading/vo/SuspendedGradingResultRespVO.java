package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedgrading.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 月年平均悬移质颗粒级配成果 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SuspendedGradingResultRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24141")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "站点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16047")
    @ExcelProperty("站点ID")
    private String stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("站点类型")
    private String stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "年份(4位数字)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("年份(4位数字)")
    private Integer year;

    @Schema(description = "月份(1-12,0表示全年)")
    @ExcelProperty("月份(1-12,0表示全年)")
    private String month;

    @Schema(description = "0.005mm粒径百分数")
    @ExcelProperty("0.005mm粒径百分数")
    private String value0005;

    @Schema(description = "0.007mm粒径百分数")
    @ExcelProperty("0.007mm粒径百分数")
    private String value0007;

    @Schema(description = "0.01mm粒径百分数")
    @ExcelProperty("0.01mm粒径百分数")
    private String value001;

    @Schema(description = "0.025mm粒径百分数")
    @ExcelProperty("0.025mm粒径百分数")
    private String value0025;

    @Schema(description = "0.05mm粒径百分数")
    @ExcelProperty("0.05mm粒径百分数")
    private String value005;

    @Schema(description = "0.1mm粒径百分数")
    @ExcelProperty("0.1mm粒径百分数")
    private String value01;

    @Schema(description = "0.25mm粒径百分数")
    @ExcelProperty("0.25mm粒径百分数")
    private String value025;

    @Schema(description = "0.5mm粒径百分数")
    @ExcelProperty("0.5mm粒径百分数")
    private String value05;

    @Schema(description = "1.0mm粒径百分数")
    @ExcelProperty("1.0mm粒径百分数")
    private String value10;

    @Schema(description = "2.0mm粒径百分数")
    @ExcelProperty("2.0mm粒径百分数")
    private String value20;

    @Schema(description = "3.0mm粒径百分数")
    @ExcelProperty("3.0mm粒径百分数")
    private String value30;

    @Schema(description = "中数粒径(mm)")
    @ExcelProperty("中数粒径(mm)")
    private String medianSize;

    @Schema(description = "平均粒径(mm)")
    @ExcelProperty("平均粒径(mm)")
    private String meanSize;

    @Schema(description = "最大粒径(mm)")
    @ExcelProperty("最大粒径(mm)")
    private String maxSize;

    @Schema(description = "是否为最新版本(1是,0否)")
    @ExcelProperty("是否为最新版本(1是,0否)")
    private Boolean latest;

    @Schema(description = "版本号")
    @ExcelProperty("版本号")
    private Integer version;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}