package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticaldirectory.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 水文-统计表-目录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StatisticalDirectoryPageReqVO extends PageParam {

    @Schema(description = "parentId", example = "18273")
    private Long parentId;

    @Schema(description = "目录、统计表、页签的名称", example = "张三")
    private String name;

    @Schema(description = "页签的code")
    private String code;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "1")
    private String stationType;

    @Schema(description = "前端组件name", example = "芋艿")
    private String componentName;

    @Schema(description = "是否标签页，0否，1是，默认为0")
    private Boolean isLabel;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}