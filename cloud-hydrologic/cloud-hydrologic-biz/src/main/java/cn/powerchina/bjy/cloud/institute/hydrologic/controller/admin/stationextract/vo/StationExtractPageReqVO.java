package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationextract.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import java.util.Date;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 站点-摘录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StationExtractPageReqVO extends PageParam {

    @Schema(description = "记录id")
    private Long logId;

    @Schema(description = "水文站id", example = "30784")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "2")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "月")
    private Integer month;

    @Schema(description = "日")
    private Integer day;

    @Schema(description = "时分")
    private String hoursMinute;

    @Schema(description = "冰情")
    private String iceSituation;

    @Schema(description = "冰厚(m)")
    private String iceThickness;

    @Schema(description = "冰上雪深(m)")
    private String deepSnowOnIce;

    @Schema(description = "岸上气温(C°)")
    private String shoreTemperature;

    @Schema(description = "水位(m)")
    private String waterLevel;

    @Schema(description = "水文站-洪水水文要素摘录表-流量(m3/s)")
    private String flow;

    @Schema(description = "水文站-洪水水文要素摘录表-含沙量(kg/m3)")
    private String sandValue;

    @Schema(description = "降水量")
    private String value;

    @Schema(description = "起时:分")
    private String startHourMinute;

    @Schema(description = "止时:分")
    private String endHourMinute;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "最新版本（1：最新；0：历史）")
    private Integer latest;

    @Schema(description = "记录日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] currentDay;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /** 是否检索：0：否 、 1：是 */
    private Integer indexed;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}