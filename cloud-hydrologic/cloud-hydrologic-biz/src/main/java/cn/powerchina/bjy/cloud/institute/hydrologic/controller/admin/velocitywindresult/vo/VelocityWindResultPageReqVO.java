package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import groovy.transform.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 气象-历年最大风速及风向分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)

public class VelocityWindResultPageReqVO extends PageParam {
    private Long logId;

    @Schema(description = "气象站id", example = "27048")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "2")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "最大风速(m/s)")
    private String velocityMax;

    @Schema(description = "风向1")
    private String wind1;

    @Schema(description = "风向2")
    private String wind2;

    @Schema(description = "风向3")
    private String wind3;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "记录时间")
    private LocalDate currentDay;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
