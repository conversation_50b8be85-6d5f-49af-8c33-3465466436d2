package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveydetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveydetail.vo.SectionSurveyDetailPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveydetail.vo.SectionSurveyDetailRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveydetail.vo.SectionSurveyDetailSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sectionsurveydetail.SectionSurveyDetailDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sectionsurveydetail.SectionSurveyDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 实测大断面成果垂线明细数据")
@RestController
@RequestMapping("/hydrologic/section-survey-detail")
@Validated
public class SectionSurveyDetailController {

    @Resource
    private SectionSurveyDetailService sectionSurveyDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建实测大断面成果垂线明细数据")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-detail:create')")
    public CommonResult<Long> createSectionSurveyDetail(@Valid @RequestBody SectionSurveyDetailSaveReqVO createReqVO) {
        return success(sectionSurveyDetailService.createSectionSurveyDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新实测大断面成果垂线明细数据")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-detail:update')")
    public CommonResult<Boolean> updateSectionSurveyDetail(@Valid @RequestBody SectionSurveyDetailSaveReqVO updateReqVO) {
        sectionSurveyDetailService.updateSectionSurveyDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除实测大断面成果垂线明细数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-detail:delete')")
    public CommonResult<Boolean> deleteSectionSurveyDetail(@RequestParam("id") Long id) {
        sectionSurveyDetailService.deleteSectionSurveyDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得实测大断面成果垂线明细数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-detail:query')")
    public CommonResult<SectionSurveyDetailRespVO> getSectionSurveyDetail(@RequestParam("id") Long id) {
        SectionSurveyDetailDO sectionSurveyDetail = sectionSurveyDetailService.getSectionSurveyDetail(id);
        return success(BeanUtils.toBean(sectionSurveyDetail, SectionSurveyDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得实测大断面成果垂线明细数据分页")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-detail:query')")
    public CommonResult<PageResult<SectionSurveyDetailRespVO>> getSectionSurveyDetailPage(@Valid SectionSurveyDetailPageReqVO pageReqVO) {
        PageResult<SectionSurveyDetailDO> pageResult = sectionSurveyDetailService.getSectionSurveyDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SectionSurveyDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出实测大断面成果垂线明细数据 Excel")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-detail:export')")
    public void exportSectionSurveyDetailExcel(@Valid SectionSurveyDetailPageReqVO pageReqVO,
                                               HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SectionSurveyDetailDO> list = sectionSurveyDetailService.getSectionSurveyDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "实测大断面成果垂线明细数据.xls", "数据", SectionSurveyDetailRespVO.class,
                BeanUtils.toBean(list, SectionSurveyDetailRespVO.class));
    }

}