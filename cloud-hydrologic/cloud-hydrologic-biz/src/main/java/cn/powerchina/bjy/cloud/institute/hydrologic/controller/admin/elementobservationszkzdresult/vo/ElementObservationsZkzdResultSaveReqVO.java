package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;

@Schema(description = "管理后台 - 气象-六要素观测成果表(中科正奇ZK-ZDx)新增/修改 Request VO")
@Data
public class ElementObservationsZkzdResultSaveReqVO {
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5949")
    private Long id;

    @Schema(description = "气象站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23323")
    @NotNull(message = "气象站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "1")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "时间")
    private String time;

    @Schema(description = "日雨量（mm）")
    private String dailyRainfall;

    @Schema(description = "风向编码")
    private String windCode;

    @Schema(description = "风向")
    private String wind;

    @Schema(description = "温度(℃)")
    private String temperature;

    @Schema(description = "湿度(%)")
    private String humidity;

    @Schema(description = "大气压(Kpa)")
    private String atmospheric;

    @Schema(description = "风速(m/s)")
    private String velocity;

    @Schema(description = "分钟雨量（mm)")
    private String minuteRainfall;

    @Schema(description = "电压(V）")
    private String voltage;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本（根据原型待定）不能为空")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最新版本（1：最新，0：历史版本，默认为1）不能为空")
    private Integer latest;

    @Schema(description = "记录时间")
    private LocalDate currentDay;
}
