package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodday;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodday.vo.StationRainPeriodDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodday.vo.StationRainPeriodDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodday.vo.StationRainPeriodDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodday.StationRainPeriodDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationrainperiodday.StationRainPeriodDayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 降水-各时段最大降水量(日时段)")
@RestController
@RequestMapping("/plan/hydrologic/station/rain/period/day")
@Validated
public class StationRainPeriodDayController {

    @Resource
    private StationRainPeriodDayService stationRainPeriodDayService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-获得降水-各时段最大降水量(日时段)分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:query-rain')")
    public CommonResult<PageResult<StationRainPeriodDayRespVO>> getStationRainPeriodDayPage(@Valid StationRainPeriodDayPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        PageResult<StationRainPeriodDayDO> pageResult = stationRainPeriodDayService.getStationRainPeriodDayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodDayRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入降水-各时段最大降水量(日时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> importRainStationRainPeriodDayFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                            @RequestParam("dataType") Integer dataType,
                                                                            @RequestParam("file") MultipartFile file
    ) {
        stationRainPeriodDayService.importStationRainPeriodDayFeatureExcel(stationId, StationEnum.RAIN.getType(),dataType, file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新降水-各时段最大降水量(日时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:update-rain')")
    public CommonResult<Boolean> updateStationRainPeriodDay(@Valid @RequestBody StationRainPeriodDaySaveReqVO updateReqVO) {
        stationRainPeriodDayService.updateStationRainPeriodDay(updateReqVO);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新降水-各时段最大降水量(日时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:update-hydrologic')")
    public CommonResult<Boolean> updateStationHydrologicPeriodDay(@Valid @RequestBody StationRainPeriodDaySaveReqVO updateReqVO) {
        stationRainPeriodDayService.updateStationRainPeriodDay(updateReqVO);
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-获得降水-各时段最大降水量(日时段)分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:query-hydrologic')")
    public CommonResult<PageResult<StationRainPeriodDayRespVO>> getStationHydrologicPeriodDayPage(@Valid StationRainPeriodDayPageReqVO pageReqVO) {
        PageResult<StationRainPeriodDayDO> pageResult = stationRainPeriodDayService.getStationRainPeriodDayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodDayRespVO.class));
    }

    @PostMapping("/import/hydrologic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入降水-各时段最大降水量(日时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> importRainStationHydrologicPeriodDayFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                                  @RequestParam("stationType") Integer stationType,
                                                                                  @RequestParam("dataType") Integer dataType,
                                                                                  @RequestParam("file") MultipartFile file) {
        stationRainPeriodDayService.importStationRainPeriodDayFeatureExcel(stationId,stationType, dataType, file);
        return success(true);
    }



    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出降水-各时段最大降水量(日时段) Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:export-rain')")
    public void exportStationRainPeriodDayExcel(@Valid StationRainPeriodDayPageReqVO pageReqVO,
                                                HttpServletResponse response) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodDayService.exportStationRainPeriodDayExcel(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出降水-各时段最大降水量(日时段) Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:export-hydrologic')")
    public void exportStationHydrologicPeriodDayExcel(@Valid StationRainPeriodDayPageReqVO pageReqVO,
                                                      HttpServletResponse response) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodDayService.exportStationRainPeriodDayExcel(response, pageReqVO);
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出降水-各时段最大降水量(日时段) Excel")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:export-data-rain')")
    public void exportStationRainPeriodDayExcel(@RequestParam("stationId") Long stationId,
                                                HttpServletResponse response) {
        StationRainPeriodDayPageReqVO pageReqVO = new StationRainPeriodDayPageReqVO();
        pageReqVO.setStationId(stationId);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodDayService.exportStationRainPeriodDayExcel(response, pageReqVO);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出降水-各时段最大降水量(日时段) Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:export-data-hydrologic')")
    public void exportStationHydrologicPeriodDayExcel(@RequestParam("stationId") Long stationId,
                                                      HttpServletResponse response) {
        StationRainPeriodDayPageReqVO pageReqVO = new StationRainPeriodDayPageReqVO();
        pageReqVO.setStationId(stationId);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodDayService.exportStationRainPeriodDayExcel(response, pageReqVO);
    }

    @GetMapping("/page/data/rain")
    @Operation(summary = "雨量站-数据检索-获得降水-各时段最大降水量(日时段)分页")
    @HydrologicOperation()
    @Parameter(name = "stationId", description = "雨量站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:query-data-rain')")
    public CommonResult<PageResult<StationRainPeriodDayRespVO>> getStationRainPeriodDayDataPage(StationRainPeriodDayPageReqVO reqVO) {
        PageResult<StationRainPeriodDayDO> pageResult = stationRainPeriodDayService.getStationRainPeriodDayPage(reqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodDayRespVO.class));
    }

    @GetMapping("/page/data/hydrologic")
    @Operation(summary = "水文站-数据检索-获得降水-各时段最大降水量(日时段)分页")
    @HydrologicOperation()
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-day:query-data-hydrologic')")
    public CommonResult<PageResult<StationRainPeriodDayRespVO>> getStationHydrologicPeriodDayDataPage(StationRainPeriodDayPageReqVO reqVO) {
        PageResult<StationRainPeriodDayDO> pageResult = stationRainPeriodDayService.getStationRainPeriodDayPage(reqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodDayRespVO.class));
    }

}