package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 蒸发-逐日水面蒸发量修改 Request VO")
@Data
public class StationEvaporationDaySaveReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationEvaporationDayData> dataList;

    @Schema(description = "管理后台 - 蒸发-逐日水面蒸发量数据修改 Request VO")
    @Data
    public static class StationEvaporationDayData {

        @Schema(description = "主键")
        private Long id;

        @Schema(description = "年")
        private String year;

        @Schema(description = "月")
        private String month;

        @Schema(description = "日")
        private String day;

        @Schema(description = "蒸发量(mm)")
        private String value;

        @Schema(description = "备注")
        private String remark;

    }
}