package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationday.dataprocessor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo.DataLogVersionInfoVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationday.vo.StationDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationday.StationDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.LatestEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.RainFallStationDayImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.WeatherStationDayImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationday.StationDayService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.MyStringUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.StringUtils;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @desc 日维度数据处理器-气象站-逐日气温
 * @time 2024/8/23 14:31
 */
@Service
@Slf4j
public class DayDataProcessorWeatherDayImpl extends AbstractDataProcessor {
    @Autowired
    private StationDayService stationDayService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(StationDaySaveReqVO updateReqVO) {
        List<StationDaySaveReqVO.StationDayData> dataList = updateReqVO.getDataList();
        List<Long> deleteIds = updateReqVO.getDeleteIds();
        //没有要更新或删除的内容直接返回
        if (CollectionUtil.isEmpty(dataList) && CollectionUtil.isEmpty(deleteIds)) {
            log.warn("没有要更新或删除的内容 stationId=[{}] ,dataType=[{}]", updateReqVO.getStationId(), updateReqVO.getDataType());
            return;
        }

        //剔除掉要删除的数据只保留要更新的元素，从更新数据list中： 处理一种特殊情况（前端操作了更新之后又删除）
        if (CollectionUtil.isNotEmpty(dataList) && CollectionUtil.isNotEmpty(deleteIds)) {
            dataList = dataList.stream().filter(item -> !deleteIds.contains(item.getId())).toList();
        }

        //本次更新的内容
        List<WeatherStationDayImportModel> importDataList=new ArrayList<>();

        List<Long> updateIdList = new ArrayList<>();

        //判断本次提交的数据是否跟库中已有的数据有重复
        if (CollectionUtil.isNotEmpty(dataList)) {
            for (StationDaySaveReqVO.StationDayData stationDayData : dataList) {

                Long id = stationDayData.getId();
                if (Objects.isNull(id)) {
                    continue;
                }
                updateIdList.add(id);

                //更新场景重复判断
                StationDayDO stationDayDO = stationDayService.selectOne(
                        updateReqVO.getStationId(),
                        stationDayData.getYear(),
                        stationDayData.getMonth(),
                        stationDayData.getDay(),
                        updateReqVO.getDataType());
                if (Objects.nonNull(stationDayDO) && !stationDayDO.getId().equals(id)) {
                    throw ServiceExceptionUtil.exception(ErrorCodeConstants.DATA_FORMAT_ERROR);
                }
            }
            //获取更新的内容
            importDataList = dataList.stream().map(item -> WeatherStationDayImportModel.builder().year(item.getYear()).month(item.getMonth()).day(item.getDay())
                    .averageValue(item.getAverageValue()).maxValue(item.getMaxValue()).minValue(item.getMinValue()).build()).collect(Collectors.toList());

        }
        dayDataToDbBuilder(updateReqVO, importDataList, updateIdList, false);
    }


    @Override
    public void importExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException {
        //获取excel内容
        List<WeatherStationDayImportModel> importDataList = EasyExcel.read(file.getInputStream(), WeatherStationDayImportModel.class, null).sheet().headRowNumber(1).doReadSync();
        if (CollectionUtils.isEmpty(importDataList)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
        }
        StationDaySaveReqVO stationDaySaveReqVO = new StationDaySaveReqVO();
        stationDaySaveReqVO.setStationId(stationId);
        stationDaySaveReqVO.setStationType(stationType);
        stationDaySaveReqVO.setDataType(dataType);
        dayDataToDbBuilder(stationDaySaveReqVO, importDataList, null, true);
    }


    /**
     * @param updateReqVO    请求参数
     * @param importDataList 本次更新或者导入的数据
     * @param updateIdList
     * @param importFlag     是否导入
     * @desc 更新、删除、批量导入
     * <p>
     * 站点-日序列数据 入库
     */
    private void dayDataToDbBuilder(StationDaySaveReqVO updateReqVO, List<WeatherStationDayImportModel> importDataList, List<Long> updateIdList, boolean importFlag) {
        Long stationId = updateReqVO.getStationId();
        Integer stationType = updateReqVO.getStationType();
        Integer dataType = updateReqVO.getDataType();

        //获取该统计表-年鉴格式相关的所有统计表的下一个版本
        //获取相关统计表的下个版本信息


        DataLogVersionInfoVO dataLogVersionInfoVO = fetchVersions(stationId, stationType, dataType);

        List<StationDayDO> stationDayList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(importDataList)) {
            //数据校验
            dataCheck(importDataList, importFlag);
            //组装数据：本次更新或者导入的内容

            stationDayList = dataBuilder(stationId, stationType, dataType, importDataList, dataLogVersionInfoVO.getDayVersion());

        }

        List<Long> idList = new ArrayList<>();

        CollUtil.addAll(idList, updateIdList);
        CollUtil.addAll(idList, updateReqVO.getDeleteIds());

        //写入数据
        dataUpdate(stationId,
                stationType,
                dataType,
                dataLogVersionInfoVO,
                stationDayList,
                idList,
                importFlag);
    }



    private List<StationDayDO> dataBuilder(Long stationId, Integer stationType, Integer dataType, List<WeatherStationDayImportModel> importDataList, Integer nextVersion) {
        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();
        importDataList.forEach(item -> {
            if (!DateUtils.isValidDate(item.getYear() + "-" + item.getMonth() + "-" + item.getDay(), DateUtils.PATTERN)) {
                throw exception(ErrorCodeConstants.EXCEL_UPDATE_DATA_DAY_FORMAT_ERROR, item.getYear(), item.getMonth(), item.getDay());
            }
        });
        return importDataList.stream()
                .map(item -> {
                    StationDayDO dayDO = new StationDayDO();
                    dayDO.setStationId(stationId);
                    dayDO.setStationType(stationType);
                    dayDO.setDataType(dataType);
                    dayDO.setVersion(nextVersion);
                    dayDO.setYear(item.getYear());
                    dayDO.setMonth(item.getMonth());
                    dayDO.setDay(item.getDay());
                    dayDO.setAverageValue(item.getAverageValue());
                    dayDO.setMaxValue(item.getMaxValue());
                    dayDO.setMinValue(item.getMinValue());
                    dayDO.setLatest(LatestEnum.LATEST.getType());
                    dayDO.setCreator(loginUserId);
                    dayDO.setUpdater(loginUserId);
                    dayDO.setCurrentDay(DateUtils.parseDate(item.getYear() + "-" + item.getMonth() + "-" + item.getDay()));
                    return dayDO;
                }).toList();
    }


    @Deprecated
    private List<StationDayDO> dataBuilderV2(Long stationId, Integer stationType, Integer dataType, List<WeatherStationDayImportModel> importDataList, Integer dayVersion, List<String> years) {
        return importDataList.stream().map(item -> {
            StationDayDO dayDO = new StationDayDO();
            dayDO.setStationId(stationId);
            dayDO.setStationType(stationType);
            dayDO.setDataType(dataType);
            dayDO.setVersion(dayVersion);
            dayDO.setYear(item.getYear());
            dayDO.setMonth(item.getMonth());
            dayDO.setDay(item.getDay());
            dayDO.setAverageValue(item.getAverageValue());
            dayDO.setMaxValue(item.getMaxValue());
            dayDO.setMinValue(item.getMinValue());
            dayDO.setLatest(LatestEnum.LATEST.getType());
            dayDO.setCurrentDay(DateUtils.parseDate(item.getYear() + "-" + item.getMonth() + "-" + item.getDay()));
            return dayDO;
        }).toList();
    }


    /**
     * 数据格式校验
     */
    private void dataCheck(List<WeatherStationDayImportModel> importDataList, boolean importFlag) {
        //数据唯一性判断
        List<String> repeatStr = new ArrayList<>();
        if (CollectionUtil.isEmpty(importDataList)) {
            return;
        }


        for (int i = 0; i < importDataList.size(); i++) {
            WeatherStationDayImportModel importModel = importDataList.get(i);

            //校验年月日
            String year = String.valueOf(importModel.getYear());
            String month = String.valueOf(importModel.getMonth());
            String day = String.valueOf(importModel.getDay());
            MyStringUtils.checkYearMonthDay(year, month, day, i + 2, importFlag);
            if (repeatStr.contains(year + "-" + month + "-" + day)) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_EXCERPT_REPEAT_EXISTS_ERROR, i + 2);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_YMD_REPEAT_EXISTS_ERROR, year, month, day);
                }
            }
            repeatStr.add(year + "-" + month + "-" + day);
        }
    }


    @Deprecated
    private void dataCheckV2(Long stationId, List<WeatherStationDayImportModel> importDataList, boolean importFlag) {
        //校验重复
        List<String> repeatStr = new ArrayList<>();
        for (int i = 0; i < importDataList.size(); i++) {
            WeatherStationDayImportModel importModel = importDataList.get(i);
            StringUtils.checkYearMonthDay(importModel.getYear(), importModel.getMonth(), importModel.getDay(), i + 2, importFlag);
            if (repeatStr.contains(stationId + "-" + importModel.getYear() + "-" + importModel.getMonth() + "-" + importModel.getDay())) {
                if (importFlag) {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_EXCERPT_REPEAT_EXISTS_ERROR, i + 2);
                } else {
                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_YMD_REPEAT_EXISTS_ERROR, importModel.getYear(), importModel.getMonth(), importModel.getDay());
                }
            }
            repeatStr.add(stationId + "-" + importModel.getYear() + "-" + importModel.getMonth() + "-" + importModel.getDay());
        }
    }
}


