package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.migrationtree.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 备份与还原tree新增/修改 Request VO")
@Data
public class MigrationTreeSaveReqVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -5715723522467381212L;

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15746")
    private Long id;

    @Schema(description = "层级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "层级不能为空")
    private Integer level;

    @Schema(description = "名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "名不能为空")
    private String name;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "类型不能为空")
    private String type;

    @Schema(description = "是否虚拟节点", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否虚拟节点不能为空")
    private Boolean virtualNode;

    @Schema(description = "parent id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30725")
    @NotNull(message = "parent id不能为空")
    private Long parentId;

}