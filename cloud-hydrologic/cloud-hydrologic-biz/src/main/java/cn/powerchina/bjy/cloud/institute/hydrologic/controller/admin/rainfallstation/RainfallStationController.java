package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.rainfallstation;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.rainfallstation.vo.RainfallStationPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.rainfallstation.vo.RainfallStationRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.rainfallstation.vo.RainfallStationSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.rainfallstation.RainfallStationDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.rainfallstation.RainfallStationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 雨量站")
@RestController
@RequestMapping("/plan/hydrologic/rainfall/station")
@Validated
public class RainfallStationController {

    @Resource
    private RainfallStationService rainfallStationService;

    @PostMapping("/create")
    @Operation(summary = "创建雨量站")
//    @PreAuthorize("@ss.hasPermission('hydrologic:rainfall-station:create')")
    public CommonResult<Long> createRainfallStation(@Valid @RequestBody RainfallStationSaveReqVO createReqVO) {
        return success(rainfallStationService.createRainfallStation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新雨量站")
//    @PreAuthorize("@ss.hasPermission('hydrologic:rainfall-station:update')")
    public CommonResult<Boolean> updateRainfallStation(@Valid @RequestBody RainfallStationSaveReqVO updateReqVO) {
        rainfallStationService.updateRainfallStation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除雨量站")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:rainfall-station:delete')")
    public CommonResult<Boolean> deleteRainfallStation(@RequestParam("id") Long id) {
        rainfallStationService.deleteRainfallStation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得雨量站")
    @Parameter(name = "id", description = "主键", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:rainfall-station:query')")
    public CommonResult<RainfallStationRespVO> getRainfallStation(@RequestParam("id") Long id) {
        RainfallStationDO rainfallStation = rainfallStationService.getRainfallStation(id);
        RainfallStationRespVO result = BeanUtils.toBean(rainfallStation, RainfallStationRespVO.class);
        result.setDataYear(result.getDataStartYear()+"-"+result.getDataEndYear());
        return success(result);
    }

    @GetMapping("/page")
    @Operation(summary = "获得雨量站分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:rainfall-station:query')")
    public CommonResult<PageResult<RainfallStationRespVO>> getRainfallStationPage(@Valid RainfallStationPageReqVO pageReqVO) {
        PageResult<RainfallStationRespVO> pageResult = rainfallStationService.getRainfallStationPage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/import")
	@ExcelImportCheck
    @Operation(summary = "导入雨量站 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:rainfall-station:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    public CommonResult<Boolean> importRainfallStationExcel(@RequestParam("file") MultipartFile file) {
        rainfallStationService.importRainfallStation(file);
        return success(true);
    }

}