package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.model.SectionSurveyImportModel;
import lombok.Data;

import java.util.List;

//@Data
//public class SectionSurveySaveReqVO {
//    private Long id;
//
//    private Long stationId;
//
//    private Integer year;
//
//    private Integer stationType;
//
//    private Integer dataType;
//
//    private LocalDate surveyDate;
//
//    private String sectionName;
//
//    private BigDecimal waterLevel;
//
//    private Integer periodType;
//
//    private String remark;
//
//}

@Data
public class SectionSurveySaveReqVO {
    private Long stationId;
    private Integer stationType;
    private Integer dataType;
    private List<SectionSurveyDataVO> dataList;  // 添加这个字段
    // 保持原有的垂线明细列表
    private List<SectionSurveyImportModel> detailList;
}

