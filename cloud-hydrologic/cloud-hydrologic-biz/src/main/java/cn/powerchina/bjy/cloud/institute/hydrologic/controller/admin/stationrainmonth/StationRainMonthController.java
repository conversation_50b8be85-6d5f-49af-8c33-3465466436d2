package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainmonth;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo.StationDayDataPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainmonth.vo.StationRainMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainmonth.vo.StationRainMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainmonth.vo.StationRainMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainmonth.StationRainMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationrainmonth.StationRainMonthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 降水-月降水量")
@RestController
@RequestMapping("/plan/hydrologic/station/rain/month")
@Validated
public class StationRainMonthController {

    @Resource
    private StationRainMonthService stationRainMonthService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-获得降水-月降水特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:query-rain')")
    public CommonResult<PageResult<StationRainMonthRespVO>> getStationRainMonthPage(@Valid StationRainMonthPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setCreateTime(null);
        PageResult<StationRainMonthDO> pageResult = stationRainMonthService.getStationRainMonthPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainMonthRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入降水-月降水特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importRainStationRainMonthFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                        @RequestParam("file") MultipartFile file) {
        stationRainMonthService.importStationRainMonthFeatureExcel(stationId, StationEnum.RAIN.getType(), file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新降水-月降水特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:update-rain')")
    public CommonResult<Boolean> updateStationRainMonth(@Valid @RequestBody StationRainMonthSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.RAIN.getType());
        stationRainMonthService.updateStationRainMonth(updateReqVO);
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-获得降水-月降水特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:query-hydrologic')")
    public CommonResult<PageResult<StationRainMonthRespVO>> getStationHydrologicMonthPage(@Valid StationRainMonthPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setCreateTime(null);
        PageResult<StationRainMonthDO> pageResult = stationRainMonthService.getStationRainMonthPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainMonthRespVO.class));
    }

    @PostMapping("/import/hydrologic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入降水-月降水特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    public CommonResult<Boolean> importRainStationHydrologicMonthFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                              @RequestParam("file") MultipartFile file) {
        stationRainMonthService.importStationRainMonthFeatureExcel(stationId, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新降水-月降水特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:update-hydrologic')")
    public CommonResult<Boolean> updateStationHydrologicMonth(@Valid @RequestBody StationRainMonthSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        stationRainMonthService.updateStationRainMonth(updateReqVO);
        return success(true);
    }

    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出降水-月降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:export-rain')")
    public void exportStationRainMonthExcel(@Valid StationRainMonthPageReqVO pageReqVO,
                                            HttpServletResponse response) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setCreateTime(null);
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainMonthService.exportStationRainMonthExcel(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出降水-月降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:export-rain')")
    public void exportStationHydrologicMonthExcel(@Valid StationRainMonthPageReqVO pageReqVO,
                                                  HttpServletResponse response) throws IOException {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setCreateTime(null);
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainMonthService.exportStationRainMonthExcel(response, pageReqVO);
    }

    @GetMapping("/page/data/rain")
    @Operation(summary = "雨量站-数据检索-获得降水-月降水特征值分页")
    @HydrologicOperation(stationType = StationEnum.RAIN, dataType = StationDataTypeEnum.STATION_RAIN_MONTH)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:query-data-rain')")
    public CommonResult<PageResult<StationRainMonthRespVO>> getStationRainMonthDataPage(@Valid StationDayDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationRainMonthPageReqVO pageReqVO1 = new StationRainMonthPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationRainMonthDO> pageResult = stationRainMonthService.getStationRainMonthPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationRainMonthRespVO.class));
    }

    @GetMapping("/page/data/hydrologic")
    @Operation(summary = "水文站-数据检索-获得降水-月降水特征值分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_RAIN_MONTH)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:query-data-hydrologic')")
    public CommonResult<PageResult<StationRainMonthRespVO>> getStationHydrologicMonthDataPage(@Valid StationDayDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationRainMonthPageReqVO pageReqVO1 = new StationRainMonthPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationRainMonthDO> pageResult = stationRainMonthService.getStationRainMonthPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationRainMonthRespVO.class));
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出降水-月降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:export-data-rain')")
    public void exportStationRainMonthDataExcel(@Valid StationDayDataPageReqVO pageReqVO,
                                                HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationRainMonthPageReqVO pageReqVO1 = new StationRainMonthPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setLogId(null);
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainMonthService.exportStationRainMonthExcel(response, pageReqVO1);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出降水-月降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-month:export-data-hydrologic')")
    public void exportStationHydrologicMonthDataExcel(@Valid StationDayDataPageReqVO pageReqVO,
                                                      HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationRainMonthPageReqVO pageReqVO1 = new StationRainMonthPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setLogId(null);
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainMonthService.exportStationRainMonthExcel(response, pageReqVO1);
    }

}