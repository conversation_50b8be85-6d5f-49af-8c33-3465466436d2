package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 降水-各时段最大降水量(日时段)修改 Request VO")
@Data
public class StationRainPeriodDaySaveReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationRainPeriodDayData> dataList;

    @Schema(description = "要删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "管理后台 - 降水-各时段最大降水量(日时段)数据")
    @Data
    public static class StationRainPeriodDayData {

        @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32421")
        private Long id;

        @Schema(description = "年")
        private String year;

        @Schema(description = "1d降水量")
        private String valueDay1;

        @Schema(description = "1d开始月日")
        private String monthDayStart1;

        @Schema(description = "3d降水量")
        private String valueDay3;

        @Schema(description = "3d开始月日")
        private String monthDayStart3;

        @Schema(description = "7d降水量")
        private String valueDay7;

        @Schema(description = "7d开始月日")
        private String monthDayStart7;

        @Schema(description = "15d降水量")
        private String valueDay15;

        @Schema(description = "15d开始月日")
        private String monthDayStart15;

        @Schema(description = "30d降水量")
        private String valueDay30;

        @Schema(description = "30d开始月日")
        private String monthDayStart30;

        @Schema(description = "备注")
        private String remark;
    }
}