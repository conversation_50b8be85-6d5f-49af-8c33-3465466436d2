package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationmonth.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 站点-月统计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StationMonthPageReqVO extends PageParam {

    @Schema(description = "记录id")
    private Long logId;

    @Schema(description = "水文站id", example = "13254")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "2")
    private Integer stationType;

    @Schema(description = "数据类型", example = "2")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "月")
    private Integer month;

    @Schema(description = "月平均值")
    private String averageValue;

    @Schema(description = "月平均值备注", example = "你说的对")
    private String averageValueRemark;

    @Schema(description = "月最大值")
    private String maxValue;

    @Schema(description = "月最大值出现日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] maxValueDate;

    @Schema(description = "月最大值备注", example = "你说的对")
    private String maxValueRemark;

    @Schema(description = "月最小值")
    private String minValue;

    @Schema(description = "月最小值出现日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] minValueDate;

    @Schema(description = "月最小值备注", example = "随便")
    private String minValueRemark;

    @Schema(description = "降水-月降水量(mm)")
    private String value;

    @Schema(description = "降水-月降水日数")
    private Integer valueDays;

    @Schema(description = "降水-最大日降水量(mm)")
    private String maxDayValue;

    @Schema(description = "0.005粒径级（mm）")
    private String grain005;

    @Schema(description = "0.007粒径级（mm）")
    private String grain007;

    @Schema(description = "0.01粒径级（mm）")
    private String grain01;

    @Schema(description = "0.025粒径级（mm）")
    private String grain025;

    @Schema(description = "0.05粒径级（mm）")
    private String grain05;

    @Schema(description = "0.1粒径级（mm）")
    private String grain1;

    @Schema(description = "0.25粒径级（mm）")
    private String grain25;

    @Schema(description = "0.5粒径级（mm）")
    private String grain5;

    @Schema(description = "1.0粒径级（mm）")
    private String grain10;

    @Schema(description = "2.0粒径级（mm）")
    private String grain20;

    @Schema(description = "3.0粒径级（mm）")
    private String grain30;

    @Schema(description = "中数粒径(mm)")
    private String grainMid;

    @Schema(description = "平均粒径(mm)")
    private String grainAverage;

    @Schema(description = "最大粒径(mm)")
    private String grainMax;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "记录时间")
    private LocalDate[] currentDay;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /** 是否检索：0：否 、 1：是 */
    private Integer indexed;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}