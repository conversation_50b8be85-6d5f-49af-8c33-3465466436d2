package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear;


import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TideLevelDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TidelevelDayUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo.TideLevelYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo.TideLevelYearRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo.TideLevelYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo.TidelevelYearUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelyear.TideLevelYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.TideLevelDayForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.TideLevelYearForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.operationlog.OperationLogService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.tidelevelyear.TideLevelYearService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
        import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.io.IOException;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 水文站—潮位年统计")
@RestController
@RequestMapping("/plan/hydrologic/tide-level-year")
@Validated
public class TideLevelYearController {

    @Resource
    private TideLevelYearService tideLevelYearService;

    @Autowired
    private OperationLogService operationLogService;

    @PostMapping("/create")
    @Operation(summary = "创建水文站—潮位年统计")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-year:create')")
    public CommonResult<Long> createTideLevelYear(@Valid @RequestBody TideLevelYearSaveReqVO createReqVO) {
        return success(tideLevelYearService.createTideLevelYear(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水文站—潮位年统计")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-year:update')")
    public CommonResult<Boolean> updateTideLevelYear(@Valid @RequestBody TideLevelYearSaveReqVO updateReqVO) {
        tideLevelYearService.updateTideLevelYear(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水文站—潮位年统计")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-year:delete')")
    public CommonResult<Boolean> deleteTideLevelYear(@RequestParam("id") Long id) {
        tideLevelYearService.deleteTideLevelYear(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水文站—潮位年统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-year:query')")
    public CommonResult<TideLevelYearRespVO> getTideLevelYear(@RequestParam("id") Long id) {
        TideLevelYearDO tideLevelYear = tideLevelYearService.getTideLevelYear(id);
        return success(BeanUtils.toBean(tideLevelYear, TideLevelYearRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水文站—潮位年统计分页")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-year:query')")
    public CommonResult<PageResult<TideLevelYearRespVO>> getTideLevelYearPage(@Valid TideLevelYearPageReqVO pageReqVO) {
        PageResult<TideLevelYearDO> pageResult = tideLevelYearService.getTideLevelYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TideLevelYearRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水文站—潮位年统计 Excel")
    public void exportTideLevelDayExcel(TideLevelYearPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        tideLevelYearService.exportTideLevelDayExcel(pageReqVO,response);
    }

    @PostMapping("/import")
    @ExcelImportCheck
    @Operation(summary = "逐潮高低潮位表-年统计导入")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "站点id", required = true)
    @Parameter(name = "stationType", description = "站点类型", required = true)
    @Parameter(name = "year", description = "年份", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> impotDayExcel(@RequestParam("stationId") Long stationId,
                                               @RequestParam("stationType") Integer stationType,
                                               @RequestParam("dataType") Integer dataType,
//                                               @RequestParam("year") Integer year,
                                               @RequestParam("file") MultipartFile file) {
        Integer year=2024;
        tideLevelYearService.impotDayExcel(stationId,dataType,stationType, file,year);
        return success(true);
    }

    @GetMapping("/info")
    @Operation(summary = "逐潮高低潮位表-年统计查询")
    @HydrologicOperation
    public CommonResult<List<TideLevelYearForm>> getYearBookList(TideLevelYearPageReqVO reqVO) {
        //数据处理器
        return success(tideLevelYearService.getYearBookList(reqVO));
    }

    @PostMapping("/update-year")
    @Operation(summary = "逐潮高低潮位表-年统计修改")
    @HydrologicOperation
    public CommonResult<Boolean> updateAndDeleteTideLevelYear(@Valid @RequestBody TidelevelYearUpdateReqVO reqVO) {
        //数据处理器
//        tideLevelDayService.getYearBookList(reqVO);
        tideLevelYearService.updateAndDeleteTideLevelYear(reqVO);
        return success(true);
    }
}