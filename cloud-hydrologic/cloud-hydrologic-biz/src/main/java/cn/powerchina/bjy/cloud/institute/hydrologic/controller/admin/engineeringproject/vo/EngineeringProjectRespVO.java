package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringproject.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo.EngineeringProjectParametersSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 工程项目信息-项目基本情况 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EngineeringProjectRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17069")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "项目名称", example = "王五")
    @ExcelProperty("项目名称")
    private String name;

    @Schema(description = "所属国家")
    @ExcelProperty("所属国家")
    private String country;

    @Schema(description = "所属省级行政区")
    @ExcelProperty("所属省级行政区")
    private String province;

    @Schema(description = "所属市级行政区")
    @ExcelProperty("所属市级行政区")
    private String city;

    @Schema(description = "所属县级行政区")
    @ExcelProperty("所属县级行政区")
    private String county;

    @Schema(description = "详细位置")
    @ExcelProperty("详细位置")
    private String address;

    @Schema(description = "经度")
    @ExcelProperty("经度")
    private String longitude;

    @Schema(description = "纬度")
    @ExcelProperty("纬度")
    private String latitude;

    @Schema(description = "开发方式")
    @ExcelProperty("开发方式")
    private String developWay;

    @Schema(description = "是否纳规")
    @ExcelProperty("是否纳规")
    private String designStage;

    @Schema(description = "工作进展")
    @ExcelProperty("工作进展")
    private String progress;

    @Schema(description = "业主单位")
    @ExcelProperty("业主单位")
    private String ownerUnit;

    @Schema(description = "重大变更情况说明")
    @ExcelProperty("重大变更情况说明")
    private String explanation;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "编辑的数据")
    private Map<String, Object> parametersArray;  ;

}
