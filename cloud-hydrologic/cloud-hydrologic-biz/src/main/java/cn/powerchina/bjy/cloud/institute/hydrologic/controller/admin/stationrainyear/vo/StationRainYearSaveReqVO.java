package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainyear.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 降水-年降水量特征值 修改 Request VO")
@Data
public class StationRainYearSaveReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationRainYearData> dataList;

    @Schema(description = "管理后台 - 降水-年降水量特征值数据")
    @Data
    public static class StationRainYearData {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "年")
        private String year;

        @Schema(description = "年降水量(mm)")
        private String value;

        @Schema(description = "年降水日数")
        private Integer valueDays;

        @Schema(description = "备注")
        private String remark;
    }

}