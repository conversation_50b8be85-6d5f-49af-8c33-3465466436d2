package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringproject;


import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringproject.vo.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.IndexReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.StationInfoRespIndexVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.engineeringproject.EngineeringProjectDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.engineeringproject.EngineeringProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 工程项目信息-项目基本情况")
@RestController
@RequestMapping("/plan/hydrologic/engineering-project")
@Validated
public class EngineeringProjectController {

    @Resource
    private EngineeringProjectService engineeringProjectService;

    @PostMapping("/create")
    @Operation(summary = "创建工程项目信息-项目基本情况")
//    @PreAuthorize("@ss.hasPermission('hydrologic:engineering-project:create')")
    public CommonResult<Long> createEngineeringProject(@Valid @RequestBody EngineeringProjectSaveReqVO createReqVO) {
        return success(engineeringProjectService.createEngineeringProject(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新工程项目信息-项目基本情况")
//    @PreAuthorize("@ss.hasPermission('hydrologic:engineering-project:update')")
    public CommonResult<Boolean> updateEngineeringProject(@Valid @RequestBody EngineeringProjectSaveReqVO updateReqVO) {
        engineeringProjectService.updateEngineeringProject(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @Operation(summary = "删除工程项目信息")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:engineering-project:delete')")
    public CommonResult<Boolean> deleteEngineeringProject(@RequestParam("id") Long id) {
        engineeringProjectService.deleteEngineeringProject(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得工程项目信息-项目基本情况")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('hydrologic:engineering-project:query')")
    public CommonResult<EngineeringProjectRespVO> getEngineeringProject(@RequestParam("id") Long id) {
        EngineeringProjectRespVO engineeringProjectRespVO = engineeringProjectService.getEngineeringProject(id);
        return success(engineeringProjectRespVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得工程项目信息-项目基本情况分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:engineering-project:query')")
    public CommonResult<PageResult<EngineeringProjectRespVO>> getEngineeringProjectPage(@Valid IndexPageReqVO pageReqVO) {
        PageResult<EngineeringProjectRespVO> pageResult = engineeringProjectService.getEngineeringProjectPage(pageReqVO);
        return success(pageResult);
    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出工程项目信息-项目基本情况 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:engineering-project:export')")
////    @OperateLog(type = EXPORT)
//    public void exportEngineeringProjectExcel(@Valid EngineeringProjectPageReqVO pageReqVO,
//                                              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<EngineeringProjectDO> list = engineeringProjectService.getEngineeringProjectPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "工程项目信息-项目基本情况.xls", "数据", EngineeringProjectRespVO.class,
//                BeanUtils.toBean(list, EngineeringProjectRespVO.class));
//    }
//
    @PostMapping("/import")
    @Operation(summary = "导入工程项目信息")
    //    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:import')")
    public CommonResult<Boolean> importEngineeringProjectExcel(@RequestParam("file") MultipartFile file
                                                                 ) throws IOException {
        engineeringProjectService.importExcel(file);
        return success(true);
    }
//
//    @GetMapping("/engineering/list")
//    @Operation(summary = "项目列表")
////    @PreAuthorize("@ss.hasPermission('hydrologic:index:query-station')")
//    public CommonResult<List<StationInfoRespIndexVO>> findList() {
////        return success(engineeringProjectService.findList());
//    }
}