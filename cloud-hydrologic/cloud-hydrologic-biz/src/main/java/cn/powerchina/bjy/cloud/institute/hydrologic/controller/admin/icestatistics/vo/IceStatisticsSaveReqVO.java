package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 冰情统计新增/修改 Request VO")
@Data
public class IceStatisticsSaveReqVO {

    @Schema(description = "站点id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11254")
    @NotNull(message = "站点id不能为空")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值")
    private Integer stationType;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<Item> dataList;

    @Schema(description = "要删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "管理后台 - 降水-日降水量数据 Request VO")
    @Data
    public static class Item {
        @Schema(description = "主键，修改时必传", example = "25603")
        private Long id;

        @Schema(description = "水文站id", example = "25543")
        private Long stationId;

        @Schema(description = "年份，修改时必传")
        private String year;

        @Schema(description = "解冻月日")
        private String thawingMonthDay;

        @Schema(description = "终止流冰月日")
        private String flowingEndDay;

        @Schema(description = "终冰月日")
        private String finalMonthDay;

        @Schema(description = "初冰月日")
        private String firstMonthDay;

        @Schema(description = "开始流冰月日")
        private String startFlowMonthDay;

        @Schema(description = "封冻月日")
        private String frozenMonthDay;

        @Schema(description = "上半年实际封冻天数")
        private String firstHalfYearDays;

        @Schema(description = "下半年实际封冻天数")
        private String secondHalfYearDays;

        @Schema(description = "河心最大冰厚(m) ")
        private String centerMaxThickness;

        @Schema(description = "河心最大冰厚出现日期")
        private String centerMaxThicknessDate;

        @Schema(description = "岸边最大冰厚(m)")
        private String shoreMaxThickness;

        @Schema(description = "岸边最大冰厚出现日期")
        private String shoreMaxThicknessDate;

        @Schema(description = "最大流冰块长度(m) ")
        private String flowMaxLength;

        @Schema(description = "最大流冰块宽度(m)")
        private String flowMaxWidth;

        @Schema(description = "冰速(m/s)")
        private String iceSpeed;
    }
}