package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherstation.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "管理后台 - 气象站新增/修改 Request VO")
@Data
public class WeatherStationSaveReqVO {

    @Schema(description = "主键，修改是必传", example = "24047")
    private Long id;

    @Schema(description = "站名", example = "赵六", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "站名不能为空")
    private String hydrologicName;

    @Schema(description = "国", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "国不能为空")
    private String country;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "县")
    private String county;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "设立机构")
    private String establishInstitution;

    @Schema(description = "经度（度）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "经度不能为空")
    private String longitude;

    @Schema(description = "纬度（度）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "纬度不能为空")
    private String latitude;

    @Schema(description = "海拔高度（米）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "海拔高度不能为空")
    private String altitude;

    @Schema(description = "站址迁移")
    private String siteRelocation;

    @Schema(description = "开始观测年份", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "开始观测年份不能为空")
    private String startObservationYear;

    @Schema(description = "开始观测月份")
    private String startObservationMonth;

    @Schema(description = "缺测时间段")
    private String missingTestingTimePeriod;

    @Schema(description = "观测方式")
    private String observationMethod;

    @Schema(description = "年限")
    private Integer dataAge;

    @Schema(description = "开始年份")
    private String dataStartYear;

    @Schema(description = "结束年份")
    private String dataEndYear;

}