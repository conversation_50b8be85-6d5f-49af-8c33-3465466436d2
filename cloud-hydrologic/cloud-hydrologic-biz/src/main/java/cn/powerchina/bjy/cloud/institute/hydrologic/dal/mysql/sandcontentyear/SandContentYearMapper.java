package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.sandcontentyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentyear.vo.SandContentYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sandcontentyear.SandContentYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.HydrologicChecker;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 水文站-输沙率-年含沙量特征值 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SandContentYearMapper extends BaseMapperX<SandContentYearDO>, HydrologicChecker<SandContentYearDO> {

    default PageResult<SandContentYearDO> selectPage(SandContentYearPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SandContentYearDO>()
                .eqIfPresent(SandContentYearDO::getStationId, reqVO.getStationId())
                .eqIfPresent(SandContentYearDO::getYear, reqVO.getYear())
                .eqIfPresent(SandContentYearDO::getMaxSectionAverage, reqVO.getMaxSectionAverage())
                .eqIfPresent(SandContentYearDO::getMaxSectionAverageDate, reqVO.getMaxSectionAverageDate())
                .eqIfPresent(SandContentYearDO::getMinSectionAverage, reqVO.getMinSectionAverage())
                .eqIfPresent(SandContentYearDO::getMinSectionAverageDate, reqVO.getMinSectionAverageDate())
                .eqIfPresent(SandContentYearDO::getAverageSandContent, reqVO.getAverageSandContent())
                .eqIfPresent(SandContentYearDO::getAverageFlow, reqVO.getAverageFlow())
                .eqIfPresent(SandContentYearDO::getAverageDischargeRate, reqVO.getAverageDischargeRate())
                .eqIfPresent(SandContentYearDO::getRemark, reqVO.getRemark())
                .eqIfPresent(SandContentYearDO::getVersion, reqVO.getVersion())
                .eqIfPresent(SandContentYearDO::getLatest, reqVO.getLatest())
                .betweenIfPresent(SandContentYearDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(SandContentYearDO::getYear));
    }

    @Select("SELECT version FROM hydrologic_sand_content_year WHERE station_id = #{stationId} AND latest = 1 AND deleted = 0 limit 1")
    Integer getLatestVersion(@Param("stationId") Long stationId);
}