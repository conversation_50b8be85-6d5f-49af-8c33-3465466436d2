package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.operationlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.OperationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.operationlog.vo.OperationLogPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.operationlog.vo.OperationLogRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.operationlog.OperationLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.operationlog.OperationLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 检索记录")
@RestController
@RequestMapping("/plan/hydrologic/operation/log")
@Validated
public class OperationLogController {

    @Resource
    private OperationLogService operationLogService;


    @GetMapping("/page")
    @Operation(summary = "获得检索记录分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:operation-log:query')")
    public CommonResult<PageResult<OperationLogRespVO>> getOperationLogPage(@Valid OperationLogPageReqVO pageReqVO) {
        pageReqVO.setOperationType(OperationEnum.SEARCH.getType());
        //特殊处理一下  开始为00:00:00 结束为23:59:59
        LocalDateTime[] createTime = pageReqVO.getCreateTime();
        if(ArrayUtils.isNotEmpty(createTime)){
            createTime[0] = createTime[0].with(LocalTime.MIDNIGHT);
            createTime[1] = createTime[1].with(LocalTime.MAX);
        }
        PageResult<OperationLogDO> pageResult = operationLogService.getOperationLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OperationLogRespVO.class));
    }

}