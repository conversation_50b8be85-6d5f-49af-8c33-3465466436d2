package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentyear.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水文站-输沙率-年含沙量特征值 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SandContentYearRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "25821")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8891")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "最大断面平均含沙量(kg/m3)")
    @ExcelProperty("最大断面平均含沙量(kg/m3)")
    private String maxSectionAverage;

    @Schema(description = "最大断面平均含沙量出现日期")
    @ExcelProperty("最大断面平均含沙量出现日期")
    private String maxSectionAverageDate;

    @Schema(description = "最小断面平均含沙量(kg/m3)")
    @ExcelProperty("最小断面平均含沙量(kg/m3)")
    private String minSectionAverage;

    @Schema(description = "最小断面平均含沙量出现日期")
    @ExcelProperty("最小断面平均含沙量出现日期")
    private String minSectionAverageDate;

    @Schema(description = "年平均含沙量(kg/m3)")
    @ExcelProperty("年平均含沙量(kg/m3)")
    private String averageSandContent;

    @Schema(description = "年平均流量(m3/s)")
    @ExcelProperty("年平均流量(m3/s)")
    private String averageFlow;

    @Schema(description = "年平均输沙率(kg/s)")
    @ExcelProperty("年平均输沙率(kg/s)")
    private String averageDischargeRate;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}