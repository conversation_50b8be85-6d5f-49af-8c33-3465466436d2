package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 降水-各时段最大降水量(分钟时段)新增/修改 Request VO")
@Data
public class TidelevelMonthUpdateReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "是否删除 true是 ")
    private Boolean isDelete;

    @Schema(description = "年")
    private String year;

    @Schema(description = "月")
    private String month;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<TidelevelMonthUpdateData> dataList;

    @Schema(description = "要删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "管理后台 - 降水-各时段最大降水量(小时时段)数据")
    @Data
    public static class TidelevelMonthUpdateData {

        @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12233")
        private Long id;

        @Schema(description = "水文站id", example = "1278")
        private Long stationId;

        @Schema(description = "站点类型，1：雨量站，2：水文站", example = "1")
        private Integer stationType;

        @Schema(description = "数据类型", example = "2")
        private Integer dataType;

        @Schema(description = "年")
        private String year;

        @Schema(description = "月")
        private String month;

        @Schema(description = "项目")
        private String value1;

        @Schema(description = "潮位最高（大)")
        private String value2;

        @Schema(description = "日")
        private String value3;

        @Schema(description = "时分")
        private String value4;

        @Schema(description = "月(潮位最高农历)")
        private String value5;

        @Schema(description = "日(潮位最高农历)")
        private String value6;

        @Schema(description = "潮位最高低（小)")
        private String value7;

        @Schema(description = "日")
        private String value8;

        @Schema(description = "时分")
        private String value9;

        @Schema(description = "月(潮位最低农历)")
        private String value10;

        @Schema(description = "日(潮位最低农历)")
        private String value11;

        @Schema(description = "月总数")
        private String value12;

        @Schema(description = "次数")
        private String value13;

        @Schema(description = "平均")
        private String value14;

        @Schema(description = "平均")
        private String value15;

        @Schema(description = "平均")
        private String value16;

        @Schema(description = "平均")
        private String value17;

        @Schema(description = "平均")
        private String value18;

        @Schema(description = "备注", example = "你说的对")
        private String remark;

        @Schema(description = "记录时间")
        private LocalDate currentDay;

        @Schema(description = "版本")
        private Integer version;

        @Schema(description = "创建时间")
        @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
        private LocalDateTime[] createTime;
    }

}