package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 蒸发-逐日水面蒸发量 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationEvaporationDayRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private String month;

    @Schema(description = "日")
    @ExcelProperty("日")
    private String day;

    @Schema(description = "蒸发量(mm)")
    @ExcelProperty("蒸发量(mm)")
    private String value;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}