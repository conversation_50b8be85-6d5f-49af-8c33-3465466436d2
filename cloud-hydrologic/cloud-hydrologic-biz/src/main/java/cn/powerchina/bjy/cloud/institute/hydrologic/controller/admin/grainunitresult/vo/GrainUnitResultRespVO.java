package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.grainunitresult.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水文-实测悬移质单位水样颗粒级配成果 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GrainUnitResultRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19802")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28372")
    @ExcelProperty("水文站id")
    private Long stationId;
    /**
     * 站点类型
     */
    private Integer stationType;
    /**
     * 数据类型
     */
    private Integer dataType;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private String month;

    @Schema(description = "日")
    @ExcelProperty("日")
    private String day;

    @Schema(description = "时分")
    @ExcelProperty("时分")
    private String hoursMinute;

    /**
     * 施测号数
     */
    private String tesNum;

    @Schema(description = "0.005粒径级（mm）")
    @ExcelProperty("0.005粒径级（mm）")
    private String grain005;

    @Schema(description = "0.007粒径级（mm）")
    @ExcelProperty("0.007粒径级（mm）")
    private String grain007;

    @Schema(description = "0.01粒径级（mm）")
    @ExcelProperty("0.01粒径级（mm）")
    private String grain01;

    @Schema(description = "0.025粒径级（mm）")
    @ExcelProperty("0.025粒径级（mm）")
    private String grain025;

    @Schema(description = "0.05粒径级（mm）")
    @ExcelProperty("0.05粒径级（mm）")
    private String grain05;

    @Schema(description = "0.1粒径级（mm）")
    @ExcelProperty("0.1粒径级（mm）")
    private String grain1;

    @Schema(description = "0.25粒径级（mm）")
    @ExcelProperty("0.25粒径级（mm）")
    private String grain25;

    @Schema(description = "0.5粒径级（mm）")
    @ExcelProperty("0.5粒径级（mm）")
    private String grain5;

    @Schema(description = "1.0粒径级（mm）")
    @ExcelProperty("1.0粒径级（mm）")
    private String grain10;

    @Schema(description = "2.0粒径级（mm）")
    @ExcelProperty("2.0粒径级（mm）")
    private String grain20;

    @Schema(description = "3.0粒径级（mm）")
    @ExcelProperty("3.0粒径级（mm）")
    private String grain30;

//    @Schema(description = "中数粒径(mm)", example = "7740")
//    @ExcelProperty("中数粒径(mm)")
//    private String grainMid;

    @Schema(description = "最大粒径(mm)")
    @ExcelProperty("最大粒径(mm)")
    private String grainMax;

    @Schema(description = "单样含沙量（kg/m3)")
    @ExcelProperty("单样含沙量（kg/m3)")
    private String sampleSandContent;

    @Schema(description = "施测水温(℃)")
    @ExcelProperty("施测水温(℃)")
    private String waterTemperature;

    @Schema(description = "取样方法")
    @ExcelProperty("取样方法")
    private String samplingMethod;

    @Schema(description = "分析方法")
    @ExcelProperty("分析方法")
    private String analysisMethod;

    @Schema(description = "附注", example = "随便")
    @ExcelProperty("附注")
    private String remark;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}