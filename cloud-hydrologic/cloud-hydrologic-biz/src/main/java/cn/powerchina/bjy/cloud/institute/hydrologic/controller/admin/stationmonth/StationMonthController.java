package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationmonth;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icemonth.vo.IceMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icemonth.vo.IceMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationmonth.vo.StationMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationmonth.vo.StationMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationmonth.vo.StationMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth.StationMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationmonth.StationMonthService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "水文 - 站点-月统计")
@RestController
@RequestMapping("/plan/hydrologic/station-month")
@Validated
public class StationMonthController {

    @Resource
    private StationMonthService stationMonthService;

    @PostMapping("/create")
    @Operation(summary = "创建站点-月统计")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-month:create')")
    public CommonResult<Long> createStationMonth(@Valid @RequestBody StationMonthSaveReqVO createReqVO) {
        return success(stationMonthService.createStationMonth(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除站点-月统计")
    @Parameter(name = "id", description = "编号", required = true)
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-month:delete')")
    public CommonResult<Boolean> deleteStationMonth(@RequestParam("id") Long id) {
        stationMonthService.deleteStationMonth(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得站点-月统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-month:query')")
    public CommonResult<StationMonthRespVO> getStationMonth(@RequestParam("id") Long id) {
        StationMonthDO stationMonth = stationMonthService.getStationMonth(id);
        return success(BeanUtils.toBean(stationMonth, StationMonthRespVO.class));
    }

//    @GetMapping("/page")
//    @Operation(summary = "获得站点-月统计分页")
//   // @PreAuthorize("@ss.hasPermission('hydrologic:station-month:query')")
//    public CommonResult<PageResult<StationMonthRespVO>> getStationMonthPage(@Valid StationMonthPageReqVO pageReqVO) {
//        PageResult<StationMonthDO> pageResult = stationMonthService.getStationMonthPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, StationMonthRespVO.class));
//    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出站点-月统计 Excel")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-month:export')")
//    @OperateLog(type = EXPORT)
    public void exportStationMonthExcel(@Valid StationMonthPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setCreateTime(null);
        stationMonthService.exportStationMonthExcel(pageReqVO, response);
    }

    @PostMapping("/import")
	@ExcelImportCheck
    @Operation(summary = "导入-站点数据-月序列格式数据")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "stationType", description = "站点类型", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> importRainStationRainMonthFeatureExcel(@RequestParam("file") MultipartFile file,
                                                                        @RequestParam("stationId") Long stationId,
                                                                        @RequestParam("stationType") Integer stationType,
                                                                        @RequestParam("dataType") Integer  dataType
    ) {
        stationMonthService.importStationMonthExcel(file,stationId, stationType,dataType);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新站点-月统计")
    // @PreAuthorize("@ss.hasPermission('hydrologic:station-day:update')")
    public CommonResult<Boolean> updateStationMonth(@Valid @RequestBody StationMonthSaveReqVO updateReqVO) {
        stationMonthService.updateStationMonth(updateReqVO);
        return success(true);
    }


    @GetMapping("/page")
    @Operation(summary = "分页查看-站点数据-月序列格式")
    @HydrologicOperation
    public CommonResult<PageResult<StationMonthDO>> getStationRainMonthPage(@Valid StationMonthPageReqVO pageReqVO) {
        pageReqVO.setCreateTime(null);
        PageResult<StationMonthDO> pageResult = stationMonthService.getStationMonthPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationMonthDO.class));
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-获得冰情水温月分页")
    @HydrologicOperation
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<StationMonthDO>> getIceMonthDataPage(@Valid StationMonthPageReqVO pageReqVO) {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return success(PageResult.empty());
        }
        pageReqVO.setLogId(null);
        PageResult<StationMonthDO> pageResult = stationMonthService.getStationMonthPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationMonthDO.class));
    }
}