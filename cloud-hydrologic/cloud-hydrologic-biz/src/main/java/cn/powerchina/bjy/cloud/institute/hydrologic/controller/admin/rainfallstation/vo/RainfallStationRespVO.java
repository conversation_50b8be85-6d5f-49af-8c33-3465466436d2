package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.rainfallstation.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 雨量站 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RainfallStationRespVO {

    @Schema(description = "主键")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "测站编码")
    @ExcelProperty("测站编码")
    private String hydrologicCode;

    @Schema(description = "水系")
    @ExcelProperty("水系")
    private String riverSystem;

    @Schema(description = "河名")
    @ExcelProperty("河名")
    private String riverName;

    @Schema(description = "站名")
    @ExcelProperty("站名")
    private String hydrologicName;

    @Schema(description = "国")
    @ExcelProperty("国")
    private String country;

    @Schema(description = "省")
    @ExcelProperty("省")
    private String province;

    @Schema(description = "市")
    @ExcelProperty("市")
    private String city;

    @Schema(description = "县")
    @ExcelProperty("县")
    private String county;

    @Schema(description = "详细位置")
    @ExcelProperty("详细位置")
    private String address;

    @Schema(description = "经度")
    @ExcelProperty("经度")
    private String longitude;

    @Schema(description = "纬度")
    @ExcelProperty("纬度")
    private String latitude;

    @Schema(description = "年份")
    @ExcelProperty("年份")
    private String year;

    @Schema(description = "月份")
    @ExcelProperty("月份")
    private String month;

    @Schema(description = "绝对高程")
    @ExcelProperty("绝对高程")
    private String absoluteHeight;

    @Schema(description = "地面高度")
    @ExcelProperty("地面高度")
    private String groundHeight;

    @Schema(description = "型式")
    @ExcelProperty("型式")
    private String type;

    @Schema(description = "资料项目")
    @ExcelProperty("资料项目")
    private String dataProject;

    @Schema(description = "资料项目-降水")
    private Integer dataProjectRainFall;

    @Schema(description = "资料项目-水面蒸发")
    private Integer dataProjectEvaporate;

    @Schema(description = "领导机关")
    @ExcelProperty("领导机关")
    private String leadershipOrganization;

    @Schema(description = "年限")
    @ExcelProperty("年限")
    private Integer dataAge;

    @Schema(description = "开始年份")
    @ExcelProperty("开始年份")
    private String dataStartYear;

    @Schema(description = "结束年份")
    @ExcelProperty("结束年份")
    private String dataEndYear;

    @Schema(description = "资料起止年份")
    @ExcelProperty("资料起止年份")
    private String dataYear;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}