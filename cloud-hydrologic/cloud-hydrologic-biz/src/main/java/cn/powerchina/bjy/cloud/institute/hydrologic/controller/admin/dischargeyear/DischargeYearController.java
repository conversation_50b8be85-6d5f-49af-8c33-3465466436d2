package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeyear.vo.DischargeYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeyear.vo.DischargeYearRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeyear.vo.DischargeYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.dischargeyear.DischargeYearService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台-年输沙率特征值")
@RestController
@RequestMapping("/plan/hydrologic/discharge/year")
@Validated
public class DischargeYearController {

    @Resource
    private DischargeYearService dischargeYearService;

    @PutMapping("/update")
    @Operation(summary = "更新水文站-输沙率-年输沙率特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:discharge-year:update')")
    public CommonResult<Boolean> updateDischargeYear(@Valid @RequestBody DischargeYearSaveReqVO updateReqVO) {
        dischargeYearService.updateDischargeYear(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "年输沙率特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:discharge-year:query')")
    public CommonResult<PageResult<DischargeYearRespVO>> getDischargeYearPage(@Valid DischargeYearPageReqVO pageReqVO) {
        PageResult<DischargeYearRespVO> pageResult = dischargeYearService.getDischargeYearPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-年输沙率特征值分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_DISCHARGE_YEAR)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<DischargeYearRespVO>> getDischargeYearDataPage(@Valid DischargeYearPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        PageResult<DischargeYearRespVO> pageResult = dischargeYearService.getDischargeYearPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出年输沙率特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportDischargeYearExcel(@Valid DischargeYearPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        dischargeYearService.exportDischargeYearExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出年输沙率特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportDischargeYearDataExcel(@Valid DischargeYearPageReqVO pageReqVO,
                                             HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        dischargeYearService.exportDischargeYearExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入年输沙率特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:flow-info:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        dischargeYearService.importData(file, stationId);
        return success(true);
    }

}