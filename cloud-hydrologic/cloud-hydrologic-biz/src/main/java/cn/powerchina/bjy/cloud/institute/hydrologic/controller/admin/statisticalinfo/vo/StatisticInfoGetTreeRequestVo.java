package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 统计表目录 get tree请求参数
 * @time 2024/8/19 08:46
 */
@Data
@Schema(description = "水文-水文统计表目录 Request VO")
public class StatisticInfoGetTreeRequestVo implements Serializable {
    @Schema(description = "站点类型 1:雨量站 2：水文站 3：气象站", requiredMode = Schema.RequiredMode.REQUIRED)
    private String stationType;
    @Schema(description = "站点id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long stationId;
}