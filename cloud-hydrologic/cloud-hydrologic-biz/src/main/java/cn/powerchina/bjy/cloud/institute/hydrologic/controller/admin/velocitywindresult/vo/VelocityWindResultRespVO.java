package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 气象-历年最大风速及风向 Response VO")
@Data
@ExcelIgnoreUnannotated

public class VelocityWindResultRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "20781")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "气象站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27048")
    @ExcelProperty("气象站id")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "2")
    @ExcelProperty("站点类型，1：雨量站，2：水文站， 3：气象站")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "年")
    @ExcelProperty("年")
    private Integer year;

    @Schema(description = "最大风速(m/s)")
    @ExcelProperty("最大风速(m/s)")
    private String velocityMax;

    @Schema(description = "风向1")
    @ExcelProperty("风向1")
    private String wind1;

    @Schema(description = "风向2")
    @ExcelProperty("风向2")
    private String wind2;

    @Schema(description = "风向3")
    @ExcelProperty("风向3")
    private String wind3;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "记录时间")
    @ExcelProperty("记录时间")
    private LocalDate currentDay;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
