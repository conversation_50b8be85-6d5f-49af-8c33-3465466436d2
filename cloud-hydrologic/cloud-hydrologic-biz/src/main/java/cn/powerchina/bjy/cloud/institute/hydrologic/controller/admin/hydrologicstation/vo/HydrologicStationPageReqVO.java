package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 水文站分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class HydrologicStationPageReqVO extends PageParam {

    @Schema(description = "测站编码")
    private String hydrologicCode;

    @Schema(description = "水系")
    private String riverSystem;

    @Schema(description = "河名", example = "芋艿")
    private String riverName;

    @Schema(description = "站名", example = "王五")
    private String hydrologicName;

    @Schema(description = "站别", example = "水文或水位")
    private String hydrologicType;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "县")
    private String county;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "集水面积")
    private String catchmentArea;

    @Schema(description = "距河口距离")
    private String distanceFromTheRiverMouth;

    @Schema(description = "年份")
    private String year;

    @Schema(description = "月份")
    private String month;

    @Schema(description = "资料项目")
    private String dataProject;

    @Schema(description = "领导机关")
    private String leadershipOrganization;

    @Schema(description = "历史沿革")
    private String historicalEvolution;

    @Schema(description = "控制条件")
    private String controlConditions;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}