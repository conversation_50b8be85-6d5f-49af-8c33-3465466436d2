package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/7/19
 */
@Data
public class StationIdReqVO {

    @Schema(description = "站点id")
    @NotNull(message = "站点id必填")
    private Long stationId;
}
