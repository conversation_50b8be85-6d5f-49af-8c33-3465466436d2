package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodday.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 降水-各时段最大降水量(日时段) Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationRainPeriodDayRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "1d降水量")
    @ExcelProperty("1d降水量")
    private String valueDay1;

    @Schema(description = "1d开始月日")
    @ExcelProperty("1d开始月日")
    private String monthDayStart1;

    @Schema(description = "3d降水量")
    @ExcelProperty("3d降水量")
    private String valueDay3;

    @Schema(description = "3d开始月日")
    @ExcelProperty("3d开始月日")
    private String monthDayStart3;

    @Schema(description = "7d降水量")
    @ExcelProperty("7d降水量")
    private String valueDay7;

    @Schema(description = "7d开始月日")
    @ExcelProperty("7d开始月日")
    private String monthDayStart7;

    @Schema(description = "15d降水量")
    @ExcelProperty("15d降水量")
    private String valueDay15;

    @Schema(description = "15d开始月日")
    @ExcelProperty("15d开始月日")
    private String monthDayStart15;

    @Schema(description = "30d降水量")
    @ExcelProperty("30d降水量")
    private String valueDay30;

    @Schema(description = "30d开始月日")
    @ExcelProperty("30d开始月日")
    private String monthDayStart30;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}