package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainexcerpt;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo.StationDayDataPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainexcerpt.vo.StationRainExcerptPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainexcerpt.vo.StationRainExcerptRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainexcerpt.vo.StationRainExcerptSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainexcerpt.StationRainExcerptDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationrainexcerpt.StationRainExcerptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 降水-摘录")
@RestController
@RequestMapping("/plan/hydrologic/station/rain/excerpt")
@Validated
public class StationRainExcerptController {

    @Resource
    private StationRainExcerptService stationRainExcerptService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-获得降水-摘录分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:query-rain')")
    public CommonResult<PageResult<StationRainExcerptRespVO>> getStationRainExcerptPage(@Valid StationRainExcerptPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setCreateTime(null);
        PageResult<StationRainExcerptDO> pageResult = stationRainExcerptService.getStationRainExcerptPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainExcerptRespVO.class));
    }

    @GetMapping("/page/data/rain")
    @Operation(summary = "雨量站-数据检索-获得降水-摘录分页")
    @HydrologicOperation(stationType = StationEnum.RAIN, dataType = StationDataTypeEnum.STATION_RAIN_EXCERPT)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:query-data-rain')")
    public CommonResult<PageResult<StationRainExcerptRespVO>> getStationRainExcerptDataPage(@Valid StationDayDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationRainExcerptPageReqVO pageReqVO1 = new StationRainExcerptPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationRainExcerptDO> pageResult = stationRainExcerptService.getStationRainExcerptPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationRainExcerptRespVO.class));
    }

    @GetMapping("/page/data/hydrologic")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_RAIN_EXCERPT)
    @Operation(summary = "水文站-数据检索-获得降水-摘录分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:query-data-hydrologic')")
    public CommonResult<PageResult<StationRainExcerptRespVO>> getStationHydrologicExcerptDataPage(@Valid StationDayDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationRainExcerptPageReqVO pageReqVO1 = new StationRainExcerptPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationRainExcerptDO> pageResult = stationRainExcerptService.getStationRainExcerptPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationRainExcerptRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入降水-摘录")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importRainStationRainExcerptExcel(@RequestParam("stationId") Long stationId,
                                                                   @RequestParam(value = "file",required = false) MultipartFile file) {
        stationRainExcerptService.importStationRainExcerptExcel(stationId, StationEnum.RAIN.getType(), file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新降水-摘录")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:update-rain')")
    public CommonResult<Boolean> updateStationRainExcerpt(@Valid @RequestBody StationRainExcerptSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.RAIN.getType());
        stationRainExcerptService.updateStationRainExcerpt(updateReqVO);
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-获得降水-摘录分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:query-hydrologic')")
    public CommonResult<PageResult<StationRainExcerptRespVO>> getStationHydrologicExcerptPage(@Valid StationRainExcerptPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setCreateTime(null);
        PageResult<StationRainExcerptDO> pageResult = stationRainExcerptService.getStationRainExcerptPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainExcerptRespVO.class));
    }

    @PostMapping("/import/hydrologic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入降水-摘录")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    public CommonResult<Boolean> importRainStationHydrologicExcerptExcel(@RequestParam("stationId") Long stationId,
                                                                         @RequestParam("file") MultipartFile file) {
        stationRainExcerptService.importStationRainExcerptExcel(stationId, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新降水-摘录")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:update-hydrologic')")
    public CommonResult<Boolean> updateStationHydrologicExcerpt(@Valid @RequestBody StationRainExcerptSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        stationRainExcerptService.updateStationRainExcerpt(updateReqVO);
        return success(true);
    }

    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出降水-摘录 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:export-rain')")
    public void exportStationRainExcerptExcel(@Valid StationRainExcerptPageReqVO pageReqVO,
                                              HttpServletResponse response) {
        pageReqVO.setCreateTime(null);
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        stationRainExcerptService.exportStationRainExcerptExcel(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出降水-摘录 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:export-hydrologic')")
    public void exportStationHydrologicExcerptExcel(@Valid StationRainExcerptPageReqVO pageReqVO,
                                                    HttpServletResponse response) {
        pageReqVO.setCreateTime(null);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainExcerptService.exportStationRainExcerptExcel(response, pageReqVO);
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出降水-摘录 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:export-data-rain')")
    public void exportStationRainExcerptDataExcel(@Valid StationDayDataPageReqVO pageReqVO,
                                                  HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationRainExcerptPageReqVO pageReqVO1 = new StationRainExcerptPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setLogId(null);
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainExcerptService.exportStationRainExcerptExcel(response, pageReqVO1);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出降水-摘录 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-excerpt:export-data-hydrologic')")
    public void exportStationHydrologicExcerptDataExcel(@Valid StationDayDataPageReqVO pageReqVO,
                                                        HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationRainExcerptPageReqVO pageReqVO1 = new StationRainExcerptPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setLogId(null);
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainExcerptService.exportStationRainExcerptExcel(response, pageReqVO1);
    }

}