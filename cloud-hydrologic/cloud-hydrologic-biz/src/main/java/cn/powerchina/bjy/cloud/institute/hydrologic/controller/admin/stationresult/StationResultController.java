package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationresult;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationresult.vo.StationResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationresult.vo.StationResultRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationresult.vo.StationResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationresult.StationResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationresult.StationResultService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 站点-成果")
@RestController
@RequestMapping("/plan/hydrologic/station-result")
@Validated
public class StationResultController {

    @Resource
    private StationResultService stationResultService;

    @PostMapping("/create")
    @Operation(summary = "创建站点-成果")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-result:create')")
    public CommonResult<Long> createStationResult(@Valid @RequestBody StationResultSaveReqVO createReqVO) {
        return success(stationResultService.createStationResult(createReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除站点-成果")
    @Parameter(name = "id", description = "编号", required = true)
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-result:delete')")
    public CommonResult<Boolean> deleteStationResult(@RequestParam("id") Long id) {
        stationResultService.deleteStationResult(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得站点-成果")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-result:query')")
    public CommonResult<StationResultRespVO> getStationResult(@RequestParam("id") Long id) {
        StationResultDO stationResult = stationResultService.getStationResult(id);
        return success(BeanUtils.toBean(stationResult, StationResultRespVO.class));
    }

   //----------------------------------------------------------

    @PutMapping("/update")
    @Operation(summary = "更新站点-成果")
    // @PreAuthorize("@ss.hasPermission('hydrologic:station-result:update')")
    public CommonResult<Boolean> updateStationResult(@Valid @RequestBody StationResultSaveReqVO updateReqVO) {
        stationResultService.updateStationResult(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得站点-成果分页")
    @HydrologicOperation
    // @PreAuthorize("@ss.hasPermission('hydrologic:station-result:query')")
    public CommonResult<PageResult<StationResultRespVO>> getStationResultPage(@Valid StationResultPageReqVO pageReqVO) {
        PageResult<StationResultDO> pageResult = stationResultService.getStationResultPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationResultRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出站点-成果 Excel")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-result:export')")
    public void exportStationResultExcel(@Valid StationResultPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        //检索和记录 两个页面使用
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationResultService.exportExcel(response, pageReqVO);
    }

    @PostMapping("/import")
	@ExcelImportCheck
    @Operation(summary = "导入站点-成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:ice-day:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId,
                                             @RequestParam("stationType") Integer stationType,
                                             @RequestParam("dataType") Integer  dataType) throws IOException {
        stationResultService.importStationYearExcel(file,stationId, stationType,dataType);
        return success(true);
    }

}