package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationyear.vo.StationEvaporationYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationyear.vo.StationEvaporationYearRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationyear.vo.StationEvaporationYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainyear.vo.StationRainYearBookUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationevaporationyear.StationEvaporationYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.RainFallDataRainYearModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationevaporationyear.StationEvaporationYearService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 蒸发-年水面蒸发量特征值")
@RestController
@RequestMapping("/plan/hydrologic/station/evaporation/year")
@Validated
public class StationEvaporationYearController {

    @Resource
    private StationEvaporationYearService stationEvaporationYearService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-获得蒸发-年水面蒸发量特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:query-rain')")
    public CommonResult<PageResult<StationEvaporationYearRespVO>> getStationEvaporationRainYearPage(@Valid StationEvaporationYearPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        PageResult<StationEvaporationYearDO> pageResult = stationEvaporationYearService.getStationEvaporationYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationEvaporationYearRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入蒸发-年水面蒸发量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importRainStationRainYearFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                       @RequestParam("file") MultipartFile file) {
        stationEvaporationYearService.importStationEvaporateYearFeatureExcel(stationId, StationEnum.RAIN.getType(), file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新蒸发-年水面蒸发量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:update-rain')")
    public CommonResult<Boolean> updateStationEvaporationRainYear(@Valid @RequestBody StationEvaporationYearSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.RAIN.getType());
        stationEvaporationYearService.updateStationEvaporationYear(updateReqVO);
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-获得蒸发-年水面蒸发量特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:query-hydrologic')")
    public CommonResult<PageResult<StationEvaporationYearRespVO>> getStationEvaporationHydrologicYearPage(@Valid StationEvaporationYearPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        PageResult<StationEvaporationYearDO> pageResult = stationEvaporationYearService.getStationEvaporationYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationEvaporationYearRespVO.class));
    }

    @PostMapping("/import/hydrolofic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入蒸发-年水面蒸发量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    public CommonResult<Boolean> importRainStationHydrologicYearFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                             @RequestParam("file") MultipartFile file) {
        stationEvaporationYearService.importStationEvaporateYearFeatureExcel(stationId, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新蒸发-年水面蒸发量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:update-hydrologic')")
    public CommonResult<Boolean> updateStationEvaporationHydrologicYear(@Valid @RequestBody StationEvaporationYearSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        stationEvaporationYearService.updateStationEvaporationYear(updateReqVO);
        return success(true);
    }

    @GetMapping("/rainbook/info")
    @Operation(summary = "雨量站-获得蒸发-年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:rainbook-info')")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "logId", description = "记录id")
    public CommonResult<List<RainFallDataRainYearModel>> getStationRainYearBookList(@RequestParam("stationId") Long stationId,
                                                                                    @RequestParam(value = "logId", required = false) Long logId) {
        return success(stationEvaporationYearService.findStationYearBookList(stationId, StationEnum.RAIN.getType(), logId, null));
    }

    @PostMapping("/import/rainbook")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入蒸发-年水面蒸发量年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:import-rainbook')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importRainStationYearExcel(@RequestParam("stationId") Long stationId,
                                                            @RequestParam("file") MultipartFile file) {
        stationEvaporationYearService.importStationEvaporateYearBookExcel(stationId, StationEnum.RAIN.getType(), file);
        return success(true);
    }

    @PostMapping("/rainbook/update")
    @Operation(summary = "雨量站-蒸发-年鉴格式-修改数据")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:update-rainbook')")
    public CommonResult<Boolean> updateStationRainYearBookList(@RequestBody StationRainYearBookUpdateReqVO reqVO) {
        stationEvaporationYearService.modifyStationYearBookList(reqVO.getStationId(), StationEnum.RAIN.getType(), reqVO.getDataMode());
        return success(true);
    }

    @GetMapping("/hydrologicbook/info")
    @Operation(summary = "水文站-获得蒸发-年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:hydrologicbook-info')")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "logId", description = "记录id")
    public CommonResult<List<RainFallDataRainYearModel>> getStationHydrologicYearBookList(@RequestParam("stationId") Long stationId,
                                                                                          @RequestParam(value = "logId", required = false) Long logId) {
        return success(stationEvaporationYearService.findStationYearBookList(stationId, StationEnum.HYDROLOGIC.getType(), logId, null));
    }

    @PostMapping("/import/hydrologicbook")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入蒸发-年水面蒸发量年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:import-hydrologicbook')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importHydrologicStationYearExcel(@RequestParam("stationId") Long stationId,
                                                                  @RequestParam("file") MultipartFile file) {
        stationEvaporationYearService.importStationEvaporateYearBookExcel(stationId, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PostMapping("/hydrologicbook/update")
    @Operation(summary = "水文站-蒸发-年鉴格式-修改数据")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:update-hydrologicbook')")
    public CommonResult<Boolean> updateStationHydrologicYearBookList(@RequestBody StationRainYearBookUpdateReqVO reqVO) {
        stationEvaporationYearService.modifyStationYearBookList(reqVO.getStationId(), StationEnum.HYDROLOGIC.getType(), reqVO.getDataMode());
        return success(true);
    }

    @GetMapping("/page/data/rain")
    @Operation(summary = "雨量站-数据检索-获得蒸发-年水面蒸发量特征值分页")
    @HydrologicOperation(stationType = StationEnum.RAIN, dataType = StationDataTypeEnum.STATION_EVAPORATE_YEAR)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:query-data-rain')")
    public CommonResult<PageResult<StationEvaporationYearRespVO>> getStationEvaporationRainYearDataPage(@Valid StationEvaporationYearPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        PageResult<StationEvaporationYearDO> pageResult = stationEvaporationYearService.getStationEvaporationYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationEvaporationYearRespVO.class));
    }

    @GetMapping("/page/data/hydrologic")
    @Operation(summary = "水文站-数据检索-获得蒸发-年水面蒸发量特征值分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_EVAPORATE_YEAR)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:query-data-hydrologic')")
    public CommonResult<PageResult<StationEvaporationYearRespVO>> getStationEvaporationHydrologicYearDataPage(@Valid StationEvaporationYearPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        PageResult<StationEvaporationYearDO> pageResult = stationEvaporationYearService.getStationEvaporationYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationEvaporationYearRespVO.class));
    }

    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出蒸发-年水面蒸发量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:export-rain')")
    public void exportRainStationEvaporationYearExcel(@Valid StationEvaporationYearPageReqVO pageReqVO,
                                                      HttpServletResponse response) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationEvaporationYearService.exportStationEvaporationYear(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出蒸发-年水面蒸发量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:export-hydrologic')")
    public void exportHydrologicStationEvaporationYearExcel(@Valid StationEvaporationYearPageReqVO pageReqVO,
                                                            HttpServletResponse response) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationEvaporationYearService.exportStationEvaporationYear(response, pageReqVO);
    }

    @GetMapping("/export/rainbook")
    @Operation(summary = "雨量站-导出蒸发-年鉴格式 Excel")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "logId", description = "记录id")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:export-rainbook')")
    public void exportRainStationEvaporationYearBookExcel(@RequestParam("stationId") Long stationId,
                                                          @RequestParam(value = "logId", required = false) Long logId,
                                                          HttpServletResponse response) {

        stationEvaporationYearService.exportStationEvaporationYearMark(response, stationId, StationEnum.RAIN.getType(), logId, null);
    }

    @GetMapping("/export/hydrologicbook")
    @Operation(summary = "水文站-导出蒸发-年鉴格式 Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "logId", description = "记录id")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:export-rainbook')")
    public void exportHydrologicStationEvaporationYearBookExcel(@RequestParam("stationId") Long stationId,
                                                                @RequestParam(value = "logId", required = false) Long logId,
                                                                HttpServletResponse response) {
        stationEvaporationYearService.exportStationEvaporationYearMark(response, stationId, StationEnum.HYDROLOGIC.getType(), logId, null);
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出蒸发-年水面蒸发量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:export-data-rain')")
    public void exportRainStationEvaporationYearDataExcel(@Valid StationEvaporationYearPageReqVO pageReqVO,
                                                          HttpServletResponse response) {
        pageReqVO.setLogId(null);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationEvaporationYearService.exportStationEvaporationYear(response, pageReqVO);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出蒸发-年水面蒸发量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:export-data-hydrologic')")
    public void exportHydrologicStationEvaporationYearDataExcel(@Valid StationEvaporationYearPageReqVO pageReqVO,
                                                                HttpServletResponse response) throws IOException {
        pageReqVO.setLogId(null);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationEvaporationYearService.exportStationEvaporationYear(response, pageReqVO);
    }

    @GetMapping("/rainbook/data/info")
    @Operation(summary = "雨量站-数据检索-获得蒸发-年鉴格式")
    @HydrologicOperation(stationType = StationEnum.RAIN, dataType = StationDataTypeEnum.STATION_EVAPORATE_YEAR_BOOK)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:rainbook-data-info')")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "year", description = "年")
    public CommonResult<List<RainFallDataRainYearModel>> getStationRainYearBookDataList(@RequestParam("stationId") Long stationId,
                                                                                        @RequestParam(value = "year", required = false) Integer year) {
        if (Objects.isNull(year)) {
            return success(new ArrayList<>());
        }
        return success(stationEvaporationYearService.findStationYearBookList(stationId, StationEnum.RAIN.getType(), null, year));
    }

    @GetMapping("/hydrologicbook/data/info")
    @Operation(summary = "水文站-数据检索-获得蒸发-年鉴格式")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_EVAPORATE_YEAR_BOOK)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:hydrologicbook-data-info')")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "year", description = "年")
    public CommonResult<List<RainFallDataRainYearModel>> getStationHydrologicYearBookDataList(@RequestParam("stationId") Long stationId,
                                                                                              @RequestParam(value = "year", required = false) Integer year) {
        if (Objects.isNull(year)) {
            return success(new ArrayList<>());
        }
        return success(stationEvaporationYearService.findStationYearBookList(stationId, StationEnum.HYDROLOGIC.getType(), null, year));
    }

    @GetMapping("/export/data/rainbook")
    @Operation(summary = "雨量站-数据检索-导出蒸发-年鉴格式 Excel")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "year", description = "年")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:export-data-rainbook')")
    public void exportRainStationEvaporationYearBookDataExcel(@RequestParam("stationId") Long stationId,
                                                              @RequestParam(value = "year", required = false) Integer year,
                                                              HttpServletResponse response) {
        if (Objects.isNull(year)) {
            return;
        }
        stationEvaporationYearService.exportStationEvaporationYearMark(response, stationId, StationEnum.RAIN.getType(), null, year);
    }

    @GetMapping("/export/data/hydrologicbook")
    @Operation(summary = "水文站-数据检索-导出蒸发-年鉴格式 Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "year", description = "年")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-year:export-data-rainbook')")
    public void exportHydrologicStationEvaporationYearBookDataExcel(@RequestParam("stationId") Long stationId,
                                                                    @RequestParam(value = "year", required = false) Integer year,
                                                                    HttpServletResponse response) {
        if (Objects.isNull(year)) {
            return;
        }
        stationEvaporationYearService.exportStationEvaporationYearMark(response, stationId, StationEnum.HYDROLOGIC.getType(), null, year);
    }

}