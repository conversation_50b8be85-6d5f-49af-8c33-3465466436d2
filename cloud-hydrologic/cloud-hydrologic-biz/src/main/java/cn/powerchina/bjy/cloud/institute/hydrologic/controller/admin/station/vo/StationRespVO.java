package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.station.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 站点 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "29540")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "2")
    @ExcelProperty("站点类型，1：雨量站，2：水文站，3：气象站")
    private Integer stationType;

    @Schema(description = "测站编码")
    @ExcelProperty("测站编码")
    private String hydrologicCode;

    @Schema(description = "站名", example = "李四")
    @ExcelProperty("站名")
    private String hydrologicName;

    @Schema(description = "站别", example = "2")
    @ExcelProperty("站别")
    private String hydrologicType;

    @Schema(description = "水系")
    @ExcelProperty("水系")
    private String riverSystem;

    @Schema(description = "河名", example = "赵六")
    @ExcelProperty("河名")
    private String riverName;

    @Schema(description = "国")
    @ExcelProperty("国")
    private String country;


    @Schema(description = "省")
    @ExcelProperty("省")
    private String province;

    @Schema(description = "市")
    @ExcelProperty("市")
    private String city;

    @Schema(description = "县")
    @ExcelProperty("县")
    private String county;

    @Schema(description = "详细地址")
    @ExcelProperty("详细地址")
    private String address;

    @Schema(description = "经度")
    @ExcelProperty("经度")
    private String longitude;

    @Schema(description = "纬度")
    @ExcelProperty("纬度")
    private String latitude;

    @Schema(description = "集水面积")
    @ExcelProperty("集水面积")
    private String catchmentArea;

    @Schema(description = "距河口距离")
    @ExcelProperty("距河口距离")
    private String distanceFromTheRiverMouth;

    @Schema(description = "年份")
    @ExcelProperty("年份")
    private String year;

    @Schema(description = "月份")
    @ExcelProperty("月份")
    private String month;

    @Schema(description = "资料项目")
    @ExcelProperty("资料项目")
    private String dataProject;

    @Schema(description = "领导机关")
    @ExcelProperty("领导机关")
    private String leadershipOrganization;

    @Schema(description = "历史沿革")
    @ExcelProperty("历史沿革")
    private String historicalEvolution;

    @Schema(description = "控制条件")
    @ExcelProperty("控制条件")
    private String controlConditions;

    @Schema(description = "雨量站-绝对高程")
    @ExcelProperty("雨量站-绝对高程")
    private String absoluteHeight;

    @Schema(description = "雨量站-地面高度")
    @ExcelProperty("雨量站-地面高度")
    private String groundHeight;

    @Schema(description = "雨量站-型式", example = "2")
    @ExcelProperty("雨量站-型式")
    private String type;

    @Schema(description = "气象站-海拔高度（米）")
    @ExcelProperty("气象站-海拔高度（米）")
    private String altitude;

    @Schema(description = "气象站-站址迁移")
    @ExcelProperty("气象站-站址迁移")
    private String siteRelocation;

    @Schema(description = "气象站-开始观测年份")
    @ExcelProperty("气象站-开始观测年份")
    private String startObservationYear;

    @Schema(description = "气象站-开始观测月份")
    @ExcelProperty("气象站-开始观测月份")
    private String startObservationMonth;

    @Schema(description = "气象站-缺测时间段")
    @ExcelProperty("气象站-缺测时间段")
    private String missingTestingTimePeriod;

    @Schema(description = "气象站-观测方式")
    @ExcelProperty("气象站-观测方式")
    private String observationMethod;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "资料起止年份")
    @ExcelProperty("资料起止年份")
    private String dataYear;

}