package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday;

import cn.hutool.extra.spring.SpringUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.almanac.vo.BookReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodminute.vo.StationRainPeriodMinutePageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TideLevelDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TideLevelDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TideLevelDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TidelevelDayUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelday.TideLevelDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnumV2;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.TideLevelDayForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.YearForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationalmanac.StationAlmanacService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.tidelevelday.TideLevelDayService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;
@Tag(name = "管理后台 - 水文站—潮位日统计")
@RestController
@RequestMapping("/plan/hydrologic/tide-level-day")
@Validated
public class TideLevelDayController {

    @Resource
    private TideLevelDayService tideLevelDayService;

    @PostMapping("/create")
    @Operation(summary = "创建水文站—潮位日统计")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-day:create')")
    public CommonResult<Long> createTideLevelDay(@Valid @RequestBody TideLevelDaySaveReqVO createReqVO) {
        return success(tideLevelDayService.createTideLevelDay(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水文站—潮位日统计")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-day:update')")
    public CommonResult<Boolean> updateTideLevelDay(@Valid @RequestBody TideLevelDaySaveReqVO updateReqVO) {
        tideLevelDayService.updateTideLevelDay(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水文站—潮位日统计")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-day:delete')")
    public CommonResult<Boolean> deleteTideLevelDay(@RequestParam("id") Long id) {
        tideLevelDayService.deleteTideLevelDay(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水文站—潮位日统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-day:query')")
    public CommonResult<TideLevelDayRespVO> getTideLevelDay(@RequestParam("id") Long id) {
        TideLevelDayDO tideLevelDay = tideLevelDayService.getTideLevelDay(id);
        return success(BeanUtils.toBean(tideLevelDay, TideLevelDayRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水文站—潮位日统计分页")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-day:query')")
    public CommonResult<PageResult<TideLevelDayRespVO>> getTideLevelDayPage(@Valid TideLevelDayPageReqVO pageReqVO) {
        PageResult<TideLevelDayDO> pageResult = tideLevelDayService.getTideLevelDayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TideLevelDayRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水文站—潮位日统计 Excel")
    public void exportTideLevelDayExcel(@Valid TideLevelDayPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        tideLevelDayService.exportTideLevelDayExcel(pageReqVO,response);
    }

    @PostMapping("/import")
    @ExcelImportCheck
    @Operation(summary = "逐潮高低潮位表-日统计导入")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "站点id", required = true)
    @Parameter(name = "stationType", description = "站点类型", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> impotDayExcel(@RequestParam("stationId") Long stationId,
                                                                               @RequestParam("stationType") Integer stationType,
                                                                               @RequestParam("dataType") Integer dataType,
                                                                               @RequestParam("file") MultipartFile file) {
        dataType=550104;
        tideLevelDayService.impotDayExcel(stationId,dataType,stationType, file);
        return success(true);
    }

    @GetMapping("/info")
    @Operation(summary = "逐潮高低潮位表-日统计查询")
    @HydrologicOperation
    public CommonResult<List<TideLevelDayForm>> getYearBookList(TideLevelDayPageReqVO reqVO) {
        //数据处理器
//        tideLevelDayService.getYearBookList(reqVO);

        return success(tideLevelDayService.getYearBookList(reqVO));
    }

    @PostMapping("/update-day")
    @Operation(summary = "逐潮高低潮位表-日统计修改")
    @HydrologicOperation
    public CommonResult<Boolean> updateAndDeleteTideLevelDay(@Valid @RequestBody TidelevelDayUpdateReqVO reqVO) {
        //数据处理器
//        tideLevelDayService.getYearBookList(reqVO);
        tideLevelDayService.updateAndDeleteTideLevelDay(reqVO);
        return success(true);
    }


}