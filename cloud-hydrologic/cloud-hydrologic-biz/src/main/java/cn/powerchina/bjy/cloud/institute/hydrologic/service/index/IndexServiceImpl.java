package cn.powerchina.bjy.cloud.institute.hydrologic.service.index;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.IndexInfoRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.IndexReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.StationInfoRespIndexVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.StationInfoRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.engineeringproject.EngineeringProjectDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.engineeringprojectparameters.EngineeringProjectParametersDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filetree.FileTreeDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.hydrologicstation.HydrologicStationDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.project.ProjectDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.rainfallstation.RainfallStationDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.resourcesitemanagement.ResourceSiteManagementDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.roleapply.RoleApplyDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.station.StationDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticaldirectory.StatisticalDirectoryDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticalinfo.StatisticalInfoDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.weatherstation.WeatherStationDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.engineeringproject.EngineeringProjectMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.engineeringprojectparameters.EngineeringProjectParametersMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.resourcesitemanagement.ResourceSiteManagementMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.station.StationMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.statisticaldirectory.StatisticalDirectoryMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.statisticalinfo.StatisticalInfoMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.filetree.FileTreeService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.hydrologicstation.HydrologicStationService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.project.ProjectService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.rainfallstation.RainfallStationService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.roleapply.RoleApplyService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.station.StationService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationyear.StationYearService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticalinfo.StatisticalInfoService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.weatherstation.WeatherStationService;
import cn.powerchina.bjy.cloud.institute.hydrologic.task.AreaManager;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.RoleUtil;
import cn.powerchina.bjy.cloud.system.api.area.AreaApi;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.WEATHER_DATA_NOT_EXISTS;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.HydrologicDataProjectEnum.EVAPORATION;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.HydrologicDataProjectEnum.RAINFALL;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/7/9
 */
@Service
@Validated
@Slf4j
public class IndexServiceImpl implements IndexService {

    @Autowired
    private HydrologicStationService hydrologicStationService;

    @Autowired
    private WeatherStationService weatherStationService;

    @Autowired
    private RainfallStationService rainfallStationService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private RoleApplyService roleApplyService;

    @Autowired
    private AreaManager areaManager;

    @Autowired
    private FileTreeService fileTreeService;

    @Autowired
    private PermissionApi permissionApi;

    @Resource
    private StatisticalInfoService statisticalInfoService;

    @Resource
    private StatisticalInfoMapper statisticalInfoMapper;

    @Resource
    private StatisticalDirectoryMapper statisticalDirectoryMapper;
    @Resource
    private StationMapper stationMapper;
    @Autowired
    private StationService stationService;

    @Resource
    private ResourceSiteManagementMapper resourceSiteManagementMapper;

    @Resource
    private StationYearService stationYearService;

    @Autowired
    private AreaApi areaApi;
    @Autowired
    private EngineeringProjectMapper engineeringProjectMapper;

    @Autowired
    private EngineeringProjectParametersMapper engineeringProjectParametersMapper;

    @Override
    public List<StationInfoRespVO> findStationInfoResp(Integer pageType) {
        List<StationInfoRespVO> stationInfoList = new ArrayList<>();
        List<Long> rainStationIdList = new ArrayList<>(List.of(-1L));
        List<Long> weatherStationIdList = new ArrayList<>(List.of(-1L));
        List<Long> hydrologicStationIdList = new ArrayList<>(List.of(-1L));
        boolean superAdmin = false;
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());
        if (!CollectionUtils.isEmpty(roleList)) {
            for (RoleRespDTO role : roleList) {
                if (RoleUtil.isAdminRole(role.getCode())) {
                    superAdmin = true;
                    break;
                }
                rainStationIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.STATION_RAINFALL.getType()));
                weatherStationIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.STATION_WEATHER.getType()));
                hydrologicStationIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.STATION_HYDROLOGIC.getType()));
                rainStationIdList = rainStationIdList.stream().distinct().collect(Collectors.toList());
                weatherStationIdList = weatherStationIdList.stream().distinct().collect(Collectors.toList());
                hydrologicStationIdList = hydrologicStationIdList.stream().distinct().collect(Collectors.toList());
            }
        }
        //查找雨量站
        if (findPermissionByRoleId(WebFrameworkUtils.getLoginUserId(), superAdmin, Objects.equals(1, pageType) ? PermissionStationEnum.STATION_RAINFALL_DATA_WRITE.getPermissionKey() : PermissionStationEnum.STATION_RAINFALL_DATA_SEARCH.getPermissionKey())) {
            List<RainfallStationDO> rainfallStationDOList = rainfallStationService.findAllRainfallStation();
            StationInfoRespVO rainFallStation = new StationInfoRespVO();
            rainFallStation.setStationType(StationEnum.RAIN.getType());
            rainFallStation.setStationId(StationEnum.RAIN.getCode());
            rainFallStation.setStationName(StationEnum.RAIN.getDesc());
            rainFallStation.setId(100L);
            if (!superAdmin) {
                List<Long> finalRainStationIdList = rainStationIdList;
                rainfallStationDOList = rainfallStationDOList.stream().filter(item -> finalRainStationIdList.contains(item.getId())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(rainfallStationDOList)) {
                AtomicLong startId = new AtomicLong(1000000L);
                rainFallStation.setChildren(rainfallStationDOList.stream().map(item -> {
                    StationInfoRespVO.StationInfo stationInfo = new StationInfoRespVO.StationInfo();
                    stationInfo.setStationId(item.getId());
                    stationInfo.setId(startId.getAndIncrement());
                    stationInfo.setStationName(item.getHydrologicName());
                    return stationInfo;
                }).collect(Collectors.toList()));
            } else {
                rainFallStation.setChildren(new ArrayList<>());
            }
            stationInfoList.add(rainFallStation);
        }
        if (findPermissionByRoleId(WebFrameworkUtils.getLoginUserId(), superAdmin, Objects.equals(1, pageType) ? PermissionStationEnum.STATION_HYDROLOGIC_DATA_WRITE.getPermissionKey() : PermissionStationEnum.STATION_HYDROLOGIC_DATA_SEARCH.getPermissionKey())) {
            //查找水文站
            List<HydrologicStationDO> hydrologicStationDOList = hydrologicStationService.findAllHydrologicStation();
            StationInfoRespVO hydrologicStation = new StationInfoRespVO();
            hydrologicStation.setStationType(StationEnum.HYDROLOGIC.getType());
            hydrologicStation.setStationId(StationEnum.HYDROLOGIC.getCode());
            hydrologicStation.setId(200L);
            hydrologicStation.setStationName(StationEnum.HYDROLOGIC.getDesc());
            if (!superAdmin) {
                List<Long> finalHydrologicStationIdList = hydrologicStationIdList;
                hydrologicStationDOList = hydrologicStationDOList.stream().filter(item -> finalHydrologicStationIdList.contains(item.getId())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(hydrologicStationDOList)) {
                AtomicLong startId = new AtomicLong(2000000L);
                hydrologicStation.setChildren(hydrologicStationDOList.stream().map(item -> {
                    StationInfoRespVO.StationInfo stationInfo = new StationInfoRespVO.StationInfo();
                    stationInfo.setStationId(item.getId());
                    stationInfo.setId(startId.getAndIncrement());
                    stationInfo.setStationName(item.getHydrologicName());
                    return stationInfo;
                }).collect(Collectors.toList()));
            } else {
                hydrologicStation.setChildren(new ArrayList<>());
            }
            stationInfoList.add(hydrologicStation);
        }
        //查找气象站
        if (findPermissionByRoleId(WebFrameworkUtils.getLoginUserId(), superAdmin, Objects.equals(1, pageType) ? PermissionStationEnum.STATION_WEATHER_DATA_WRITE.getPermissionKey() : PermissionStationEnum.STATION_WEATHER_DATA_SEARCH.getPermissionKey())) {
            List<WeatherStationDO> weatherStationDOList = weatherStationService.findAllWeatherStation();
            StationInfoRespVO weatherStation = new StationInfoRespVO();
            weatherStation.setStationType(StationEnum.WEATHER.getType());
            weatherStation.setStationId(StationEnum.WEATHER.getCode());
            weatherStation.setStationName(StationEnum.WEATHER.getDesc());
            weatherStation.setId(300L);
            if (!superAdmin) {
                List<Long> finalWeatherStationIdList = weatherStationIdList;
                weatherStationDOList = weatherStationDOList.stream().filter(item -> finalWeatherStationIdList.contains(item.getId())).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(weatherStationDOList)) {
                AtomicLong startId = new AtomicLong(3000000);
                weatherStation.setChildren(weatherStationDOList.stream().map(item -> {
                    StationInfoRespVO.StationInfo stationInfo = new StationInfoRespVO.StationInfo();
                    stationInfo.setStationId(item.getId());
                    stationInfo.setId(startId.getAndIncrement());
                    stationInfo.setStationName(item.getHydrologicName());
                    return stationInfo;
                }).collect(Collectors.toList()));
            } else {
                weatherStation.setChildren(new ArrayList<>());
            }
            stationInfoList.add(weatherStation);
        }
        return stationInfoList;
    }


    @Override
    public List<StationInfoRespIndexVO> findStationInfoRespV2(IndexReqVO indexReqVO) {
        String pageType=indexReqVO.getPageType();
        if(indexReqVO==null){
            indexReqVO=new IndexReqVO();
        }
        MPJLambdaWrapper<StationDO> lam=new MPJLambdaWrapper<StationDO>();
        if(indexReqVO.getName()!=null&&!indexReqVO.getName().isEmpty()){
            lam.like(StationDO::getHydrologicName, indexReqVO.getName());
        }
        if(indexReqVO.getCountry()!=null&&!indexReqVO.getCountry().isEmpty()){
            lam.eq(StationDO::getCountry, indexReqVO.getCountry());
        }
        if(indexReqVO.getProvince()!=null&&!indexReqVO.getProvince().isEmpty()){
            lam.eq(StationDO::getProvince, indexReqVO.getProvince());
        }
        if(indexReqVO.getCity()!=null&&!indexReqVO.getCity().isEmpty()){
            lam.eq(StationDO::getCity, indexReqVO.getCity());
        }
        List<String> stationType=new ArrayList<>();
        List<String> hydrologicType=new ArrayList<>();
        if(indexReqVO.getStationType()!=null&&!indexReqVO.getStationType().isEmpty()){
            String[] stationTypes=indexReqVO.getStationType().split(",");
            for(String type:stationTypes){
                if(type.equals(StationTypeEnum.RESERVOIR.getType().toString())){
                    type="2";
                    hydrologicType.add("水库");
                }else if(type.equals(StationTypeEnum.WATER_LEVEL.getType().toString())){
                    type="2";
                    hydrologicType.add("水位");
                }else if(type.equals(StationTypeEnum.HYDROLOGIC.getType().toString())){
                    type="2";
                    hydrologicType.add("水文");
                }
                stationType.add(type);
            }
        }
        if(!stationType.isEmpty()){
            lam.in(StationDO::getStationType, stationType);
        }
        if(indexReqVO.getMonLengthList()!=null){
            for(IndexReqVO.monLength monLength:indexReqVO.getMonLengthList()){
                if((monLength.getMonitoringItems()==null||monLength.getMonitoringItems().isEmpty())&&(monLength.getSeriesLength()!=null&&!monLength.getSeriesLength().isEmpty())){
                    throw exception(new ErrorCode(500, "请选择监测项目"));
                }
                if(monLength.getSeriesLength()==null||monLength.getSeriesLength().isEmpty()){
                    monLength.setSeriesLength("1");
                }
                MPJLambdaWrapper<StatisticalInfoDO> lamInfo=new MPJLambdaWrapper<StatisticalInfoDO>();
                lamInfo.select(StatisticalInfoDO::getStationId);
                lamInfo.leftJoin(StatisticalDirectoryDO.class,StatisticalDirectoryDO::getId,StatisticalInfoDO::getStatsId);
                lamInfo.ge(StatisticalInfoDO::getYearCount,monLength.getSeriesLength());
                lamInfo.eq(StatisticalDirectoryDO::getName,HydrologicDataProjectEnum.getDescByCode(Integer.parseInt(monLength.getMonitoringItems())));
                List<StatisticalInfoDO> stationId=statisticalInfoMapper.selectList(lamInfo);
                List<Long>  stationIdList=stationId.stream().map(StatisticalInfoDO::getStationId).distinct().toList();
                if(stationIdList!=null&&!stationIdList.isEmpty()){
                    lam.in(StationDO::getId, stationIdList);
                }

            }
        }
        StringBuffer  lastSql=new StringBuffer();
        if(!hydrologicType.isEmpty()){
            lastSql.append("AND ( ");
            for(int i=0;i<hydrologicType.size();i++){
                if(i<=hydrologicType.size()-2){
                    lastSql.append(" hydrologic_type = '"+hydrologicType.get(i)+"' or ");
                }else{
                    lastSql.append(" hydrologic_type = '"+hydrologicType.get(i)+"'");
                }
            }
            lastSql.append(" ) ");
        }
        lastSql.append(" GROUP BY t.id");
        if(indexReqVO.getStationIdList()!=null&& !indexReqVO.getStationIdList().isEmpty()){
            lam.in(StationDO::getId, indexReqVO.getStationIdList());
        }
        if(!lastSql.isEmpty()){
            lam.last(lastSql.toString());
        }
        List<StationInfoRespIndexVO> stationInfoList = new ArrayList<>();
        List<Long> rainStationIdList = new ArrayList<>(List.of(-1L));
        List<Long> weatherStationIdList = new ArrayList<>(List.of(-1L));
        List<Long> hydrologicStationIdList = new ArrayList<>(List.of(-1L));
        boolean superAdmin = false;
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());
        if (!CollectionUtils.isEmpty(roleList)) {
            for (RoleRespDTO role : roleList) {
                if (RoleUtil.isAdminRole(role.getCode())) {
                    superAdmin = true;
                    break;
                }
                rainStationIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.STATION_RAINFALL.getType()));
                weatherStationIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.STATION_WEATHER.getType()));
                hydrologicStationIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.STATION_HYDROLOGIC.getType()));
                rainStationIdList = rainStationIdList.stream().distinct().collect(Collectors.toList());
                weatherStationIdList = weatherStationIdList.stream().distinct().collect(Collectors.toList());
                hydrologicStationIdList = hydrologicStationIdList.stream().distinct().collect(Collectors.toList());
            }
        }
//        List<StationDO> stationList = stationService.listAll();
        List<StationDO> stationList = stationMapper.selectList(lam);
        Map<Integer, List<StationDO>> stationGroupByStationType = new HashMap<>();
        if (CollectionUtil.isNotEmpty(stationList)) {
            stationGroupByStationType = stationList.stream().collect(Collectors.groupingBy(StationDO::getStationType));
        }

        //查找雨量站
        if (findPermissionByRoleId(WebFrameworkUtils.getLoginUserId(), superAdmin, Objects.equals(1, pageType) ? PermissionStationEnum.STATION_RAINFALL_DATA_WRITE.getPermissionKey() : PermissionStationEnum.STATION_RAINFALL_DATA_SEARCH.getPermissionKey())) {
            List<StationDO> rainfallStationDOList = stationGroupByStationType.get(StationEnum.RAIN.getType());
            StationInfoRespIndexVO rainFallStation = new StationInfoRespIndexVO();
            rainFallStation.setStationType(StationEnum.RAIN.getType());
            rainFallStation.setStationId(StationEnum.RAIN.getCode());
            rainFallStation.setStationName(StationEnum.RAIN.getDesc());
            rainFallStation.setId("100");
            stationInfoList.add(rainFallStation);
            if (CollectionUtil.isNotEmpty(rainfallStationDOList)) {
                if (!superAdmin) {
                    List<Long> finalRainStationIdList = rainStationIdList;
                    rainfallStationDOList = rainfallStationDOList.stream().filter(item -> finalRainStationIdList.contains(item.getId())).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(rainfallStationDOList)) {
                    AtomicLong startId = new AtomicLong(1000000L);
                    //查询雨量站所有的国家ID
//                    List<Long> countryList=stationService.getAllCountry(StationEnum.RAIN.getType());
                    List<Long> countryList1=rainfallStationDOList.stream()
                            .map(StationDO::getCountry)
                            .map(Long::valueOf)
                            .distinct().toList();
                    Set<Long> countrySet = new HashSet<>(countryList1);
                    List<Long> countryList = new ArrayList<>(countrySet);
                    List<StationInfoRespIndexVO.StationInfo> countryInfoList = new ArrayList<>();
                    countryList.sort(Collections.reverseOrder());
                    for(Long countryId:countryList){
                        StationInfoRespIndexVO.StationInfo countryInfo = new StationInfoRespIndexVO.StationInfo();
                        countryInfo.setStationId(IdUtil.getSnowflakeNextId() + "");
                        countryInfo.setId(startId.getAndIncrement() + "");
                        if(countryId!=null){
                            countryInfo.setStationName(areaManager.getCountryNameByCode(countryId.toString().trim()));
                        }
                        if(countryId==193){
                            List<Long> provinceList1 = rainfallStationDOList.stream()
                                    .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                    .map(StationDO::getProvince)
                                    .map(Long::valueOf)
                                    .distinct().toList();
                            Set<Long> provinceSet = new HashSet<>(provinceList1);
                            List<Long> provinceList = new ArrayList<>(provinceSet);
                            if (!CollectionUtils.isEmpty(provinceList)) {
                                Collections.sort(provinceList);
                                List<StationInfoRespIndexVO.StationInfo> provinceItemList = new ArrayList<>();
                                for(Long provinceId:provinceList){
                                    StationInfoRespIndexVO.StationInfo provinceInfo = new StationInfoRespIndexVO.StationInfo();
                                    provinceInfo.setStationId(IdUtil.getSnowflakeNextId() + "");
                                    provinceInfo.setId(startId.getAndIncrement() + "");
                                    provinceInfo.setStationName(areaManager.getProvinceNameByCode(provinceId.toString().trim()));
                                    List<Long> cityIds1=rainfallStationDOList.stream()
                                            .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                            .filter(item -> provinceId.equals(Long.valueOf(item.getProvince().trim())))
                                            .map(StationDO::getCity)
                                            .map(Long::valueOf)
                                            .distinct().toList();
                                    Set<Long> citySet = new HashSet<>(cityIds1);
                                    List<Long> cityIds = new ArrayList<>(citySet);
                                    if (!CollectionUtils.isEmpty(cityIds)) {
                                        Collections.sort(cityIds);
                                        List<StationInfoRespIndexVO.StationInfo> cityItemList = new ArrayList<>();
                                        for(Long cityId:cityIds){
                                            StationInfoRespIndexVO.StationInfo cityInfo = new StationInfoRespIndexVO.StationInfo();
                                            cityInfo.setStationId(IdUtil.getSnowflakeNextId() + "");
                                            cityInfo.setId(startId.getAndIncrement() + "");
                                            cityInfo.setStationName(areaManager.getCityNameByCode(cityId.toString().trim()));
                                            List<StationDO> stationDOList = rainfallStationDOList.stream()
                                                    .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                                    .filter(item -> provinceId.equals(Long.valueOf(item.getProvince().trim())))
                                                    .filter(item -> cityId.equals(Long.valueOf(item.getCity().trim())))
                                                    .toList();
                                            if (!CollectionUtils.isEmpty(stationDOList)) {
                                                List<StationInfoRespIndexVO.StationInfo> stationItemList = new ArrayList<>();
                                                for(StationDO stationDO:stationDOList){
                                                    StationInfoRespIndexVO.StationInfo stationInfo = new StationInfoRespIndexVO.StationInfo();
                                                    stationInfo.setStationId(stationDO.getId().toString());
                                                    stationInfo.setId(startId.getAndIncrement() + "");
                                                    stationInfo.setStationName(stationDO.getHydrologicName());
                                                    stationItemList.add(stationInfo);
                                                }
                                                cityInfo.setChildren(stationItemList);
                                            }
                                            cityItemList.add(cityInfo);
                                        }
                                        provinceInfo.setChildren(cityItemList);
                                    }
                                    provinceItemList.add(provinceInfo);
                                }
                                countryInfo.setChildren(provinceItemList);
                            }
                        }else{
                            List<StationDO> stationDOList =  rainfallStationDOList.stream()
                                    .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                    .toList();
                            if (!CollectionUtils.isEmpty(stationDOList)) {
                                List<StationInfoRespIndexVO.StationInfo> stationItemList = new ArrayList<>();
                                stationDOList.stream().forEach(stationDO -> {
                                    StationInfoRespIndexVO.StationInfo stationInfo = new StationInfoRespIndexVO.StationInfo();
                                    stationInfo.setStationId(stationDO.getId().toString());
                                    stationInfo.setId(startId.getAndIncrement() + "");
                                    stationInfo.setStationName(stationDO.getHydrologicName());
                                    stationItemList.add(stationInfo);
                                });
                                countryInfo.setChildren(stationItemList);
                            }
                        }
                        countryInfoList.add(countryInfo);
                    }
                    rainFallStation.setChildren(countryInfoList);
                } else {
                    rainFallStation.setChildren(new ArrayList<>());
                }
            }
        }
        if (findPermissionByRoleId(WebFrameworkUtils.getLoginUserId(), superAdmin, Objects.equals(1, pageType) ? PermissionStationEnum.STATION_HYDROLOGIC_DATA_WRITE.getPermissionKey() : PermissionStationEnum.STATION_HYDROLOGIC_DATA_SEARCH.getPermissionKey())) {
            //查找水文站
            List<StationDO> hydrologicStationDOList = stationGroupByStationType.get(StationEnum.HYDROLOGIC.getType());
            StationInfoRespIndexVO hydrologicStation = new StationInfoRespIndexVO();
            hydrologicStation.setStationType(StationEnum.HYDROLOGIC.getType());
            hydrologicStation.setStationId(StationEnum.HYDROLOGIC.getCode());
            hydrologicStation.setId("200");
            hydrologicStation.setStationName(StationEnum.HYDROLOGIC.getDesc());
            stationInfoList.add(hydrologicStation);

            if (CollectionUtil.isNotEmpty(hydrologicStationDOList)) {
                if (!superAdmin) {
                    List<Long> finalHydrologicStationIdList = hydrologicStationIdList;
                    hydrologicStationDOList = hydrologicStationDOList.stream().filter(item -> finalHydrologicStationIdList.contains(item.getId())).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(hydrologicStationDOList)) {
                    AtomicLong startId = new AtomicLong(2000000L);
                    //查询水文站所有的国家ID
                    List<Long> countryList1=hydrologicStationDOList.stream()
                            .map(StationDO::getCountry)
                            .map(Long::valueOf)
                            .distinct().toList();
                    Set<Long> countrySet = new HashSet<>(countryList1);
                    List<Long> countryList = new ArrayList<>(countrySet);
                    List<StationInfoRespIndexVO.StationInfo> InfoList = new ArrayList<>();
                    countryList.sort(Collections.reverseOrder());
                    for(Long countryId:countryList){
                        StationInfoRespIndexVO.StationInfo info = new StationInfoRespIndexVO.StationInfo();
                        info.setStationId(IdUtil.getSnowflakeNextId() + "");
                        info.setId(startId.getAndIncrement() + "");
                        if(countryId!=null){
                            info.setStationName(areaManager.getCountryNameByCode(countryId.toString().trim()));
                        }
                        if(countryId==193){
                            List<Long> provinceList1 = hydrologicStationDOList.stream()
                                    .filter(item -> !item.getCountry().isEmpty())
                                    .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                    .map(StationDO::getProvince)
                                    .map(Long::valueOf)
                                    .distinct().toList();
                            Set<Long> provinceSet = new HashSet<>(provinceList1);
                            List<Long> provinceList = new ArrayList<>(provinceSet);
                            if (!CollectionUtils.isEmpty(provinceList)) {
                                Collections.sort(provinceList);
                                List<StationInfoRespIndexVO.StationInfo> provinceItemList = new ArrayList<>();
                                for(Long provinceId:provinceList){
                                    StationInfoRespIndexVO.StationInfo provinceInfo = new StationInfoRespIndexVO.StationInfo();
                                    provinceInfo.setStationId(IdUtil.getSnowflakeNextId() + "");
                                    provinceInfo.setId(startId.getAndIncrement() + "");
                                    provinceInfo.setStationName(areaManager.getProvinceNameByCode(provinceId.toString().trim()));
                                    List<Long> cityIds1=hydrologicStationDOList.stream()
                                            .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                            .filter(item -> provinceId.equals(Long.valueOf(item.getProvince().trim())))
                                            .map(StationDO::getCity)
                                            .map(Long::valueOf)
                                            .distinct().toList();
                                    Set<Long> citySet = new HashSet<>(cityIds1);
                                    List<Long> cityIds = new ArrayList<>(citySet);
                                    if (!CollectionUtils.isEmpty(cityIds)) {
                                        Collections.sort(cityIds);
                                        List<StationInfoRespIndexVO.StationInfo> cityItemList = new ArrayList<>();
                                        for(Long cityId:cityIds){
                                            StationInfoRespIndexVO.StationInfo cityInfo = new StationInfoRespIndexVO.StationInfo();
                                            cityInfo.setStationId(IdUtil.getSnowflakeNextId() + "");
                                            cityInfo.setId(startId.getAndIncrement() + "");
                                            cityInfo.setStationName(areaManager.getCityNameByCode(cityId.toString().trim()));
                                            List<StationDO> stationDOList = hydrologicStationDOList.stream()
                                                    .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                                    .filter(item -> provinceId.equals(Long.valueOf(item.getProvince().trim())))
                                                    .filter(item -> cityId.equals(Long.valueOf(item.getCity().trim())))
                                                    .toList();
                                            if (!CollectionUtils.isEmpty(stationDOList)) {
                                                List<StationInfoRespIndexVO.StationInfo> stationItemList = new ArrayList<>();
                                                for(StationDO stationDO:stationDOList){
                                                    StationInfoRespIndexVO.StationInfo stationInfo = new StationInfoRespIndexVO.StationInfo();
                                                    stationInfo.setStationId(stationDO.getId().toString());
                                                    stationInfo.setId(startId.getAndIncrement() + "");
                                                    stationInfo.setStationName(stationDO.getHydrologicName());
                                                    stationItemList.add(stationInfo);
                                                }
                                                cityInfo.setChildren(stationItemList);
                                            }
                                            cityItemList.add(cityInfo);
                                        }
                                        provinceInfo.setChildren(cityItemList);
                                    }
                                    provinceItemList.add(provinceInfo);
                                }
                                info.setChildren(provinceItemList);
                            }
                        }else{
                            List<StationDO> stationDOList =  hydrologicStationDOList.stream()
                                    .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                    .toList();
                            if (!CollectionUtils.isEmpty(stationDOList)) {
                                List<StationInfoRespIndexVO.StationInfo> stationItemList = new ArrayList<>();
                                stationDOList.stream().forEach(stationDO -> {
                                    StationInfoRespIndexVO.StationInfo stationInfo = new StationInfoRespIndexVO.StationInfo();
                                    stationInfo.setStationId(stationDO.getId().toString());
                                    stationInfo.setId(startId.getAndIncrement() + "");
                                    stationInfo.setStationName(stationDO.getHydrologicName());
                                    stationItemList.add(stationInfo);
                                });
                                info.setChildren(stationItemList);
                            }
                        }
                        InfoList.add(info);
                    }
                    hydrologicStation.setChildren(InfoList);
                } else {
                    hydrologicStation.setChildren(new ArrayList<>());
                }
            }
        }
        //查找气象站
        if (findPermissionByRoleId(WebFrameworkUtils.getLoginUserId(), superAdmin, Objects.equals(1, pageType) ? PermissionStationEnum.STATION_WEATHER_DATA_WRITE.getPermissionKey() : PermissionStationEnum.STATION_WEATHER_DATA_SEARCH.getPermissionKey())) {
            List<StationDO> weatherStationDOList = stationGroupByStationType.get(StationEnum.WEATHER.getType());
            StationInfoRespIndexVO weatherStation = new StationInfoRespIndexVO();
            weatherStation.setStationType(StationEnum.WEATHER.getType());
            weatherStation.setStationId(StationEnum.WEATHER.getCode());
            weatherStation.setStationName(StationEnum.WEATHER.getDesc());
            weatherStation.setId("300");
            stationInfoList.add(weatherStation);
            if (CollectionUtil.isNotEmpty(weatherStationDOList)) {
                if (!superAdmin) {
                    List<Long> finalWeatherStationIdList = weatherStationIdList;
                    weatherStationDOList = weatherStationDOList.stream().filter(item -> finalWeatherStationIdList.contains(item.getId())).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(weatherStationDOList)) {
                    AtomicLong startId = new AtomicLong(2000000L);
                    //查询气象站所有的国家ID
                    List<Long> countryList1=weatherStationDOList.stream()
                            .map(StationDO::getCountry)
                            .map(Long::valueOf)
                            .distinct().toList();
                    Set<Long> countrySet = new HashSet<>(countryList1);
                    List<Long> countryList = new ArrayList<>(countrySet);
                    List<StationInfoRespIndexVO.StationInfo> InfoList = new ArrayList<>();
                    countryList.sort(Collections.reverseOrder());
                    for(Long countryId:countryList){
                        StationInfoRespIndexVO.StationInfo info = new StationInfoRespIndexVO.StationInfo();
                        info.setStationId(IdUtil.getSnowflakeNextId() + "");
                        info.setId(startId.getAndIncrement() + "");
                        if(countryId!=null){
                            info.setStationName(areaManager.getCountryNameByCode(countryId.toString().trim()));
                        }
                        if(countryId==193){
                            List<Long> provinceList1 = weatherStationDOList.stream()
                                    .filter(item -> !item.getCountry().isEmpty())
                                    .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                    .map(StationDO::getProvince)
                                    .map(Long::valueOf)
                                    .distinct().toList();
                            Set<Long> provinceSet = new HashSet<>(provinceList1);
                            List<Long> provinceList = new ArrayList<>(provinceSet);
                            if (!CollectionUtils.isEmpty(provinceList)) {
                                Collections.sort(provinceList);
                                List<StationInfoRespIndexVO.StationInfo> provinceItemList = new ArrayList<>();
                                for(Long provinceId:provinceList){
                                    StationInfoRespIndexVO.StationInfo provinceInfo = new StationInfoRespIndexVO.StationInfo();
                                    provinceInfo.setStationId(IdUtil.getSnowflakeNextId() + "");
                                    provinceInfo.setId(startId.getAndIncrement() + "");
                                    provinceInfo.setStationName(areaManager.getProvinceNameByCode(provinceId.toString().trim()));
                                    List<Long> cityIds1=weatherStationDOList.stream()
                                            .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                            .filter(item -> provinceId.equals(Long.valueOf(item.getProvince().trim())))
                                            .map(StationDO::getCity)
                                            .map(Long::valueOf)
                                            .distinct().toList();
                                    Set<Long> citySet = new HashSet<>(cityIds1);
                                    List<Long> cityIds = new ArrayList<>(citySet);
                                    if (!CollectionUtils.isEmpty(cityIds)) {
                                        Collections.sort(cityIds);
                                        List<StationInfoRespIndexVO.StationInfo> cityItemList = new ArrayList<>();
                                        for(Long cityId:cityIds){
                                            StationInfoRespIndexVO.StationInfo cityInfo = new StationInfoRespIndexVO.StationInfo();
                                            cityInfo.setStationId(IdUtil.getSnowflakeNextId() + "");
                                            cityInfo.setId(startId.getAndIncrement() + "");
                                            cityInfo.setStationName(areaManager.getCityNameByCode(cityId.toString().trim()));
                                            List<StationDO> stationDOList = weatherStationDOList.stream()
                                                    .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                                    .filter(item -> provinceId.equals(Long.valueOf(item.getProvince().trim())))
                                                    .filter(item -> cityId.equals(Long.valueOf(item.getCity().trim())))
                                                    .toList();
                                            if (!CollectionUtils.isEmpty(stationDOList)) {
                                                List<StationInfoRespIndexVO.StationInfo> stationItemList = new ArrayList<>();
                                                for(StationDO stationDO:stationDOList){
                                                    StationInfoRespIndexVO.StationInfo stationInfo = new StationInfoRespIndexVO.StationInfo();
                                                    stationInfo.setStationId(stationDO.getId().toString());
                                                    stationInfo.setId(startId.getAndIncrement() + "");
                                                    stationInfo.setStationName(stationDO.getHydrologicName());
                                                    stationItemList.add(stationInfo);
                                                }
                                                cityInfo.setChildren(stationItemList);
                                            }
                                            cityItemList.add(cityInfo);
                                        }
                                        provinceInfo.setChildren(cityItemList);
                                    }
                                    provinceItemList.add(provinceInfo);
                                }
                                info.setChildren(provinceItemList);
                            }
                        }else{
                            List<StationDO> stationDOList =  weatherStationDOList.stream()
                                    .filter(item -> countryId.equals(Long.valueOf(item.getCountry().trim())))
                                    .toList();
                            if (!CollectionUtils.isEmpty(stationDOList)) {
                                List<StationInfoRespIndexVO.StationInfo> stationItemList = new ArrayList<>();
                                stationDOList.stream().forEach(stationDO -> {
                                    StationInfoRespIndexVO.StationInfo stationInfo = new StationInfoRespIndexVO.StationInfo();
                                    stationInfo.setStationId(stationDO.getId().toString());
                                    stationInfo.setId(startId.getAndIncrement() + "");
                                    stationInfo.setStationName(stationDO.getHydrologicName());
                                    stationItemList.add(stationInfo);
                                });
                                info.setChildren(stationItemList);
                            }
                        }
                        InfoList.add(info);
                    }
                    weatherStation.setChildren(InfoList);
                } else {
                    weatherStation.setChildren(new ArrayList<>());
                }
            }
        }
        return stationInfoList;
    }

    @Override
    public List<Map<String, Object>> getStationType() {
        List<Map<String, Object>> list=new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            Map<String,Object> map=new HashMap<>();
            if(i==0){
                map.put("value","1");
                map.put("label","雨量站");
                map.put("children",new ArrayList<>());
            }else if(i==1){
                map.put("value","2");
                map.put("label","水文站");
                List<Map<String, Object>> list1=new ArrayList<>();
                for (int j = 0; j <3 ; j++) {
                    Map<String,Object> map1=new HashMap<>();
                    if(j==0){
                        map1.put("value",StationTypeEnum.RESERVOIR.getType().toString());
                        map1.put("label",StationTypeEnum.RESERVOIR.getDesc());
                    }else if(j==1){
                        map1.put("value",StationTypeEnum.WATER_LEVEL.getType().toString());
                        map1.put("label",StationTypeEnum.WATER_LEVEL.getDesc());
                    }else if(j==2){
                        map1.put("value",StationTypeEnum.HYDROLOGIC.getType().toString());
                        map1.put("label",StationTypeEnum.HYDROLOGIC.getDesc());
                    }
                    map1.put("children",new ArrayList<>());
                    list1.add(map1);
                }
                map.put("children",list1);
            }else if(i==2){
                map.put("value","3");
                map.put("label","气象站");
                map.put("children",new ArrayList<>());
            }
            list.add(map);
        }


        return list;
    }

    @Override
    public List<Map<String, Object>> getMonitoringItems(String value) {
        List<Map<String, Object>> list=new ArrayList<>();
        Map<String,Object> mapset=new HashMap<>();
        if(!value.isEmpty()){
            String[] str=value.split(",");
            for (String s : str) {
                String type=MonitoringItemEnum.getCodeByType(Integer.parseInt(s));
                for(String sr : type.split(",")){
                    Map<String,Object> map=new HashMap<>();
                    map.put("label",HydrologicDataProjectEnum.getDescByCode(Integer.parseInt(sr)));
                    map.put("value",sr);
                    if(mapset.get(sr)==null){
                        mapset.put(sr,map);
                        list.add(map);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 查找角色是否有相应权限
     *
     * @param userId
     * @param superAdmin
     * @param permission
     * @return
     */
    private boolean findPermissionByRoleId(Long userId, boolean superAdmin, String permission) {
        if (superAdmin) {
            return true;
        }
        try {
            CommonResult<Boolean> permissionResult = permissionApi.hasAnyPermissions(userId, permission);
            if (Objects.nonNull(permissionResult)) {
                return permissionResult.getData();
            }
        } catch (Exception e) {
            log.error("findPermissionByRoleId--->error.", e);
        }
        return false;
    }

    @Deprecated
    @Override
    public IndexInfoRespVO findIndexItemInfoResp() {
        IndexInfoRespVO indexInfoRespVO = new IndexInfoRespVO();
        List<IndexInfoRespVO.RainFallStation> rainFallStationList = new ArrayList<>();
        //查找雨量站
        List<RainfallStationDO> rainfallStationDOList = rainfallStationService.findAllRainfallStation();
        if (!CollectionUtils.isEmpty(rainfallStationDOList)) {
            rainFallStationList.addAll(rainfallStationDOList.stream().map(item -> {
                IndexInfoRespVO.RainFallStation stationInfo = new IndexInfoRespVO.RainFallStation();

                stationInfo.setStationId(item.getId());
                stationInfo.setStationName(item.getHydrologicName());
                stationInfo.setHydrologicCode(item.getHydrologicCode());
                stationInfo.setRiverSystem(item.getRiverSystem());
                stationInfo.setRiverName(item.getRiverName());
                stationInfo.setSectionAddress(findAddress(item.getProvince(), item.getCity(), item.getCounty()));
                stationInfo.setLongitude(item.getLongitude());
                stationInfo.setLatitude(item.getLatitude());
                stationInfo.setEstablishDate(generateEstablishDate(item.getYear(), item.getMonth()));
                return stationInfo;
            }).toList());
        }
        indexInfoRespVO.setRainFallStationList(rainFallStationList);
        //查找水文站
        List<HydrologicStationDO> hydrologicStationDOList = hydrologicStationService.findAllHydrologicStation();
        List<IndexInfoRespVO.HydrologicStation> HydrologicStationList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(hydrologicStationDOList)) {
            HydrologicStationList.addAll(hydrologicStationDOList.stream().map(item -> {
                IndexInfoRespVO.HydrologicStation stationInfo = new IndexInfoRespVO.HydrologicStation();
                stationInfo.setDataAge(item.getDataAge());
                stationInfo.setDataYear("");
                if (StringUtils.isNotEmpty(item.getDataStartYear()) && StringUtils.isNotEmpty(item.getDataEndYear())) {
                    stationInfo.setDataYear(item.getDataStartYear() + "-" + item.getDataEndYear());
                }
                stationInfo.setStationId(item.getId());
                stationInfo.setStationName(item.getHydrologicName());
                stationInfo.setHydrologicCode(item.getHydrologicCode());
                stationInfo.setRiverSystem(item.getRiverSystem());
                stationInfo.setRiverName(item.getRiverName());
                stationInfo.setSectionAddress(findAddress(item.getProvince(), item.getCity(), item.getCounty()));
                stationInfo.setLongitude(item.getLongitude());
                stationInfo.setLatitude(item.getLatitude());
                stationInfo.setEstablishDate(generateEstablishDate(item.getYear(), item.getMonth()));
                return stationInfo;
            }).toList());
        }
        indexInfoRespVO.setHydrologicStationList(HydrologicStationList);
        //查找气象站
        List<WeatherStationDO> weatherStationDOList = weatherStationService.findAllWeatherStation();
        List<IndexInfoRespVO.WeatherStation> weatherStationList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(weatherStationDOList)) {
            weatherStationList.addAll(weatherStationDOList.stream().map(item -> {
                IndexInfoRespVO.WeatherStation stationInfo = new IndexInfoRespVO.WeatherStation();
                stationInfo.setDataAge(item.getDataAge());
                stationInfo.setDataYear("");
                if (StringUtils.isNotEmpty(item.getDataStartYear()) && StringUtils.isNotEmpty(item.getDataEndYear())) {
                    stationInfo.setDataYear(item.getDataStartYear() + "-" + item.getDataEndYear());
                }
                stationInfo.setStationId(item.getId());
                stationInfo.setStationName(item.getHydrologicName());
                stationInfo.setLongitude(item.getLongitude());
                stationInfo.setLatitude(item.getLatitude());
                stationInfo.setAltitude(item.getAltitude());
//                stationInfo.setObservationTime(item.getStartObservationTime());
                stationInfo.setMissingTestingTimePeriod(item.getMissingTestingTimePeriod());
                stationInfo.setObservationMethod(item.getObservationMethod());
                return stationInfo;
            }).toList());
        }
        indexInfoRespVO.setWeatherStationList(weatherStationList);
        //查找项目列表
        List<Long> projectIdList = new ArrayList<>(List.of(-1L));
        boolean superAdmin = false;
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());
        if (!CollectionUtils.isEmpty(roleList)) {
            for (RoleRespDTO role : roleList) {
                if (RoleUtil.isAdminRole(role.getCode())) {
                    superAdmin = true;
                    break;
                }
                projectIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.PROJECT.getType()));
            }
        }
        projectIdList = projectIdList.stream().distinct().collect(Collectors.toList());
        //查找当前用户分配的项目列表
        List<ProjectDO> projectDOList = projectService.findAllProject();
        List<IndexInfoRespVO.ProjectInfo> projectInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(projectDOList)) {
            boolean finalSuperAdmin = superAdmin;
            List<Long> finalProjectIdList = projectIdList;
            projectInfoList.addAll(projectDOList.stream().map(item -> {
                IndexInfoRespVO.ProjectInfo projectInfo = new IndexInfoRespVO.ProjectInfo();
                projectInfo.setProjectId(item.getId());
                projectInfo.setPowerStationName(item.getPowerStationName());
                projectInfo.setArea(findAddress(item.getProvince(), item.getCity(), item.getCounty()));
                projectInfo.setLongitude(item.getLongitude());
                projectInfo.setLatitude(item.getLatitude());
                projectInfo.setDesignStage(item.getDesignStage());
                projectInfo.setShowFileTree((finalSuperAdmin || finalProjectIdList.contains(item.getId())) ? FileTreeShowEnum.SHOW.getType() : FileTreeShowEnum.HIDE.getType());
                FileTreeDO fileTreeDO = fileTreeService.selectFirstLevelFileTreeDO(item.getId(),"1");
                projectInfo.setId(Objects.nonNull(fileTreeDO) ? fileTreeDO.getId() : null);
                return projectInfo;
            }).toList());
        }
        indexInfoRespVO.setProjectInfoList(projectInfoList);
        return indexInfoRespVO;
    }


    @Override
    public IndexInfoRespVO findIndexItemInfoRespV2(IndexReqVO indexReqVO) {
        IndexInfoRespVO indexInfoRespVO = new IndexInfoRespVO();
        if(indexReqVO==null){
            indexReqVO=new IndexReqVO();
        }

        // 角色权限适用项目map(key:绑定类型; value:绑定数据id列表)
        Map<Integer, Set<Long>> bizIdMap = new HashMap<>();

        // 获取当前用户的角色
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());

        // 获取当前用户是否为超级管理员
        boolean superAdmin = false;
        for (RoleRespDTO roleRespDTO : roleList) {
            if (RoleUtil.isAdminRole(roleRespDTO.getCode())) {
                superAdmin = true;
                break;
            }
        }

        // 获取当前用户所属的角色选择的适用项目
        Set<Long> roleIdSet = roleList.stream().map(temp->temp.getId()).collect(Collectors.toSet());
        List<RoleApplyDO> roleApplyDOList = roleApplyService.listByRoleIdSet(roleIdSet);

        // 不同角色可能绑定了同一个项目，需要把不同角色的适用项目按绑定类型分组，每组的数据去重
        roleApplyDOList.stream().collect(Collectors.groupingBy(RoleApplyDO::getBindType)).forEach((bindType, tempRoleApplyDOList)->{
            Set<Long> bizIdSet = Optional.ofNullable(bizIdMap.get(bindType)).orElse(new HashSet<>());
            tempRoleApplyDOList.forEach(tempRoleApplyDO -> {
                Arrays.stream(Optional.ofNullable(tempRoleApplyDO.getBizId()).orElse("").split(",")).forEach(bizId->{
                    if (StringUtils.isNotBlank(bizId)) {
                        bizIdSet.add(Long.parseLong(bizId));
                    }
                });
            });
            bizIdMap.put(bindType, bizIdSet);
        });

        if (!StringUtils.isEmpty(indexReqVO.getSearchType())) {
            if (SearchTypeEnum.HYDROLOGY_DATA.getType()==Integer.parseInt(indexReqVO.getSearchType())) {
                MPJLambdaWrapper<StationDO> lam = new MPJLambdaWrapper<StationDO>();
                if (indexReqVO.getName() != null && !indexReqVO.getName().isEmpty()) {
                    lam.like(StationDO::getHydrologicName, indexReqVO.getName());
                }
                if (indexReqVO.getCountry() != null && !indexReqVO.getCountry().isEmpty()) {
                    lam.eq(StationDO::getCountry, indexReqVO.getCountry());
                }
                if (indexReqVO.getProvince() != null && !indexReqVO.getProvince().isEmpty()) {
                    lam.eq(StationDO::getProvince, indexReqVO.getProvince());
                }
                if (indexReqVO.getCity() != null && !indexReqVO.getCity().isEmpty()) {
                    lam.eq(StationDO::getCity, indexReqVO.getCity());
                }
                List<String> stationType=new ArrayList<>();
                List<String> hydrologicType=new ArrayList<>();
                if(indexReqVO.getStationType()!=null&&!indexReqVO.getStationType().isEmpty()){
                    String[] stationTypes=indexReqVO.getStationType().split(",");
                    for(String type:stationTypes){
                        if(type.equals(StationTypeEnum.RESERVOIR.getType().toString())){
                            type="2";
                            hydrologicType.add("水库");
                        } else if (type.equals(StationTypeEnum.WATER_LEVEL.getType().toString())) {
                            type = "2";
                            hydrologicType.add("水位");
                        } else if (type.equals(StationTypeEnum.HYDROLOGIC.getType().toString())) {
                            type = "2";
                            hydrologicType.add("水文");
                        }
                        stationType.add(type);
                    }
                }
                if (!stationType.isEmpty()) {
                    lam.in(StationDO::getStationType, stationType);
                }
                if(indexReqVO.getMonLengthList()!=null){
                    for(IndexReqVO.monLength monLength:indexReqVO.getMonLengthList()){
                        if((monLength.getMonitoringItems()==null||monLength.getMonitoringItems().isEmpty())&&(monLength.getSeriesLength()!=null&&!monLength.getSeriesLength().isEmpty())){
                            throw exception(new ErrorCode(500, "请选择监测项目"));
                        }
                        if(monLength.getSeriesLength()==null||monLength.getSeriesLength().isEmpty()){
                            monLength.setSeriesLength("1");
                        }
                        MPJLambdaWrapper<StatisticalInfoDO> lamInfo=new MPJLambdaWrapper<StatisticalInfoDO>();
                        lamInfo.select(StatisticalInfoDO::getStationId);
                        lamInfo.leftJoin(StatisticalDirectoryDO.class,StatisticalDirectoryDO::getId,StatisticalInfoDO::getStatsId);
                        lamInfo.ge(StatisticalInfoDO::getYearCount,monLength.getSeriesLength());
                        lamInfo.eq(StatisticalDirectoryDO::getName,HydrologicDataProjectEnum.getDescByCode(Integer.parseInt(monLength.getMonitoringItems())));
                        List<StatisticalInfoDO> stationId=statisticalInfoMapper.selectList(lamInfo);
                        List<Long>  stationIdList=stationId.stream().map(StatisticalInfoDO::getStationId).distinct().toList();
                        if(stationIdList!=null&&!stationIdList.isEmpty()){
                            lam.in(StationDO::getId, stationIdList);
                        }
                    }
                }
                StringBuffer  lastSql=new StringBuffer();
                if(!hydrologicType.isEmpty()){
                    lastSql.append("AND ( ");
                    for(int i=0;i<hydrologicType.size();i++){
                        if(i<=hydrologicType.size()-2){
                            lastSql.append(" hydrologic_type = '"+hydrologicType.get(i)+"' or ");
                        }else{
                            lastSql.append(" hydrologic_type = '"+hydrologicType.get(i)+"'");
                        }
                    }
                    lastSql.append(" ) ");
                }
                lastSql.append(" GROUP BY t.id");
                if(indexReqVO.getStationIdList()!=null&& !indexReqVO.getStationIdList().isEmpty()){
                    lam.in(StationDO::getId, indexReqVO.getStationIdList());
                }
                if(!lastSql.isEmpty()){
                    lam.last(lastSql.toString());
                }
                List<StationDO> list = stationMapper.selectList(lam);

                // 根据配置的角色权限适用项目控制数据范围
                if (!superAdmin) {
                    Set<Long> bizIdSet = new HashSet<>();
                    bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.STATION_WEATHER.getType())).orElse(new HashSet<>()));
                    bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.STATION_RAINFALL.getType())).orElse(new HashSet<>()));
                    bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.STATION_HYDROLOGIC.getType())).orElse(new HashSet<>()));
                    list = list.stream().filter(temp->bizIdSet.contains(temp.getId())).collect(Collectors.toList());
                }

                Map<Integer, List<StationDO>> stationsMap = list.stream().collect(Collectors.groupingBy(StationDO::getStationType));
                if (CollectionUtil.isNotEmpty(list)) {
                    //查找雨量站
                    List<IndexInfoRespVO.RainFallStation> rainFallStationList = getRainDataAll(stationsMap);
                    indexInfoRespVO.setRainFallStationList(rainFallStationList);
                    //查找水文站
                    List<IndexInfoRespVO.HydrologicStation> hydrologicStations = getHydrologicStationData(stationsMap);
                    indexInfoRespVO.setHydrologicStationList(hydrologicStations);
                    //查找气象站
                    List<IndexInfoRespVO.WeatherStation> weatherStationList = getWeatherStationData(stationsMap);
                    indexInfoRespVO.setWeatherStationList(weatherStationList);
                }
            } else if (SearchTypeEnum.RESOURCE_SITE.getType()==Integer.parseInt(indexReqVO.getSearchType())) {
                LambdaQueryWrapperX<ResourceSiteManagementDO> lam = new LambdaQueryWrapperX<ResourceSiteManagementDO>();
                lam.likeIfPresent(ResourceSiteManagementDO::getPowerStationName, indexReqVO.getName())
                        .eqIfPresent(ResourceSiteManagementDO::getCountry, indexReqVO.getCountry())
                        .eqIfPresent(ResourceSiteManagementDO::getProvince, indexReqVO.getProvince())
                        .eqIfPresent(ResourceSiteManagementDO::getCity, indexReqVO.getCity())
                        .eqIfPresent(ResourceSiteManagementDO::getDesignStage, indexReqVO.getWorkDepth())
                        .geIfPresent(ResourceSiteManagementDO::getTotalCapacity, indexReqVO.getInstalledCapacityW())
                        .leIfPresent(ResourceSiteManagementDO::getTotalCapacity, indexReqVO.getInstalledCapacityM())
                        .eqIfPresent(ResourceSiteManagementDO::getFullShippingHours, indexReqVO.getContinuousHours());
                if (indexReqVO.getIsNaGui() != null && !indexReqVO.getIsNaGui().isEmpty()) {
                    if (indexReqVO.getIsNaGui().equals("1")) {
                        lam.eq(ResourceSiteManagementDO::getComplianceRegulations, "是");
                    } else {
                        lam.eq(ResourceSiteManagementDO::getComplianceRegulations, "否");
                    }
                }
                if (indexReqVO.getStationType() != null && !indexReqVO.getStationType().isEmpty() && indexReqVO.getStationType().equals("5")) {
                    lam.eqIfPresent(ResourceSiteManagementDO::getPowerStationType, indexReqVO.getStationType());
                } else if (indexReqVO.getStationType() != null && !indexReqVO.getStationType().isEmpty()) {
                    String[] stationType = indexReqVO.getStationType().split(",");
                    if (stationType.length == 1) {
                        if (stationType[0].equals("41")) {
                            lam.eq(ResourceSiteManagementDO::getDevelopWay, "纯蓄能");
                        } else {
                            lam.eq(ResourceSiteManagementDO::getDevelopWay, "混合式");
                        }
                    } else {
                        List<String> developWayList = List.of("纯蓄能", "混合式");
                        lam.in(ResourceSiteManagementDO::getDevelopWay, developWayList);
                    }
                }
                List<ResourceSiteManagementDO> list = resourceSiteManagementMapper.selectList(lam);

                // 根据配置的角色权限适用项目控制数据范围
                if (!superAdmin) {
                    Set<Long> bizIdSet = new HashSet<>();
                    bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.RESOURCE_SITE_PUMPED_STORAGE_HYDROPOWER.getType())).orElse(new HashSet<>()));
                    bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.GETRESOURCE_SITE_CONVENTIONAL_HYDROPOWER.getType())).orElse(new HashSet<>()));
                    list = list.stream().filter(temp->bizIdSet.contains(temp.getId())).collect(Collectors.toList());
                }

                List<IndexInfoRespVO.ResourceSiteManagement> resourceSiteManagementList = list.stream().map(re -> {
                    IndexInfoRespVO.ResourceSiteManagement resourceSiteManagement = new IndexInfoRespVO.ResourceSiteManagement();
                    FileTreeDO fileTreeDO = fileTreeService.selectFirstLevelFileTreeDO(re.getId(), "0");
                    resourceSiteManagement.setId(Objects.nonNull(fileTreeDO) ? fileTreeDO.getId() : null);
                    resourceSiteManagement.setResourceSiteId(re.getId());
                    resourceSiteManagement.setPowerStationName(re.getPowerStationName());
                    resourceSiteManagement.setPowerStationType(re.getPowerStationType());
                    resourceSiteManagement.setPowerCode(re.getPowerCode());
                    resourceSiteManagement.setCountry(findAddress1(re.getCountry(), re.getProvince(), re.getCity(), re.getCounty()));
                    resourceSiteManagement.setProvince(re.getProvince());
                    resourceSiteManagement.setCity(re.getCity());
                    resourceSiteManagement.setCounty(re.getCounty());
                    resourceSiteManagement.setAddress(re.getAddress());
                    resourceSiteManagement.setComplianceRegulations(re.getComplianceRegulations());
                    resourceSiteManagement.setDesignStage(re.getDesignStage());
                    resourceSiteManagement.setDevelopWay(re.getDevelopWay());
                    resourceSiteManagement.setDistanceToHeightRatio(re.getDistanceToHeightRatio());
                    resourceSiteManagement.setFullShippingHours(re.getFullShippingHours());
                    resourceSiteManagement.setLnvestmentUnit(re.getLnvestmentUnit());
                    resourceSiteManagement.setLongitude(re.getLongitude());
                    resourceSiteManagement.setLatitude(re.getLatitude());
                    resourceSiteManagement.setRatedHead(re.getRatedHead());
                    resourceSiteManagement.setStaticInvestment(re.getStaticInvestment());
                    resourceSiteManagement.setSignificantSensitiveFactors(re.getSignificantSensitiveFactors());
                    resourceSiteManagement.setTotalCapacity(re.getTotalCapacity());
                    resourceSiteManagement.setDataSources(re.getDataSources());
                    resourceSiteManagement.setDesignUnit(re.getDesignUnit());
                    resourceSiteManagement.setWaterSourceConditions(re.getWaterSourceConditions());
                    resourceSiteManagement.setRemarks(re.getRemarks());
                    resourceSiteManagement.setShowFileTree(2);
                    return resourceSiteManagement;
                }).collect(Collectors.toList());
                indexInfoRespVO.setResourceSiteManagementList(resourceSiteManagementList);
            } else if (SearchTypeEnum.PROJECT_INFO.getType()==Integer.parseInt(indexReqVO.getSearchType())) {
                //查找项目列表
                List<IndexInfoRespVO.ProjectInfo> projectInfoList = getProjectInfoData();
                indexInfoRespVO.setProjectInfoList(projectInfoList);
                MPJLambdaWrapper<EngineeringProjectDO> lam = new MPJLambdaWrapper<EngineeringProjectDO>();
                if (indexReqVO.getName() != null && !indexReqVO.getName().isEmpty()) {
                    lam.like(EngineeringProjectDO::getName, indexReqVO.getName());
                }
                if (indexReqVO.getCountry() != null && !indexReqVO.getCountry().isEmpty()) {
                    lam.eq(EngineeringProjectDO::getCountry, indexReqVO.getCountry());
                }
                if (indexReqVO.getProvince() != null && !indexReqVO.getProvince().isEmpty()) {
                    lam.eq(EngineeringProjectDO::getProvince, indexReqVO.getProvince());
                }
                if (indexReqVO.getCity() != null && !indexReqVO.getCity().isEmpty()) {
                    lam.eq(EngineeringProjectDO::getCity, indexReqVO.getCity());
                }
                if (indexReqVO.getIsNaGui() != null && !indexReqVO.getIsNaGui().isEmpty()) {
                    lam.eq(EngineeringProjectDO::getDesignStage, indexReqVO.getIsNaGui());
                }
                if (indexReqVO.getWorkDepth() != null && !indexReqVO.getWorkDepth().isEmpty()) {
                    lam.eq(EngineeringProjectDO::getProgress, indexReqVO.getWorkDepth());
                }
                if ((indexReqVO.getInstalledCapacityW() != null && !indexReqVO.getInstalledCapacityW().isEmpty())
                        || (indexReqVO.getInstalledCapacityM() != null && !indexReqVO.getInstalledCapacityM().isEmpty())) {
                    if (indexReqVO.getWorkDepth() != null && !indexReqVO.getWorkDepth().isEmpty()) {
                        lam.leftJoin(EngineeringProjectParametersDO.class, EngineeringProjectParametersDO::getProjectId, EngineeringProjectDO::getId);
                        if (indexReqVO.getInstalledCapacityM() != null && !indexReqVO.getInstalledCapacityM().isEmpty()) {
                            IndexReqVO finalIndexReqVO = indexReqVO;
                            lam.and(i -> {
                                i.eq(EngineeringProjectParametersDO::getValue1, "装机容量(MW)");
                                if (finalIndexReqVO.getWorkDepth().equals("规划")) {
                                    i.le(EngineeringProjectParametersDO::getValue2, Long.valueOf(finalIndexReqVO.getInstalledCapacityM()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("预可")) {
                                    i.le(EngineeringProjectParametersDO::getValue4, Long.valueOf(finalIndexReqVO.getInstalledCapacityM()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("三专")) {
                                    i.le(EngineeringProjectParametersDO::getValue6, Long.valueOf(finalIndexReqVO.getInstalledCapacityM()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("可研")) {
                                    i.le(EngineeringProjectParametersDO::getValue8, Long.valueOf(finalIndexReqVO.getInstalledCapacityM()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("详图")) {
                                    i.le(EngineeringProjectParametersDO::getValue10, Long.valueOf(finalIndexReqVO.getInstalledCapacityM()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("已建")) {
                                    i.le(EngineeringProjectParametersDO::getValue12, Long.valueOf(finalIndexReqVO.getInstalledCapacityM()));
                                }
                            });
                        }
                        if (indexReqVO.getInstalledCapacityW() != null && !indexReqVO.getInstalledCapacityW().isEmpty()) {
                            IndexReqVO finalIndexReqVO = indexReqVO;
                            lam.and(i -> {
                                i.eq(EngineeringProjectParametersDO::getValue1, "装机容量(MW)");
                                if (finalIndexReqVO.getWorkDepth().equals("规划")) {
                                    i.ge(EngineeringProjectParametersDO::getValue2, Long.valueOf(finalIndexReqVO.getInstalledCapacityW()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("预可")) {
                                    i.ge(EngineeringProjectParametersDO::getValue4, Long.valueOf(finalIndexReqVO.getInstalledCapacityW()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("三专")) {
                                    i.ge(EngineeringProjectParametersDO::getValue6, Long.valueOf(finalIndexReqVO.getInstalledCapacityW()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("可研")) {
                                    i.ge(EngineeringProjectParametersDO::getValue8, Long.valueOf(finalIndexReqVO.getInstalledCapacityW()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("详图")) {
                                    i.ge(EngineeringProjectParametersDO::getValue10, Long.valueOf(finalIndexReqVO.getInstalledCapacityW()));
                                } else if (finalIndexReqVO.getWorkDepth().equals("已建")) {
                                    i.ge(EngineeringProjectParametersDO::getValue12, Long.valueOf(finalIndexReqVO.getInstalledCapacityW()));
                                }
                            });
                        }
                    } else {
                        throw exception(new ErrorCode(500, "请选择工作进展！"));
                    }
                }
                List<EngineeringProjectDO> list = engineeringProjectMapper.selectList(lam);

                // 根据配置的角色权限适用项目控制数据范围
                if (!superAdmin) {
                    Set<Long> bizIdSet = new HashSet<>();
                    bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.PROJECT.getType())).orElse(new HashSet<>()));
                    bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.PROJECT_CONVENTIONAL_HYDROPOWER.getType())).orElse(new HashSet<>()));
                    list = list.stream().filter(temp->bizIdSet.contains(temp.getId())).collect(Collectors.toList());
                }

                List<IndexInfoRespVO.ProjectInfo> listProjectInfo = new ArrayList<>();
                if (list != null && !list.isEmpty()) {
                    List<Long> projectIdList = list.stream().map(EngineeringProjectDO::getId).toList();
                    lam = new MPJLambdaWrapper<EngineeringProjectDO>();
                    lam.in(EngineeringProjectDO::getId, projectIdList);
                    if (indexReqVO.getContinuousHours() != null && !indexReqVO.getContinuousHours().isEmpty()) {
                        IndexReqVO finalIndexReqVO = indexReqVO;
                        if (indexReqVO.getWorkDepth() != null && !indexReqVO.getWorkDepth().isEmpty()) {
                            lam.leftJoin(EngineeringProjectParametersDO.class, EngineeringProjectParametersDO::getProjectId, EngineeringProjectDO::getId);
                            lam.and(i -> {
                                i.eq(EngineeringProjectParametersDO::getValue1, "连续满发小时数(h)");
                                if (finalIndexReqVO.getWorkDepth().equals("规划")) {
                                    i.eq(EngineeringProjectParametersDO::getValue2, finalIndexReqVO.getContinuousHours());
                                } else if (finalIndexReqVO.getWorkDepth().equals("预可")) {
                                    i.eq(EngineeringProjectParametersDO::getValue4, finalIndexReqVO.getContinuousHours());
                                } else if (finalIndexReqVO.getWorkDepth().equals("三专")) {
                                    i.eq(EngineeringProjectParametersDO::getValue6, finalIndexReqVO.getContinuousHours());
                                } else if (finalIndexReqVO.getWorkDepth().equals("可研")) {
                                    i.eq(EngineeringProjectParametersDO::getValue8, finalIndexReqVO.getContinuousHours());
                                } else if (finalIndexReqVO.getWorkDepth().equals("详图")) {
                                    i.eq(EngineeringProjectParametersDO::getValue10, finalIndexReqVO.getContinuousHours());
                                } else if (finalIndexReqVO.getWorkDepth().equals("已建")) {
                                    i.eq(EngineeringProjectParametersDO::getValue12, finalIndexReqVO.getContinuousHours());
                                }
                            });
                        } else {
                            throw exception(new ErrorCode(500, "请选择工作进展！"));
                        }
                    }
                    list = engineeringProjectMapper.selectList(lam);
                    listProjectInfo = list.stream().map(re -> {
                        IndexInfoRespVO.ProjectInfo projectInfo = new IndexInfoRespVO.ProjectInfo();
//                    projectInfo.setId(re.getId());
                        FileTreeDO fileTreeDO = fileTreeService.selectFirstLevelFileTreeDO(re.getId(), "1");
                        projectInfo.setId(Objects.nonNull(fileTreeDO) ? fileTreeDO.getId() : null);
                        projectInfo.setProjectId(re.getId());
                        projectInfo.setPowerStationName(re.getName());
                        projectInfo.setArea(findAddress1(re.getCountry(), re.getProvince(), re.getCity(), re.getCounty()));
                        projectInfo.setLongitude(re.getLongitude());
                        projectInfo.setLatitude(re.getLatitude());
                        projectInfo.setDesignStage(re.getProgress());
                        projectInfo.setDevelopWay(re.getDevelopWay());
                        projectInfo.setShowFileTree(1);
                        EngineeringProjectParametersDO ep = engineeringProjectParametersMapper.selectOne(
                                new LambdaQueryWrapperX<EngineeringProjectParametersDO>()
                                        .eq(EngineeringProjectParametersDO::getProjectId, re.getId())
                                        .eq(EngineeringProjectParametersDO::getValue1, "装机容量(MW)")
                        );
                        if (ep != null) {
                            if (re.getProgress().equals("规划")) {
                                projectInfo.setTotalCapacity(ep.getValue2());
                            } else if (re.getProgress().equals("预可")) {
                                projectInfo.setTotalCapacity(ep.getValue4());
                            } else if (re.getProgress().equals("三专")) {
                                projectInfo.setTotalCapacity(ep.getValue6());
                            } else if (re.getProgress().equals("可研")) {
                                projectInfo.setTotalCapacity(ep.getValue8());
                            } else if (re.getProgress().equals("详图")) {
                                projectInfo.setTotalCapacity(ep.getValue10());
                            } else if (re.getProgress().equals("已建")) {
                                projectInfo.setTotalCapacity(ep.getValue12());
                            }
                        }
                        return projectInfo;
                    }).collect(Collectors.toList());
                }
                indexInfoRespVO.setProjectInfoList(listProjectInfo);
            }
        } else {
//            List<StationDO> stationDOS= stationService.listAll();
            List<StationDO> stationDOS = stationMapper.selectList(new LambdaQueryWrapperX<StationDO>()
                    .likeIfPresent(StationDO::getHydrologicName, indexReqVO.getName()));

            // 根据配置的角色权限适用项目控制数据范围
            if (!superAdmin) {
                Set<Long> stationBizIdSet = new HashSet<>();
                stationBizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.STATION_WEATHER.getType())).orElse(new HashSet<>()));
                stationBizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.STATION_RAINFALL.getType())).orElse(new HashSet<>()));
                stationBizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.STATION_HYDROLOGIC.getType())).orElse(new HashSet<>()));
                stationDOS = stationDOS.stream().filter(temp->stationBizIdSet.contains(temp.getId())).collect(Collectors.toList());
            }

            Map<Integer, List<StationDO>> stationsMap = stationDOS.stream().collect(Collectors.groupingBy(StationDO::getStationType));
            if (CollectionUtil.isNotEmpty(stationDOS)) {
                //查找雨量站
                List<IndexInfoRespVO.RainFallStation> rainFallStationList = getRainDataAll(stationsMap);
                indexInfoRespVO.setRainFallStationList(rainFallStationList);
                //查找水文站
                List<IndexInfoRespVO.HydrologicStation> hydrologicStations = getHydrologicStationData(stationsMap);
                indexInfoRespVO.setHydrologicStationList(hydrologicStations);
                //查找气象站
                List<IndexInfoRespVO.WeatherStation> weatherStationList = getWeatherStationData(stationsMap);
                indexInfoRespVO.setWeatherStationList(weatherStationList);
            }

            //查找资源站点
            List<ResourceSiteManagementDO> list1 = resourceSiteManagementMapper.selectList(new LambdaQueryWrapperX<ResourceSiteManagementDO>()
                    .likeIfPresent(ResourceSiteManagementDO::getPowerStationName, indexReqVO.getName()));

            // 根据配置的角色权限适用项目控制数据范围
            if (!superAdmin) {
                Set<Long> resourceSiteBizIdSet = new HashSet<>();
                resourceSiteBizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.RESOURCE_SITE_PUMPED_STORAGE_HYDROPOWER.getType())).orElse(new HashSet<>()));
                resourceSiteBizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.GETRESOURCE_SITE_CONVENTIONAL_HYDROPOWER.getType())).orElse(new HashSet<>()));
                list1 = list1.stream().filter(temp->resourceSiteBizIdSet.contains(temp.getId())).collect(Collectors.toList());
            }

            List<IndexInfoRespVO.ResourceSiteManagement> resourceSiteManagementList = list1.stream().map(re -> {
                IndexInfoRespVO.ResourceSiteManagement resourceSiteManagement = new IndexInfoRespVO.ResourceSiteManagement();
                FileTreeDO fileTreeDO = fileTreeService.selectFirstLevelFileTreeDO(re.getId(), "0");
                resourceSiteManagement.setId(Objects.nonNull(fileTreeDO) ? fileTreeDO.getId() : null);
                resourceSiteManagement.setResourceSiteId(re.getId());
                resourceSiteManagement.setPowerStationName(re.getPowerStationName());
                resourceSiteManagement.setPowerStationType(re.getPowerStationType());
                resourceSiteManagement.setPowerCode(re.getPowerCode());
                resourceSiteManagement.setCountry(findAddress1(re.getCountry(), re.getProvince(), re.getCity(), re.getCounty()));
                resourceSiteManagement.setProvince(re.getProvince());
                resourceSiteManagement.setCity(re.getCity());
                resourceSiteManagement.setCounty(re.getCounty());
                resourceSiteManagement.setAddress(re.getAddress());
                resourceSiteManagement.setComplianceRegulations(re.getComplianceRegulations());
                resourceSiteManagement.setDesignStage(re.getDesignStage());
                resourceSiteManagement.setDevelopWay(re.getDevelopWay());
                resourceSiteManagement.setDistanceToHeightRatio(re.getDistanceToHeightRatio());
                resourceSiteManagement.setFullShippingHours(re.getFullShippingHours());
                resourceSiteManagement.setLnvestmentUnit(re.getLnvestmentUnit());
                resourceSiteManagement.setLongitude(re.getLongitude());
                resourceSiteManagement.setLatitude(re.getLatitude());
                resourceSiteManagement.setRatedHead(re.getRatedHead());
                resourceSiteManagement.setStaticInvestment(re.getStaticInvestment());
                resourceSiteManagement.setSignificantSensitiveFactors(re.getSignificantSensitiveFactors());
                resourceSiteManagement.setTotalCapacity(re.getTotalCapacity());
                resourceSiteManagement.setDataSources(re.getDataSources());
                resourceSiteManagement.setDesignUnit(re.getDesignUnit());
                resourceSiteManagement.setWaterSourceConditions(re.getWaterSourceConditions());
                resourceSiteManagement.setRemarks(re.getRemarks());
                resourceSiteManagement.setShowFileTree(2);
                return resourceSiteManagement;
            }).collect(Collectors.toList());
            indexInfoRespVO.setResourceSiteManagementList(resourceSiteManagementList);
            //查找项目列表
            List<EngineeringProjectDO> list = engineeringProjectMapper.selectList(new LambdaQueryWrapperX<EngineeringProjectDO>()
                    .likeIfPresent(EngineeringProjectDO::getName, indexReqVO.getName()));

            // 根据配置的角色权限适用项目控制数据范围
            if (!superAdmin) {
                Set<Long> projectBizIdSet = new HashSet<>();
                projectBizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.PROJECT.getType())).orElse(new HashSet<>()));
                projectBizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.PROJECT_CONVENTIONAL_HYDROPOWER.getType())).orElse(new HashSet<>()));
                list = list.stream().filter(temp->projectBizIdSet.contains(temp.getId())).collect(Collectors.toList());
            }

            List<IndexInfoRespVO.ProjectInfo> listProjectInfo = list.stream().map(re -> {
                IndexInfoRespVO.ProjectInfo projectInfo = new IndexInfoRespVO.ProjectInfo();
//                    projectInfo.setId(re.getId());
                FileTreeDO fileTreeDO = fileTreeService.selectFirstLevelFileTreeDO(re.getId(), "1");
                projectInfo.setId(Objects.nonNull(fileTreeDO) ? fileTreeDO.getId() : null);
                projectInfo.setProjectId(re.getId());
                projectInfo.setPowerStationName(re.getName());
                projectInfo.setArea(findAddress1(re.getCountry(), re.getProvince(), re.getCity(), re.getCounty()));
                projectInfo.setLongitude(re.getLongitude());
                projectInfo.setLatitude(re.getLatitude());
                projectInfo.setDesignStage(re.getProgress());
                projectInfo.setDevelopWay(re.getDevelopWay());
                projectInfo.setShowFileTree(1);
                return projectInfo;
            }).collect(Collectors.toList());
            indexInfoRespVO.setProjectInfoList(listProjectInfo);
        }
        indexInfoRespVO.setSelectNum(0);
        if(indexInfoRespVO.getHydrologicStationList()!=null){
            indexInfoRespVO.setSelectNum(indexInfoRespVO.getHydrologicStationList().size()+indexInfoRespVO.getSelectNum());
        }
        if(indexInfoRespVO.getWeatherStationList()!=null){
            indexInfoRespVO.setSelectNum(indexInfoRespVO.getWeatherStationList().size()+indexInfoRespVO.getSelectNum());
        }
        if(indexInfoRespVO.getRainFallStationList()!=null){
            indexInfoRespVO.setSelectNum(indexInfoRespVO.getRainFallStationList().size()+indexInfoRespVO.getSelectNum());
        }
        if(indexInfoRespVO.getResourceSiteManagementList()!=null){
            indexInfoRespVO.setSelectNum(indexInfoRespVO.getResourceSiteManagementList().size()+indexInfoRespVO.getSelectNum());
        }
        if(indexInfoRespVO.getProjectInfoList()!=null){
            indexInfoRespVO.setSelectNum(indexInfoRespVO.getProjectInfoList().size()+indexInfoRespVO.getSelectNum());
        }
        return indexInfoRespVO;
    }

    //查找雨量站数据
    public List<IndexInfoRespVO.RainFallStation> getRainDataAll(Map<Integer, List<StationDO>> stationsMap) {
        List<StationDO> rainStationDOList = stationsMap.get(StationEnum.RAIN.getType());
        List<IndexInfoRespVO.RainFallStation> rainFallStationList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rainStationDOList)) {
            rainFallStationList.addAll(rainStationDOList.stream().map(item -> {
                IndexInfoRespVO.RainFallStation stationInfo = new IndexInfoRespVO.RainFallStation();
                StationDO stationDO = stationMapper.selectById(item.getId());
                Set<Integer> dataProjectSet = new HashSet<>();

                if (!stationDO.getDataProject().isEmpty()) {
                    String dataProject = stationDO.getDataProject();
                    if(dataProject.contains(",")){
                        dataProjectSet = Arrays.stream(dataProject.split(","))
                                .filter(s -> !s.isEmpty())
                                .map(Integer::parseInt)
                                .map(this::mapDataProject)
                                .collect(Collectors.toSet());
                    }else{
                        dataProjectSet.add(0);
                    }
                }else{
                    dataProjectSet.add(0);
                }

                // 获取描述集合
                Set<String> descSet = dataProjectSet.stream()
                        .map(HydrologicDataProjectEnum::getDescByCode)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                // 从 hydrologic_stastical_dictionary 表中找到匹配的数据及其所有子级数据
                Set<Long> validStatsIds = getValidStatsIds(descSet);
                //查询索引表  计算年限  起止年份
                List<StatisticalInfoDO> statisticalInfoDOS = statisticalInfoService.getStatisticaInfosByStationId(item.getId());
                List<StatisticalInfoDO> filteredStatisticalInfoDOS = statisticalInfoDOS.stream()
                        .filter(info -> validStatsIds.contains(info.getStatsId()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(filteredStatisticalInfoDOS)) {
                    List<String> list = filteredStatisticalInfoDOS.stream().map(StatisticalInfoDO::getYears).toList();
                    Set<String> strings = new HashSet<>();
                    list.forEach(str -> {
                        if (StringUtils.isNotEmpty(str)) {
                            String[] split = str.split(";");
                            Collections.addAll(strings, split);
                        }
                    });
                    if (CollectionUtil.isNotEmpty(strings)) {
                        String max2 = Collections.max(strings);
                        String min1 = Collections.min(strings);
                        stationInfo.setDataAge(strings.size());
                        stationInfo.setDataYear(min1 + "-" + max2);
                    } else {
                        stationInfo.setDataYear("-");
                    }
                }
                stationInfo.setStationId(item.getId());
                stationInfo.setStationName(item.getHydrologicName());
                stationInfo.setHydrologicCode(item.getHydrologicCode());
                stationInfo.setRiverSystem(item.getRiverSystem());
                stationInfo.setRiverName(item.getRiverName());
                stationInfo.setSectionAddress(findAddress1(item.getCountry(),item.getProvince(), item.getCity(), item.getCounty()));
                stationInfo.setLongitude(item.getLongitude());
                stationInfo.setLatitude(item.getLatitude());
                stationInfo.setEstablishDate(generateEstablishDate(item.getYear(), item.getMonth()));
                return stationInfo;
            }).toList());
        }
        return rainFallStationList;
    }
    //查找水文站
    public List<IndexInfoRespVO.HydrologicStation> getHydrologicStationData(Map<Integer, List<StationDO>> stationsMap){
        List<StationDO> hydrologicStationDOList = stationsMap.get(StationEnum.HYDROLOGIC.getType());
        List<IndexInfoRespVO.HydrologicStation> hydrologicStations = new ArrayList<>();
        if (!CollectionUtils.isEmpty(hydrologicStationDOList)) {
            hydrologicStations.addAll(hydrologicStationDOList.stream().map(item -> {
                IndexInfoRespVO.HydrologicStation stationInfo = new IndexInfoRespVO.HydrologicStation();
                StationDO stationDO = stationMapper.selectById(item.getId());
                Set<Integer> dataProjectSet = new HashSet<>();

                if (!stationDO.getDataProject().isEmpty()) {
                    String dataProject = stationDO.getDataProject();
                    if(dataProject.contains(",")){
                        dataProjectSet = Arrays.stream(dataProject.split(","))
                                .filter(s -> !s.isEmpty())
                                .map(Integer::parseInt)
                                .map(this::mapDataProject)
                                .collect(Collectors.toSet());
                    }else{
                        dataProjectSet.add(0);
                    }
                }else{
                    dataProjectSet.add(0);
                }

                // 获取描述集合
                Set<String> descSet = dataProjectSet.stream()
                        .map(HydrologicDataProjectEnum::getDescByCode)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                // 从 hydrologic_stastical_dictionary 表中找到匹配的数据及其所有子级数据
                Set<Long> validStatsIds = getValidStatsIds(descSet);

                //查询索引表  计算年限  起止年份
                List<StatisticalInfoDO> statisticalInfoDOS = statisticalInfoService.getStatisticaInfosByStationId(item.getId());
                List<StatisticalInfoDO> filteredStatisticalInfoDOS = statisticalInfoDOS.stream()
                        .filter(info -> validStatsIds.contains(info.getStatsId()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(filteredStatisticalInfoDOS)) {
                    List<String> list = filteredStatisticalInfoDOS.stream().map(StatisticalInfoDO::getYears).toList();
                    Set<String> strings = new HashSet<>();
                    list.forEach(str -> {
                        if (StringUtils.isNotEmpty(str)) {
                            String[] split = str.split(";");
                            Collections.addAll(strings, split);
                        }
                    });
                    if (CollectionUtil.isNotEmpty(strings)) {
                        String max2 = Collections.max(strings);
                        String min1 = Collections.min(strings);
                        stationInfo.setDataAge(strings.size());
                        stationInfo.setDataYear(min1 + "-" + max2);
                    } else {
                        stationInfo.setDataYear("-");
                    }
                }
                stationInfo.setStationId(item.getId());
                stationInfo.setStationName(item.getHydrologicName());
                stationInfo.setHydrologicCode(item.getHydrologicCode());
                stationInfo.setRiverSystem(item.getRiverSystem());
                stationInfo.setRiverName(item.getRiverName());
                stationInfo.setSectionAddress(findAddress1(item.getCountry(),item.getProvince(), item.getCity(), item.getCounty()));
                stationInfo.setLongitude(item.getLongitude());
                stationInfo.setLatitude(item.getLatitude());
                stationInfo.setEstablishDate(generateEstablishDate(item.getYear(), item.getMonth()));
                return stationInfo;
            }).toList());
        }
        return hydrologicStations;
    }
    //查找气象站
    public List<IndexInfoRespVO.WeatherStation> getWeatherStationData(Map<Integer, List<StationDO>> stationsMap){
        List<StationDO> weatherStationDOList = stationsMap.get(StationEnum.WEATHER.getType());
        List<IndexInfoRespVO.WeatherStation> weatherStationList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(weatherStationDOList)) {
            weatherStationList.addAll(weatherStationDOList.stream().map(item -> {
                IndexInfoRespVO.WeatherStation stationInfo = new IndexInfoRespVO.WeatherStation();
                //查询索引表  计算年限  起止年份
                List<StatisticalInfoDO> statisticalInfoDOS = statisticalInfoService.getStatisticaInfosByStationId(item.getId());
                if (CollectionUtil.isNotEmpty(statisticalInfoDOS)) {
                    List<String> list = statisticalInfoDOS.stream().map(StatisticalInfoDO::getYears).toList();
                    Set<String> strings = new HashSet<>();
                    list.forEach(str -> {
                        if (StringUtils.isNotEmpty(str)) {
                            String[] split = str.split(";");
                            Collections.addAll(strings, split);
                        }
                    });
                    if (CollectionUtil.isNotEmpty(strings)) {
                        String max2 = Collections.max(strings);
                        String min1 = Collections.min(strings);
                        stationInfo.setDataAge(strings.size());
                        stationInfo.setDataYear(min1 + "-" + max2);
                    } else {
                        stationInfo.setDataYear("-");
                    }
                }
                stationInfo.setStationId(item.getId());
                stationInfo.setStationName(item.getHydrologicName());
                stationInfo.setLongitude(item.getLongitude());
                stationInfo.setLatitude(item.getLatitude());
                stationInfo.setAltitude(item.getAltitude());
                stationInfo.setObservationTime(item.getStartObservationYear());
                stationInfo.setMissingTestingTimePeriod(item.getMissingTestingTimePeriod());
                stationInfo.setObservationMethod(item.getObservationMethod());
                return stationInfo;
            }).toList());
        }
        return weatherStationList;
    }



    //查找项目列表
    public List<IndexInfoRespVO.ProjectInfo> getProjectInfoData(){
        List<Long> projectIdList = new ArrayList<>(List.of(-1L));
        boolean superAdmin = false;
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());
        if (!CollectionUtils.isEmpty(roleList)) {
            for (RoleRespDTO role : roleList) {
                if (RoleUtil.isAdminRole(role.getCode())) {
                    superAdmin = true;
                    break;
                }
                projectIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.PROJECT.getType()));
            }
        }
        projectIdList = projectIdList.stream().distinct().collect(Collectors.toList());
        //查找当前用户分配的项目列表
        List<ProjectDO> projectDOList = projectService.findAllProject();
        List<IndexInfoRespVO.ProjectInfo> projectInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(projectDOList)) {
            boolean finalSuperAdmin = superAdmin;
            List<Long> finalProjectIdList = projectIdList;
            projectInfoList.addAll(projectDOList.stream().map(item -> {
                IndexInfoRespVO.ProjectInfo projectInfo = new IndexInfoRespVO.ProjectInfo();
                projectInfo.setProjectId(item.getId());
                projectInfo.setPowerStationName(item.getPowerStationName());
                projectInfo.setArea(findAddress(item.getProvince(), item.getCity(), item.getCounty()));
                projectInfo.setLongitude(item.getLongitude());
                projectInfo.setLatitude(item.getLatitude());
                projectInfo.setDesignStage(item.getDesignStage());
                projectInfo.setDevelopWay(item.getDevelopWay());
                projectInfo.setShowFileTree((finalSuperAdmin || finalProjectIdList.contains(item.getId())) ? FileTreeShowEnum.SHOW.getType() : FileTreeShowEnum.HIDE.getType());
                if(item.getId()!=null){
//                    FileTreeDO fileTreeDO = fileTreeService.selectFirstLevelFileTreeDO(item.getId());
//                    projectInfo.setId(Objects.nonNull(fileTreeDO) ? fileTreeDO.getId() : null);
                }
                return projectInfo;
            }).toList());
        }
        return projectInfoList;
    }

    @Override
    public List<ProjectDO> gainRoleApplyProjectList() {
        List<Long> projectIdList = new ArrayList<>(List.of(-1L));
        boolean superAdmin = false;
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());
        if (!CollectionUtils.isEmpty(roleList)) {
            for (RoleRespDTO role : roleList) {
                if (RoleUtil.isAdminRole(role.getCode())) {
                    superAdmin = true;
                    break;
                }
                projectIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.PROJECT.getType()));
            }
        }
        if (superAdmin) {
            return projectService.findAllProject();
        }
        projectIdList = projectIdList.stream().distinct().collect(Collectors.toList());
        return projectService.listProjectByIds(projectIdList);
    }


    @Override
    public List<StationDO> gainRoleApplyStationList(StationEnum stationEnum) {
        List<Long> bizIdList = new ArrayList<>(List.of(-1L));
        boolean superAdmin = false;
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());
        if (CollectionUtil.isNotEmpty(roleList)) {
            for (RoleRespDTO role : roleList) {
                if (RoleUtil.isAdminRole(role.getCode())) {
                    superAdmin = true;
                    break;
                }
                bizIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), RoleApplyBindTypeEnum.fromStationEnum(stationEnum).getType()));
            }
        }
        if (superAdmin) {
            return stationService.listByStationType(stationEnum.getType());
        }
        bizIdList = bizIdList.stream().distinct().collect(Collectors.toList());
        return stationService.listByIds(bizIdList);
    }

    //    private Set<Long> getValidStatsIds(Set<String> descSet) {
//        // 从 hydrologic_stastical_dictionary 表中找到匹配的数据及其所有子级数据
//        Set<Long> validStatsIds = new HashSet<>();
//        List<StatisticalDirectoryDO> dictionaryDOS = statisticalDirectoryMapper.getByNames(descSet);
//
//        for (StatisticalDirectoryDO dictionaryDO : dictionaryDOS) {
//            collectAllChildrenIds(dictionaryDO, validStatsIds);
//        }
//
//        return validStatsIds;
//    }
//
//    private void collectAllChildrenIds(StatisticalDirectoryDO dictionaryDO, Set<Long> validStatsIds) {
//        validStatsIds.add(dictionaryDO.getId());
//
//        List<StatisticalDirectoryDO> children = statisticalDirectoryMapper.getAllChildrenByParentId(dictionaryDO.getId());
//        for (StatisticalDirectoryDO child : children) {
//            collectAllChildrenIds(child, validStatsIds);
//        }
//    }
    private Set<Long> getValidStatsIds(Set<String> descSet) {
        // 从 hydrologic_stastical_dictionary 表中找到匹配的数据及其所有子级数据
        Set<Long> validStatsIds = new HashSet<>();

        // 一次性获取所有相关数据
        List<StatisticalDirectoryDO> allData = statisticalDirectoryMapper.getAllData();
        Map<Long, List<StatisticalDirectoryDO>> childrenMap = buildChildrenMap(allData);

        // 获取匹配的数据
        List<StatisticalDirectoryDO> dictionaryDOS = statisticalDirectoryMapper.getByNames(descSet);

        for (StatisticalDirectoryDO dictionaryDO : dictionaryDOS) {
            collectAllChildrenIds(dictionaryDO, childrenMap, validStatsIds);
        }

        return validStatsIds;
    }

    private void collectAllChildrenIds(StatisticalDirectoryDO dictionaryDO, Map<Long, List<StatisticalDirectoryDO>> childrenMap, Set<Long> validStatsIds) {
        validStatsIds.add(dictionaryDO.getId());

        List<StatisticalDirectoryDO> children = childrenMap.getOrDefault(dictionaryDO.getId(), Collections.emptyList());
        for (StatisticalDirectoryDO child : children) {
            collectAllChildrenIds(child, childrenMap, validStatsIds);
        }
    }

    private Map<Long, List<StatisticalDirectoryDO>> buildChildrenMap(List<StatisticalDirectoryDO> allData) {
        Map<Long, List<StatisticalDirectoryDO>> childrenMap = new HashMap<>();

        for (StatisticalDirectoryDO data : allData) {
            childrenMap.computeIfAbsent(data.getParentId(), k -> new ArrayList<>()).add(data);
        }

        return childrenMap;
    }

    /**
     * 组装成立时间
     *
     * @param year
     * @param month
     * @return
     */
    private String generateEstablishDate(String year, String month) {
        if (StringUtils.isNotBlank(year) && StringUtils.isNotBlank(month)) {
            return year + "年" + month + "月";
        } else if (StringUtils.isNotBlank(year)) {
            return year + "年";
        } else if (StringUtils.isNotBlank(month)) {
            return month + "月";
        }
        return null;
    }

    /**
     * 查找地址
     *
     * @param province
     * @param city
     * @param county
     * @return
     */
    private String findAddress(String province, String city, String county) {
        String address = null;
        if (StringUtils.isNotBlank(province)) {
            String provinceName = areaManager.getProvinceNameByCode(province);
            if (StringUtils.isNotBlank(provinceName)) {
                address = provinceName;
            }
        }
        if (StringUtils.isNotBlank(city)) {
            String cityName = areaManager.getCityNameByCode(city);
            if (StringUtils.isNotBlank(cityName)) {
                address = StringUtils.isBlank(address) ? cityName : (address + "/" + cityName);
            }
        }
        if (StringUtils.isNotBlank(county)) {
            String countyName = areaManager.getAreaNameByCode(county);
            if (StringUtils.isNotBlank(countyName)) {
                address = StringUtils.isBlank(address) ? countyName : (address + "/" + countyName);
            }
        }
        return address;
    }

    private String findAddress1(String country,String province, String city, String county) {
        String address = null;
        if (StringUtils.isNotBlank(country)) {
            String countryName = areaManager.getCountryNameByCode(country);
            if (StringUtils.isNotBlank(countryName)) {
                address = countryName;
            }
        }
        if (StringUtils.isNotBlank(province)) {
            String provinceName = areaManager.getProvinceNameByCode(province);
            if (StringUtils.isNotBlank(provinceName)) {
                address = StringUtils.isBlank(address) ? provinceName : (address + "/" + provinceName);
            }
        }
        if (StringUtils.isNotBlank(city)) {
            String cityName = areaManager.getCityNameByCode(city);
            if (StringUtils.isNotBlank(cityName)) {
                address = StringUtils.isBlank(address) ? cityName : (address + "/" + cityName);
            }
        }
        if (StringUtils.isNotBlank(county)) {
            String countyName = areaManager.getAreaNameByCode(county);
            if (StringUtils.isNotBlank(countyName)) {
                address = StringUtils.isBlank(address) ? countyName : (address + "/" + countyName);
            }
        }
        return address;
    }

    private int mapDataProject(int code) {
        switch (code) {
            case 1:
                return RAINFALL.getCode();
            case 2:
                return EVAPORATION.getCode();
            default:
                return code;
        }
    }
}
