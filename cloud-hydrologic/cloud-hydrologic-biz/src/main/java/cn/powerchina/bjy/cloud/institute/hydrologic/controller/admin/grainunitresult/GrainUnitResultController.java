package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.grainunitresult;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.grainunitresult.vo.GrainUnitResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.grainunitresult.vo.GrainUnitResultRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.grainunitresult.vo.GrainUnitResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.grainunitresult.GrainUnitResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 水文-实测悬移质单位水样颗粒级配成果")
@RestController
@RequestMapping("/plan/hydrologic/grain/unit/result")
@Validated
public class GrainUnitResultController {

    @Resource
    private GrainUnitResultService grainUnitResultService;

    @PutMapping("/update")
    @Operation(summary = "更新水文-实测悬移质单位水样颗粒级配成果")
//    @PreAuthorize("@ss.hasPermission('hydrologic:grain-unit-result:update')")
    public CommonResult<Boolean> updateGrainUnitResult(@Valid @RequestBody GrainUnitResultSaveReqVO updateReqVO) {
        grainUnitResultService.updateGrainUnitResult(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得实测悬移质单位水样颗粒级配成果分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:grain-unit-result:query')")
    public CommonResult<PageResult<GrainUnitResultRespVO>> getGrainUnitResultPage(@Valid GrainUnitResultPageReqVO pageReqVO) {
        PageResult<GrainUnitResultRespVO> pageResult = grainUnitResultService.getGrainUnitResultPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-实测悬移质单位水样颗粒级配成果分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_GRAIN_UNIT_RESULT)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<GrainUnitResultRespVO>> getGrainUnitResultDataPage(@Valid GrainUnitResultPageReqVO pageReqVO) {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return success(PageResult.empty());
        }
        pageReqVO.setLogId(null);
        PageResult<GrainUnitResultRespVO> pageResult = grainUnitResultService.getGrainUnitResultPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出实测悬移质颗粒级配成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportGrainUnitResultExcel(@Valid GrainUnitResultPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        grainUnitResultService.exportGrainUnitResultExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出实测悬移质颗粒级配成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportGrainUnitResultDataExcel(@Valid GrainUnitResultPageReqVO pageReqVO,
                                               HttpServletResponse response) throws IOException {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return;
        }
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        grainUnitResultService.exportGrainUnitResultExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入实测悬移质单位水样颗粒级配成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:grain-unit-result:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        grainUnitResultService.importData(file, stationId);
        return success(true);
    }

}