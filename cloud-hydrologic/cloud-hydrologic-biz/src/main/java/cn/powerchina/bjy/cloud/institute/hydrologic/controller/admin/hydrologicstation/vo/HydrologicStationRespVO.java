package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水文站 Response VO")
@Data
@ExcelIgnoreUnannotated
public class HydrologicStationRespVO {

    @Schema(description = "主键", example = "23021")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "测站编码")
    @ExcelProperty("测站编码")
    private String hydrologicCode;

    @Schema(description = "水系")
    @ExcelProperty("水系")
    private String riverSystem;

    @Schema(description = "河名", example = "芋艿")
    @ExcelProperty("河名")
    private String riverName;

    @Schema(description = "站名", example = "王五")
    @ExcelProperty("站名")
    private String hydrologicName;

    @Schema(description = "站别", example = "水文或水位")
    @ExcelProperty("站名")
    private String hydrologicType;

    @Schema(description = "省")
    @ExcelProperty("省")
    private String province;

    @Schema(description = "市")
    @ExcelProperty("市")
    private String city;

    @Schema(description = "县")
    @ExcelProperty("县")
    private String county;

    @Schema(description = "详细地址")
    @ExcelProperty("详细地址")
    private String address;

    @Schema(description = "经度")
    @ExcelProperty("经度")
    private String longitude;

    @Schema(description = "纬度")
    @ExcelProperty("纬度")
    private String latitude;

    @Schema(description = "集水面积")
    @ExcelProperty("集水面积")
    private String catchmentArea;

    @Schema(description = "距河口距离")
    @ExcelProperty("距河口距离")
    private String distanceFromTheRiverMouth;

    @Schema(description = "年份")
    @ExcelProperty("年份")
    private String year;

    @Schema(description = "月份")
    @ExcelProperty("月份")
    private String month;

    @Schema(description = "资料项目")
    @ExcelProperty("资料项目")
    private String dataProject;

    @Schema(description = "水位")
    @ExcelProperty("水位")
    private Integer waterLevel;

    @Schema(description = "流量")
    @ExcelProperty("流量")
    private Integer flow;

    @Schema(description = "输沙率")
    @ExcelProperty("输沙率")
    private Integer transportRate;

    @Schema(description = "含沙量")
    @ExcelProperty("含沙量")
    private Integer sandContent;

    @Schema(description = "颗粒级配")
    @ExcelProperty("颗粒级配")
    private Integer grain;

    @Schema(description = "冰情、水温")
    @ExcelProperty("冰情、水温")
    private Integer iceTem;

    @Schema(description = "降水")
    @ExcelProperty("降水")
    private Integer rainfall;

    @Schema(description = "蒸发")
    @ExcelProperty("蒸发")
    private Integer evaporation;

    @Schema(description = "领导机关")
    @ExcelProperty("领导机关")
    private String leadershipOrganization;

    @Schema(description = "历史沿革")
    @ExcelProperty("历史沿革")
    private String historicalEvolution;

    @Schema(description = "控制条件")
    @ExcelProperty("控制条件")
    private String controlConditions;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "年限")
    @ExcelProperty("年限")
    private Integer dataAge;

    @Schema(description = "开始年份")
    @ExcelProperty("开始年份")
    private String dataStartYear;

    @Schema(description = "结束年份")
    @ExcelProperty("结束年份")
    private String dataEndYear;

    @Schema(description = "资料起止年份")
    @ExcelProperty("资料起止年份")
    private String dataYear;

}