package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationmonth.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class StationMonthModel {
    /**
     * 年
     */
    @ExcelProperty(index = 0)
    private String year;
    /**
     * 月
     */
    @ExcelProperty(index = 1)
    private String month;
    /**
     * 月平均值
     */
    @ExcelProperty(index = 2)
    private String averageValue;
    /**
     * 月平均值备注", example = "随便
     */
    @ExcelProperty(index = 3)
    private String averageValueRemark;
    /**
     * 月最高水温
     */
    @ExcelProperty(index = 4)
    private String maxValue;
    /**
     * 月最高水温出现日期
     */
    @ExcelProperty(index = 5)
    private String maxValueDate;
    /**
     * 月最高水温备注", example = "随便
     */
    @ExcelProperty(index = 6)
    private String maxValueRemark;
    /**
     * 月最低水温
     */
    @ExcelProperty(index = 7)
    private String minValue;
    /**
     * 月最低水温出现日期
     */
    @ExcelProperty(index = 8)
    private String minValueDate;
    /**
     * 月最低水温备注", example = "你说的对
     */
    @ExcelProperty(index = 9)
    private String minValueRemark;
}
