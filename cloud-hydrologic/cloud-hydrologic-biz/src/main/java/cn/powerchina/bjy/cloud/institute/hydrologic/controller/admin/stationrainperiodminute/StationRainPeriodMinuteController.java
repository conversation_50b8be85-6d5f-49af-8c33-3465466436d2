package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodminute;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodminute.vo.StationRainPeriodMinutePageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodminute.vo.StationRainPeriodMinuteRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodminute.vo.StationRainPeriodMinuteSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodminute.StationRainPeriodMinuteDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationrainperiodminute.StationRainPeriodMinuteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 降水-各时段最大降水量(分钟时段)")
@RestController
@RequestMapping("/plan/hydrologic/station/rain/period/minute")
@Validated
public class StationRainPeriodMinuteController {

    @Resource
    private StationRainPeriodMinuteService stationRainPeriodMinuteService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-获得降水-各时段最大降水量(分钟时段)分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:query-rain')")
    public CommonResult<PageResult<StationRainPeriodMinuteRespVO>> getStationRainPeriodMinutePage(@Valid StationRainPeriodMinutePageReqVO pageReqVO) {
        PageResult<StationRainPeriodMinuteDO> pageResult = stationRainPeriodMinuteService.getStationRainPeriodMinutePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodMinuteRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入降水-各时段最大降水量(分钟时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> importRainStationRainPeriodMinuteFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                               @RequestParam("stationType") Integer stationType,
                                                                               @RequestParam("dataType") Integer dataType,
                                                                               @RequestParam("file") MultipartFile file) {
        stationRainPeriodMinuteService.importStationRainPeriodMinuteFeatureExcel(stationId,dataType,stationType, file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新降水-各时段最大降水量(分钟时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:update-rain')")
    public CommonResult<Boolean> updateStationRainPeriodMinute(@Valid @RequestBody StationRainPeriodMinuteSaveReqVO updateReqVO) {
        stationRainPeriodMinuteService.updateStationRainPeriodMinute(updateReqVO);
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-获得降水-各时段最大降水量(分钟时段)分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:query-hydrologic')")
    public CommonResult<PageResult<StationRainPeriodMinuteRespVO>> getStationHydrologicPeriodMinutePage(@Valid StationRainPeriodMinutePageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        PageResult<StationRainPeriodMinuteDO> pageResult = stationRainPeriodMinuteService.getStationRainPeriodMinutePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodMinuteRespVO.class));
    }

    @PostMapping("/import/hydrologic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入降水-各时段最大降水量(分钟时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> importRainStationHydrologicPeriodMinuteFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                                     @RequestParam("dataType") Integer dataType,
                                                                                     @RequestParam("file") MultipartFile file) {
        stationRainPeriodMinuteService.importStationRainPeriodMinuteFeatureExcel(stationId, dataType, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新降水-各时段最大降水量(分钟时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:update-hydrologic')")
    public CommonResult<Boolean> updateStationHydrologicPeriodMinute(@Valid @RequestBody StationRainPeriodMinuteSaveReqVO updateReqVO) {
        stationRainPeriodMinuteService.updateStationRainPeriodMinute(updateReqVO);
        return success(true);
    }

    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出降水-各时段最大降水量(分钟时段) Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:export-rain')")
    public void exportStationRainPeriodMinuteExcel(@Valid StationRainPeriodMinutePageReqVO pageReqVO,
                                                   HttpServletResponse response) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodMinuteService.exportStationRainPeriodMinuteExcel(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出降水-各时段最大降水量(分钟时段) Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:export-hydrologic')")
    public void exportStationHydrologicPeriodMinuteExcel(@Valid StationRainPeriodMinutePageReqVO pageReqVO,
                                                         HttpServletResponse response) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodMinuteService.exportStationRainPeriodMinuteExcel(response, pageReqVO);
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出降水-各时段最大降水量(分钟时段) Excel")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:export-data-rain')")
    public void exportStationRainPeriodMinuteDataExcel(@RequestParam("stationId") Long stationId,
                                                       HttpServletResponse response) {
        StationRainPeriodMinutePageReqVO pageReqVO = new StationRainPeriodMinutePageReqVO();
        pageReqVO.setStationId(stationId);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodMinuteService.exportStationRainPeriodMinuteExcel(response, pageReqVO);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出降水-各时段最大降水量(分钟时段) Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:export-data-hydrologic')")
    public void exportStationHydrologicPeriodMinuteDataExcel(@RequestParam("stationId") Long stationId,
                                                             HttpServletResponse response) {
        StationRainPeriodMinutePageReqVO pageReqVO = new StationRainPeriodMinutePageReqVO();
        pageReqVO.setStationId(stationId);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodMinuteService.exportStationRainPeriodMinuteExcel(response, pageReqVO);
    }

    @GetMapping("/page/data/rain")
    @Operation(summary = "雨量站-数据检索-获得降水-各时段最大降水量(分钟时段)分页")
    @HydrologicOperation()
    @Parameter(name = "stationId", description = "雨量站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:query-data-rain')")
    public CommonResult<PageResult<StationRainPeriodMinuteRespVO>> getStationRainPeriodMinuteDataPage(StationRainPeriodMinutePageReqVO pageReqVO) {
        PageResult<StationRainPeriodMinuteDO> pageResult = stationRainPeriodMinuteService.getStationRainPeriodMinutePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodMinuteRespVO.class));
    }

    @GetMapping("/page/data/hydrologic")
    @Operation(summary = "水文站-数据检索-获得降水-各时段最大降水量(分钟时段)分页")
    @HydrologicOperation()
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-minute:query-data-hydrologic')")
    public CommonResult<PageResult<StationRainPeriodMinuteRespVO>> getStationHydrologicPeriodMinuteDataPage(StationRainPeriodMinutePageReqVO pageReqVO) {
        PageResult<StationRainPeriodMinuteDO> pageResult = stationRainPeriodMinuteService.getStationRainPeriodMinutePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodMinuteRespVO.class));
    }

}