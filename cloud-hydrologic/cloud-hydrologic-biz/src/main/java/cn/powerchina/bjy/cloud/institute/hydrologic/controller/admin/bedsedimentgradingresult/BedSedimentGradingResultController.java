package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.bedsedimentgradingresult;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.bedsedimentgradingresult.vo.BedSedimentGradingResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.bedsedimentgradingresult.vo.BedSedimentGradingResultRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.bedsedimentgradingresult.vo.BedSedimentGradingResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.bedsedimentgradingresult.BedSedimentGradingResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.bedsedimentgradingresult.BedSedimentGradingResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 实测床沙颗粒级配成果")
@RestController
@RequestMapping("/plan/hydrologic/bed-sediment-grading-result")
@Validated
public class BedSedimentGradingResultController {

    @Resource
    private BedSedimentGradingResultService bedSedimentGradingResultService;

    @PostMapping("/create")
    @Operation(summary = "创建实测床沙颗粒级配成果")
    @PreAuthorize("@ss.hasPermission('hydrologic:bed-sediment-grading-result:create')")
    public CommonResult<Long> createBedSedimentGradingResult(@Valid @RequestBody BedSedimentGradingResultSaveReqVO createReqVO) {
        return success(bedSedimentGradingResultService.createBedSedimentGradingResult(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新实测床沙颗粒级配成果")
//    @PreAuthorize("@ss.hasPermission('hydrologic:bed-sediment-grading-result:update')")
    public CommonResult<Boolean> updateBedSedimentGradingResult(@Valid @RequestBody BedSedimentGradingResultSaveReqVO updateReqVO) {
        bedSedimentGradingResultService.updateBedSedimentGradingResult(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除实测床沙颗粒级配成果")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:bed-sediment-grading-result:delete')")
    public CommonResult<Boolean> deleteBedSedimentGradingResult(@RequestParam("id") Long id) {
        bedSedimentGradingResultService.deleteBedSedimentGradingResult(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得实测床沙颗粒级配成果")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hydrologic:bed-sediment-grading-result:query')")
    public CommonResult<BedSedimentGradingResultRespVO> getBedSedimentGradingResult(@RequestParam("id") Long id) {
        BedSedimentGradingResultDO bedSedimentGradingResult = bedSedimentGradingResultService.getBedSedimentGradingResult(id);
        return success(BeanUtils.toBean(bedSedimentGradingResult, BedSedimentGradingResultRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得实测床沙颗粒级配成果分页")
    @HydrologicOperation
//    @PreAuthorize("@ss.hasPermission('hydrologic:bed-sediment-grading-result:query')")
    public CommonResult<PageResult<BedSedimentGradingResultRespVO>> getBedSedimentGradingResultPage(@Valid BedSedimentGradingResultPageReqVO pageReqVO) {
        PageResult<BedSedimentGradingResultDO> pageResult = bedSedimentGradingResultService.getBedSedimentGradingResultPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BedSedimentGradingResultRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出实测床沙颗粒级配成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:bed-sediment-grading-result:export')")
    public void exportBedSedimentGradingResultExcel(@Valid BedSedimentGradingResultPageReqVO pageReqVO,
                                                    HttpServletResponse response) throws IOException {
        //检索和记录 两个页面使用
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        bedSedimentGradingResultService.exportExcel(response, pageReqVO);
    }

    @PostMapping("/import")
    @ExcelImportCheck
    @Operation(summary = "导入实测床沙颗粒级配成果")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "站id", required = true)
    public CommonResult<Boolean> importExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam("stationId") Long stationId,
            @RequestParam("stationType") Integer stationType,
            @RequestParam("dataType") Integer dataType
    ) throws IOException {
        bedSedimentGradingResultService.importExcel(file, stationId, stationType, dataType);
        return success(true);
    }



}