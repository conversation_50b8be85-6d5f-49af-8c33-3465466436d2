package cn.powerchina.bjy.cloud.institute.hydrologic.service.hydrologicstation;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation.vo.HydrologicStationPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation.vo.HydrologicStationRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation.vo.HydrologicStationSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.hydrologicstation.HydrologicStationDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.hydrologicstation.HydrologicStationMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.HydrologicDataProjectEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.PlanningDesignConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.HydrologicStationImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.BaseExcelProcessorAdapter;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.rainfallstation.CommonStationDataFacade;
import cn.powerchina.bjy.cloud.institute.hydrologic.task.AreaManager;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.STATION_NOT_EXISTS;


/**
 * 水文站 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class HydrologicStationServiceImpl extends BaseExcelProcessorAdapter<HydrologicStationDO, HydrologicStationImportModel>
        implements HydrologicStationService {

    @Autowired
    private AreaManager areaManager;

    @Resource
    private HydrologicDataFacade hydrologicDataFacade;

    @Resource
    private HydrologicStationMapper hydrologicStationMapper;

    @Autowired
    private CommonStationDataFacade commonStationDataFacade;

    @Override
    public Long createStation(HydrologicStationSaveReqVO createReqVO) {
        // 校验省市区
        validateProvinceEtc(createReqVO);
        // 校验站名
        validateStationName(createReqVO.getHydrologicName(), null);
        // 插入
        HydrologicStationDO station = BeanUtils.toBean(createReqVO, HydrologicStationDO.class);
        hydrologicStationMapper.insert(station);
        // 返回
        return station.getId();
    }

    @Override
    public void updateStation(HydrologicStationSaveReqVO updateReqVO) {
        // 校验省市区
        validateProvinceEtc(updateReqVO);
        // 校验存在
        HydrologicStationDO oldData = validateStationExists(updateReqVO.getId());
        // 校验站名
        validateStationName(updateReqVO.getHydrologicName(), oldData.getId());
        // 更新
        HydrologicStationDO updateObj = BeanUtils.toBean(updateReqVO, HydrologicStationDO.class);
        hydrologicStationMapper.updateById(updateObj);
    }

    @Override
    public void deleteStation(Long id) {
        // 校验存在
        validateStationExists(id);
        // 校验关联数据，水文数据较多，暂无明细提醒
        hydrologicDataFacade.hasRelation(id);
        //校验是否存在数据
        commonStationDataFacade.hasStationData(id, StationEnum.HYDROLOGIC.getType());
        // 删除
        hydrologicStationMapper.deleteById(id);
    }

    @Override
    public HydrologicStationRespVO getStation(Long id) {
        HydrologicStationDO data = hydrologicStationMapper.selectById(id);
        if (null == data) {
            return new HydrologicStationRespVO();
        }
        HydrologicStationRespVO result = BeanUtils.toBean(data, HydrologicStationRespVO.class);
        result.setDataYear(result.getDataStartYear()+"-"+result.getDataEndYear());
//        result.setProvince(AreaCacheManager.Area.PROVINCE.getName(data.getProvince()));
//        result.setCity(AreaCacheManager.Area.CITY.getName(data.getCity()));
//        result.setCounty(AreaCacheManager.Area.AREA.getName(data.getCounty()));
        return result;
    }

    @Override
    public PageResult<HydrologicStationRespVO> getStationPage(HydrologicStationPageReqVO pageReqVO) {
        PageResult<HydrologicStationDO> dos = hydrologicStationMapper.selectPage(pageReqVO);
        if (dos == null || CollectionUtils.isEmpty(dos.getList())) {
            return PageResult.empty();
        }
        PageResult<HydrologicStationRespVO> result = BeanUtils.toBean(dos, HydrologicStationRespVO.class);
        result.getList().forEach(item -> {
            item.setDataYear(item.getDataStartYear()+"-"+item.getDataEndYear());
            item.setProvince(areaManager.getProvinceNameByCode(item.getProvince()));
            item.setCity(areaManager.getCityNameByCode(item.getCity()));
            item.setCounty(areaManager.getAreaNameByCode(item.getCounty()));
            String dataObject = item.getDataProject();
            if (StringUtils.isBlank(dataObject)) {
                return;
            }
            String[] choose = dataObject.split(",", -1);
            List<String> chooseList = Lists.newArrayList(choose);
            item.setWaterLevel(HydrologicDataProjectEnum.WATER_LEVEL.choose(chooseList));
            item.setFlow(HydrologicDataProjectEnum.FLOW.choose(chooseList));
            item.setTransportRate(HydrologicDataProjectEnum.TRANSPORT_RATE.choose(chooseList));
            item.setGrain(HydrologicDataProjectEnum.GRAIN.choose(chooseList));
            item.setIceTem(HydrologicDataProjectEnum.ICE_TEM.choose(chooseList));
            item.setRainfall(HydrologicDataProjectEnum.RAINFALL.choose(chooseList));
            item.setEvaporation(HydrologicDataProjectEnum.EVAPORATION.choose(chooseList));
            item.setSandContent(HydrologicDataProjectEnum.SAND_CONTENT.choose(chooseList));
        });
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importStation(MultipartFile file) throws IOException {
        importData(file, PlanningDesignConstants.STATION_HYDROLOGIC_IMPORT_KEY, 3, null);
    }

    @Override
    public List<HydrologicStationDO> findAllHydrologicStation() {
        return hydrologicStationMapper.selectAllHydrologicStation();
    }

    private HydrologicStationDO validateStationExists(Long id) {
        HydrologicStationDO result = hydrologicStationMapper.selectById(id);
        if (result == null) {
            throw exception(STATION_NOT_EXISTS);
        }

        return result;
    }

    private void validateProvinceEtc(HydrologicStationSaveReqVO reqVO) {
        // 校验省市区否正确
        String province = areaManager.getProvinceNameByCode(reqVO.getProvince());
        if (StringUtils.isBlank(province)) {
            throw exception(ErrorCodeConstants.PARAM_ERROR, "省");
        }
        String city = areaManager.getCityNameByCode(reqVO.getCity());
        if (StringUtils.isBlank(city)) {
            throw exception(ErrorCodeConstants.PARAM_ERROR, "市");
        }
        if (StringUtils.isNotBlank(reqVO.getCounty())) {
            String area = areaManager.getAreaNameByCode(reqVO.getCounty());
            if (StringUtils.isBlank(area)) {
                throw exception(ErrorCodeConstants.PARAM_ERROR, "区/县");
            }
        }
    }

    /**
     * 判断站名是否存在
     *
     * @param stationName 站名
     * @param id          主键
     */
    private void validateStationName(String stationName, Long id) {
        List<Long> oldData = findStationByName(stationName);
        if (null == id) {
            if (CollectionUtils.isNotEmpty(oldData)) {
                throw exception(ErrorCodeConstants.HY_STATION_NAME_EXISTS);
            }
        } else {
            if (CollectionUtils.isEmpty(oldData)) {
                return;
            }
            if (oldData.size() == 1 && oldData.get(0).equals(id)) {
                return;
            }
            throw exception(ErrorCodeConstants.HY_STATION_NAME_EXISTS);
        }
    }

    @Override
    protected BaseMapperX<HydrologicStationDO> getMapper() {
        return hydrologicStationMapper;
    }

    /**
     * 校验导入的数据
     *
     * @param importDataList
     */
    protected List<HydrologicStationDO> validateStationExistsImport(List<HydrologicStationImportModel> importDataList) {
        if (CollectionUtils.isEmpty(importDataList)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
        }
        List<HydrologicStationDO> result = new ArrayList<>(importDataList.size());
        List<String> hydrologicCodeList = new ArrayList<>();
        for (int i = 0; i < importDataList.size(); i++) {
            HydrologicStationImportModel importModel = importDataList.get(i);
            //校验一些必填项
            contextValidate(importModel, i + 3);
            if (hydrologicCodeList.contains(importModel.getHydrologicCode()) || CollectionUtils.isNotEmpty(findStationByCode(importModel.getHydrologicCode()))) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_HYDROLOGIC_CODE_EXISTS_ERROR, i + 3);
            }
            //校验测站站名是否存在
            if (CollectionUtils.isNotEmpty(findStationByName(importModel.getHydrologicName()))) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NAME_EXISTS_ERROR, i + 3);
            }
            HydrologicStationDO stationDO = new HydrologicStationDO();
            org.springframework.beans.BeanUtils.copyProperties(importDataList.get(i), stationDO);
            //处理省市县
//            String[] areaItem = importModel.getArea().split("/");
            stationDO.setProvince(areaManager.getProvinceCodeByName(importDataList.get(i).getProvince()));
            stationDO.setCity(areaManager.getCityCodeByName(importDataList.get(i).getProvince(), importDataList.get(i).getCity()));
            if (StringUtils.isNotBlank(importDataList.get(i).getCounty())) {
                stationDO.setCounty(areaManager.getAreaCodeByName(importDataList.get(i).getCounty()));
            }
            //处理资料项目
            StringBuilder dataProject = new StringBuilder();
            if (StringUtils.isNotBlank(importModel.getWaterLevel())) {
                Integer value = HydrologicDataProjectEnum.WATER_LEVEL.needFill(importModel.getWaterLevel());
                if (value != null) {
                    dataProject.append(value).append(",");
                }
            }
            if (StringUtils.isNotBlank(importModel.getFlow())) {
                Integer value = HydrologicDataProjectEnum.FLOW.needFill(importModel.getFlow());
                if (value != null) {
                    dataProject.append(value).append(",");
                }
            }
            if (StringUtils.isNotBlank(importModel.getTransportRate())) {
                Integer value = HydrologicDataProjectEnum.TRANSPORT_RATE.needFill(importModel.getTransportRate());
                if (value != null) {
                    dataProject.append(value).append(",");
                }
            }
            if (StringUtils.isNotBlank(importModel.getGrain())) {
                Integer value = HydrologicDataProjectEnum.GRAIN.needFill(importModel.getGrain());
                if (value != null) {
                    dataProject.append(value).append(",");
                }
            }
            if (StringUtils.isNotBlank(importModel.getIceTem())) {
                Integer value = HydrologicDataProjectEnum.ICE_TEM.needFill(importModel.getIceTem());
                if (value != null) {
                    dataProject.append(value).append(",");
                }
            }
            if (StringUtils.isNotBlank(importModel.getRainfall())) {
                Integer value = HydrologicDataProjectEnum.RAINFALL.needFill(importModel.getRainfall());
                if (value != null) {
                    dataProject.append(value).append(",");
                }
            }
            if(StringUtils.isNotBlank(importModel.getEvaporation())){
                Integer value = HydrologicDataProjectEnum.EVAPORATION.needFill(importModel.getEvaporation());
                if (value != null) {
                    dataProject.append(value).append(",");
                }
            }
            if(StringUtils.isNotBlank(importModel.getSandContent())){
                Integer value = HydrologicDataProjectEnum.SAND_CONTENT.needFill(importModel.getSandContent());
                if (value != null) {
                    dataProject.append(value).append(",");
                }
            }
            stationDO.setDataProject(dataProject.isEmpty() ? null : dataProject.substring(0, dataProject.lastIndexOf(",")));
            hydrologicCodeList.add(importModel.getHydrologicCode());
            result.add(stationDO);
        }

        return result;
    }

    /**
     * 字段校验
     * 非空：水系、河名、站名、省市县等（待定）、经度、纬度、集水面积、年份
     *
     * @param importModel
     */
    private void contextValidate(HydrologicStationImportModel importModel, int place) {
        if (StringUtils.isBlank(importModel.getRiverSystem())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "水系为空；");
        }
        if (StringUtils.isBlank(importModel.getRiverName())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "河名为空；");
        }
        if (StringUtils.isBlank(importModel.getHydrologicName())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "站名为空；");
        }
        if (StringUtils.isBlank(importModel.getHydrologicType())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "站别为空；");
        } else {
            if (!"水文".equals(importModel.getHydrologicType()) && !"水位".equals(importModel.getHydrologicType())&& !"水库".equals(importModel.getHydrologicType())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "站别只能为水位、水库或水文；");
            }
        }
        // 省市县
        if (StringUtils.isBlank(importModel.getProvince())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "断面地点 省为空；");
        }
        if (StringUtils.isBlank(importModel.getCity())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "断面地点 市为空；");
        }
        // 校验省市区否正确
        String province = areaManager.getProvinceCodeByName(importModel.getProvince());
        if (StringUtils.isBlank(province)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "断面地点 省填写错误，" + importModel.getProvince() + "不存在");
        }
        String city = areaManager.getCityCodeByName(importModel.getProvince(), importModel.getCity());
        if (StringUtils.isBlank(city) || !areaManager.checkProvinceCityExists(province, city)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "断面地点 市填写错误，" + importModel.getProvince() + "下无" + importModel.getCity());
        }
        if(StringUtils.isNotBlank(importModel.getCounty())){
            String area="";
            if(province.equals("11")&&importModel.getCounty().equals("朝阳区")){
                area = "110105";
            }else{
                area = areaManager.getAreaCodeByName(importModel.getCounty());
            }
            if (StringUtils.isBlank(area) || !areaManager.checkCityAreaExists(city, area)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "断面地点 县填写错误，" + importModel.getProvince() + importModel.getCity() + "下无" + importModel.getCounty());
            }
        }
        if (StringUtils.isBlank(importModel.getLongitude())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "东经为空；");
        }
        importModel.setLongitude(importModel.getLongitude().replace("°", ".").replace("'", ""));
        if (StringUtils.isBlank(importModel.getLatitude())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "北纬为空；");
        }
        importModel.setLatitude(importModel.getLatitude().replace("°", ".").replace("'", ""));
        if (StringUtils.isBlank(importModel.getCatchmentArea())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "集水面积为空；");
        }
        if (StringUtils.isBlank(importModel.getYear())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, place, "年份为空；");
        }
    }

    private List<Long> findStationByName(String name) {
        return hydrologicStationMapper.selectByName(name);
    }

    private List<Long> findStationByCode(String code) {
        return hydrologicStationMapper.selectByCode(code);
    }
}