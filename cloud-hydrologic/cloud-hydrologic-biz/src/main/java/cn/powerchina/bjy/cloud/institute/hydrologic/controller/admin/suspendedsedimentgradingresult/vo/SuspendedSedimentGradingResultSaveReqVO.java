package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedsedimentgradingresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 实测推移质颗粒级配成果新增/修改 Request VO")
@Data
public class SuspendedSedimentGradingResultSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1360")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12091")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "站点类型不能为空")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;
    @Schema(description = "编辑的数据，只给编辑的")
    private List<SuspendedSedimentGradingResultSaveReqVO.SuspendedSedimentGradingResultData> dataList;

    @Schema(description = "是否删除 true是 ")
    private Boolean isDelete;

    @Schema(description = "本次更新删除的数据id")
    private List<Long> deleteIds;
    @Data
    public static class SuspendedSedimentGradingResultData {
        @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1360")
        private Long id;

        @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12091")
        @NotNull(message = "水文站id不能为空")
        private Long stationId;

        @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        @NotNull(message = "站点类型不能为空")
        private Integer stationType;

        @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        @NotNull(message = "数据类型不能为空")
        private Integer dataType;

        @Schema(description = "年份", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "年份不能为空")
        private String year;

        @Schema(description = "月份", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "月份不能为空")
        private String month;

        @Schema(description = "日", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "日不能为空")
        private String day;

        @Schema(description = "0.008mm粒径级百分比")
        private String value008;

        @Schema(description = "0.016mm粒径级百分比")
        private String value016;

        @Schema(description = "0.031mm粒径级百分比")
        private String value031;

        @Schema(description = "0.062mm粒径级百分比")
        private String value062;

        @Schema(description = "0.125mm粒径级百分比")
        private String value125;

        @Schema(description = "0.25mm粒径级百分比")
        private String value250;

        @Schema(description = "0.5mm粒径级百分比")
        private String value500;

        @Schema(description = "1.0mm粒径级百分比")
        private String value1000;

        @Schema(description = "2.0mm粒径级百分比")
        private String value2000;

        @Schema(description = "4.0mm粒径级百分比")
        private String value4000;

        @Schema(description = "8.0mm粒径级百分比")
        private String value8000;

        @Schema(description = "16.0mm粒径级百分比")
        private String value1600;

        @Schema(description = "32.0mm粒径级百分比")
        private String value3200;

        @Schema(description = "中数粒径(mm)")
        private String medianDiameter;

        @Schema(description = "平均粒径(mm)")
        private String meanDiameter;

        @Schema(description = "最大粒径(mm)")
        private String maxDiameter;

        @Schema(description = "取样方法")
        private String sampleMethod;

        @Schema(description = "分析方法")
        private String analysisMethod;

        @Schema(description = "附注", example = "你说的对")
        private String remark;
    }


}