package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo;

import lombok.Data;

import java.util.List;

@Data
public class SectionSurveyDeleteReqVO {
    /**
     * 水文站id
     */
    private Long stationId;

    /**
     * 站点类型
     */
    private Integer stationType;

    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 是否整表删除
     */
    private Boolean isDelete;

    /**
     * 删除的行数据ID列表
     */
    private List<Long> deleteRows;
}
