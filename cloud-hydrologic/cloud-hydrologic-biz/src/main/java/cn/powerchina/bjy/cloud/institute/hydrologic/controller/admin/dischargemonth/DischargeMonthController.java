package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargemonth;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargemonth.vo.DischargeMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargemonth.vo.DischargeMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargemonth.vo.DischargeMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.dischargemonth.DischargeMonthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 -月输沙率特征值")
@RestController
@RequestMapping("/plan/hydrologic/discharge/month")
@Validated
public class DischargeMonthController {

    @Resource
    private DischargeMonthService dischargeMonthService;

    @PutMapping("/update")
    @Operation(summary = "更新水文站-输沙率-月输沙率特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:discharge-month:update')")
    public CommonResult<Boolean> updateDischargeMonth(@Valid @RequestBody DischargeMonthSaveReqVO updateReqVO) {
        dischargeMonthService.updateDischargeMonth(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "月输沙率特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:discharge-month:query')")
    public CommonResult<PageResult<DischargeMonthRespVO>> getDischargeMonthPage(@Valid DischargeMonthPageReqVO pageReqVO) {
        PageResult<DischargeMonthRespVO> pageResult = dischargeMonthService.getDischargeMonthPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-月输沙率特征值分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_DISCHARGE_MONTH)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<DischargeMonthRespVO>> getDischargeMonthDataPage(@Valid DischargeMonthPageReqVO pageReqVO) {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return success(PageResult.empty());
        }
        pageReqVO.setLogId(null);
        PageResult<DischargeMonthRespVO> pageResult = dischargeMonthService.getDischargeMonthPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出水文站-输沙率-月输沙率特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportDischargeMonthExcel(@Valid DischargeMonthPageReqVO pageReqVO,
                                          HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        dischargeMonthService.exportDischargeMonthExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出水文站-输沙率-月输沙率特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportDischargeMonthDataExcel(@Valid DischargeMonthPageReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        dischargeMonthService.exportDischargeMonthExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入月输沙率特征 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:flow-info:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        dischargeMonthService.importData(file, stationId);
        return success(true);
    }

}