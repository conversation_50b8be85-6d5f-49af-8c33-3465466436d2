package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 水文站—潮位月统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TideLevelMonthRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "29827")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1278")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站", example = "1")
    @ExcelProperty("站点类型，1：雨量站，2：水文站")
    private Integer stationType;

    @Schema(description = "数据类型", example = "2")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "年")
    @ExcelProperty("年")
    private Integer year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private Integer month;

    @Schema(description = "项目")
    @ExcelProperty("项目")
    private String value1;

    @Schema(description = "潮位最高（大)")
    @ExcelProperty("潮位最高（大)")
    private String value2;

    @Schema(description = "日")
    @ExcelProperty("日")
    private String value3;

    @Schema(description = "时分")
    @ExcelProperty("时分")
    private String value4;

    @Schema(description = "月(潮位最高农历)")
    @ExcelProperty("月(潮位最高农历)")
    private String value5;

    @Schema(description = "日(潮位最高农历)")
    @ExcelProperty("日(潮位最高农历)")
    private String value6;

    @Schema(description = "潮位最高低（小)")
    @ExcelProperty("潮位最高低（小)")
    private String value7;

    @Schema(description = "日")
    @ExcelProperty("日")
    private String value8;

    @Schema(description = "时分")
    @ExcelProperty("时分")
    private String value9;

    @Schema(description = "月(潮位最低农历)")
    @ExcelProperty("月(潮位最低农历)")
    private String value10;

    @Schema(description = "日(潮位最低农历)")
    @ExcelProperty("日(潮位最低农历)")
    private String value11;

    @Schema(description = "月总数")
    @ExcelProperty("月总数")
    private String value12;

    @Schema(description = "次数")
    @ExcelProperty("次数")
    private String value13;

    @Schema(description = "平均")
    @ExcelProperty("平均")
    private String value14;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "记录时间")
    @ExcelProperty("记录时间")
    private LocalDate currentDay;

    @Schema(description = "版本")
    @ExcelProperty("版本")
    private Integer version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}