package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo.StationDataLogPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo.StationDataLogReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo.StationDataLogRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo.StationDataTreeVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationdatalog.StationDataLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 站点数据更新记录")
@RestController
@RequestMapping("/plan/hydrologic/station/data/log")
@Validated
public class StationDataLogController {

    @Resource
    private StationDataLogService stationDataLogService;

    @GetMapping("/page")
    @Operation(summary = "获得站点数据更新记录分页")
    //@PreAuthorize("@ss.hasPermission('hydrologic:station-data-log:query')")
    public CommonResult<PageResult<StationDataLogRespVO>> getStationDataLogPage(@Valid StationDataLogPageReqVO pageReqVO) {
        PageResult<StationDataLogDO> pageResult = stationDataLogService.getStationDataLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationDataLogRespVO.class));
    }

    @GetMapping("/year/list")
    @Operation(summary = "获得年鉴拥有的年份")
    //@PreAuthorize("@ss.hasPermission('hydrologic:station-data-log:query-year')")
    public CommonResult<List<Integer>> getStationDataLogYear(@Valid StationDataLogReqVO reqVO) {
        return success(stationDataLogService.getStationDataLogYear(reqVO));
    }

    @GetMapping("/year/list-children")
    @Operation(summary = "获得年鉴拥有的年份-树状图")
    //@PreAuthorize("@ss.hasPermission('hydrologic:station-data-log:query-year')")
    public CommonResult<List<StationDataTreeVO>> getStationDataLogYearTree(@Valid StationDataLogReqVO reqVO) {
        return success(stationDataLogService.getStationDataLogYearTree(reqVO));
    }

}