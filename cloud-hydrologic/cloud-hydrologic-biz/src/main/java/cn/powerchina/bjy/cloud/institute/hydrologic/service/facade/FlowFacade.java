package cn.powerchina.bjy.cloud.institute.hydrologic.service.facade;

import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.flowmonth.FlowMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.flowyear.FlowYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.FlowMonthImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.FlowYearImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.flowday.FlowDayService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.flowmonth.FlowMonthService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.flowyear.FlowYearService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 流量相关service聚合
 *
 * <AUTHOR>
 **/
@Component
public class FlowFacade {

    @Lazy
    @Resource
    private FlowDayService flowDayService;
    @Lazy
    @Resource
    private FlowMonthService flowMonthService;
    @Lazy
    @Resource
    private FlowYearService flowYearService;

    public Integer getYearLatestVersion(Long stationId) {
        return flowYearService.getLatestVersion(stationId);
    }

    public Integer getMonthLatestVersion(Long stationId) {
        return flowMonthService.getLatestVersion(stationId);
    }

    public Integer getDayLatestVersion(Long stationId) {
        return flowDayService.getLatestVersion(stationId);
    }

    public void processImportYearModel(List<FlowYearImportModel> importModels) {
        flowYearService.processImportModel(importModels);
    }

    public void processImportMonthModel(List<FlowMonthImportModel> importModels) {
        flowMonthService.processImportModel(importModels);
    }

    public FlowYearDO findYearDOByVersionAndYear(Long stationId, String year, Integer version) {
        return flowYearService.findByVersionAndYear(stationId, year, version);
    }

    public List<FlowMonthDO> findMonthDOByVersionAndYear(Long stationId, String year, Integer version) {
        return flowMonthService.findByVersionAndYear(stationId, year, version);
    }
}
