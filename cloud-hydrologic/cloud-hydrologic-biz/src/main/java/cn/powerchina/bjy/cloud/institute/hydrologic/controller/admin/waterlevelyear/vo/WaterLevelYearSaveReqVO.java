package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelyear.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 水文站-水位-年水位特征值新增/修改 Request VO")
@Data
public class WaterLevelYearSaveReqVO {

    @Schema(description = "站点id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11254")
    @NotNull(message = "站点id不能为空")
    private Long stationId;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<Item> dataList;
    @Schema(description = "管理后台 - 降水-日降水量数据 Request VO")
    @Data
    public static class Item {
        @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "29625")
        private Long id;

        @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27167")
        @NotNull(message = "水文站id不能为空")
        private Long stationId;

        @Schema(description = "年")
        private String year;

        @Schema(description = "平均水位(m)")
        private String averageValue;

        @Schema(description = "最高水位(m)")
        private String maxValue;

        @Schema(description = "最高水位出现日期")
        private String maxValueDate;

        @Schema(description = "最低水位(m)")
        private String minValue;

        @Schema(description = "最低水位出现日期")
        private String minValueDate;

        @Schema(description = "备注", example = "你说的对")
        private String remark;

        @Schema(description = "版本（根据原型待定）")
        private Integer version;

        @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
        private Integer latest;

    }

}