package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationresult.dataprocessor;

import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult.vo.ElementObservationsZkzdResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo.VelocityWindResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.elementobservationszkzdresult.ElementObservationsZkzdResultMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.DataProcessor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
@Slf4j
@Service
public class ElementObservationsZkzdResultProcessorServiceImpl implements DataProcessor<ElementObservationsZkzdResultSaveReqVO> {
    @Resource
    private ElementObservationsZkzdResultMapper elementObservationsZkzdResultMapper;
    @Override
    public void update(ElementObservationsZkzdResultSaveReqVO updateReqVO) {

    }

    @Override
    public void importExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException {

    }

    @Override
    public List<Integer> listYear(Long stationId, Integer dataType) {
        return  elementObservationsZkzdResultMapper.listYear(stationId,dataType);
    }
}
