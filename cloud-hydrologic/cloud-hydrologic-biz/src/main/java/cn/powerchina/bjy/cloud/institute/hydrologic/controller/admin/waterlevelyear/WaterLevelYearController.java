package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelyear.vo.WaterLevelYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelyear.vo.WaterLevelYearRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelyear.vo.WaterLevelYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.waterlevelyear.WaterLevelYearService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 水文站-水位-年水位特征值")
@RestController
@RequestMapping("/plan/hydrologic/water/level/year")
@Validated
public class WaterLevelYearController {

    @Resource
    private WaterLevelYearService waterLevelYearService;

    @PutMapping("/update")
    @Operation(summary = "更新水位-年水位特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:water-level-year:update')")
    public CommonResult<Boolean> updateWaterLevelYear(@Valid @RequestBody WaterLevelYearSaveReqVO updateReqVO) {
        waterLevelYearService.updateWaterLevelYear(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得水位-年水位特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:water-level-year:query')")
    public CommonResult<PageResult<WaterLevelYearRespVO>> getWaterLevelYearPage(@Valid WaterLevelYearPageReqVO pageReqVO) {
        PageResult<WaterLevelYearRespVO> pageResult = waterLevelYearService.getWaterLevelYearPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-年水位特征值分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_WATER_LEVEL_YEAR)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<WaterLevelYearRespVO>> getWaterLevelYearDataPage(@Valid WaterLevelYearPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        PageResult<WaterLevelYearRespVO> pageResult = waterLevelYearService.getWaterLevelYearPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出年水位特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportWaterLevelYearExcel(@Valid WaterLevelYearPageReqVO pageReqVO,
                                          HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        waterLevelYearService.exportExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出年水位特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportWaterLevelYearDataExcel(@Valid WaterLevelYearPageReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        waterLevelYearService.exportExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入水文站年水位特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:ice-day:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        waterLevelYearService.importData(file, stationId);
        return success(true);
    }

}