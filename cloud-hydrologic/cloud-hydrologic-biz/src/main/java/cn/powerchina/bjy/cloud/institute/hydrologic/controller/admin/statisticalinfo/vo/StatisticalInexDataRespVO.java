package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo;

import com.alibaba.druid.support.spring.stat.annotation.Stat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Schema(description = "管理后台 - 水文-统计表索引数据信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StatisticalInexDataRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "29319")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "parentId", example = "18273")
    @ExcelProperty("parentId")
    private Long parentId;

    @Schema(description = "目录、统计表、页签的名称", example = "张三")
    @ExcelProperty("目录、统计表、页签的名称")
    private String name;

    @Schema(description = "页签的code")
    @ExcelProperty("页签的code")
    private String code;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "1")
    @ExcelProperty("站点类型，1：雨量站，2：水文站，3：气象站")
    private String stationType;

    @Schema(description = "前端组件name", example = "芋艿")
    @ExcelProperty("前端组件name")
    private String componentName;

    @Schema(description = "是否标签页，0否，1是，默认为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否标签页，0否，1是，默认为0")
    private Boolean isLabel;


    @Schema(description = "站点id", example = "6060")
    @ExcelProperty("站点id")
    private Long stationId;

    @Schema(description = "统计表id", example = "12517")
    @ExcelProperty("统计表id")
    private Long statsId;

    @Schema(description = "开始年")
    @ExcelProperty("开始年")
    private String startYear;

    @Schema(description = "结束年")
    @ExcelProperty("结束年")
    private String endYear;

    @Schema(description = "年份数量", example = "16265")
    @ExcelProperty("年份数量")
    private Integer yearCount;

    @Schema(description = "年份数据 例如：2021;2022;2023")
    @ExcelProperty("年份数据 例如：2021;2022;2023")
    private String years;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "子目录")
    List<StatisticalInexDataRespVO> children;

    @Schema(description = "统计表页签")
    List<StatisticalInexDataRespVO> labels;
}