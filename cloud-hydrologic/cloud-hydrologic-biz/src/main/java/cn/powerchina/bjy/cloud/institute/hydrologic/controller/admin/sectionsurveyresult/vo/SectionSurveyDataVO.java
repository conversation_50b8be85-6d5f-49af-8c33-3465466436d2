package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
    public class SectionSurveyDataVO {
    // 原有字段全部移到这个类中
    private Long id;
    private Long stationId;
    private Integer year;


    private String surveyDate;

    private String sectionName;
    private BigDecimal waterLevel;
    private Integer periodType;
    private String remark;
    private String verticalNo;
    private BigDecimal startDistance;
    private BigDecimal riverBedElevation;
    private String creator;           // 根据前端数据新增
    private String updater;           // 根据前端数据新增
    private Long createTime;          // 根据前端数据新增
    private Long updateTime;          // 根据前端数据新增

    public LocalDate getSurveyDateAsLocalDate() {
        if (surveyDate == null) {
            return null;
        }
        return DateUtils.parseMonthDay(surveyDate);
    }
}
