package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.station;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.station.vo.StationPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.station.vo.StationRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.station.vo.StationSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.station.StationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "水文 - 站点")
@RestController
    @RequestMapping("/plan/hydrologic/station")
@Validated
public class StationController {

    @Resource
    private StationService stationService;

    @PostMapping("/create")
    @Operation(summary = "创建站点")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station:create')")
    public CommonResult<Long> createStation(@Valid @RequestBody StationSaveReqVO createReqVO) {
        return success(stationService.createStation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新站点")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station:update')")
    public CommonResult<Boolean> updateStation(@Valid @RequestBody StationSaveReqVO updateReqVO) {
        stationService.updateStation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除站点")
    @Parameter(name = "id", description = "编号", required = true)
   // @PreAuthorize("@ss.hasPermission('hydrologic:station:delete')")
    public CommonResult<Boolean> deleteStation(@RequestParam("id") Long id) {
        stationService.deleteStation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得站点")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station:query')")
    public CommonResult<StationRespVO> getStation(@RequestParam("id") Long id) {
        return success(stationService.getStation(id));

    }

    @GetMapping("/page")
    @Operation(summary = "获得站点分页")
    public CommonResult<PageResult<StationRespVO>> getStationPage(@Valid StationPageReqVO pageReqVO) {
        PageResult<StationRespVO> pageResult = stationService.getStationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出站点 Excel")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station:export')")
//    @OperateLog(type = EXPORT)
    public void exportStationExcel(@Valid StationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<StationRespVO> list = stationService.getStationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "站点.xls", "数据", StationRespVO.class,
                list);
    }


    @GetMapping("/getForm/page")
    @Operation(summary = "获得站点表单列表分页")
    // @PreAuthorize("@ss.hasPermission('hydrologic:station:query')")
    public CommonResult<PageResult<StationRespVO>> getStationAndFormPage(@Valid StationPageReqVO pageReqVO) {
        PageResult<StationRespVO> pageResult = stationService.getStationPage(pageReqVO);
        return success(pageResult);
    }


    @PostMapping("/import")
	@ExcelImportCheck
    @Operation(summary = "站点Excel导入")
    @Parameter(name = "file", description = "excel文件", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importStationExcel(@RequestParam("file") MultipartFile file,@RequestParam("stationType") Integer stationType) throws IOException {
        stationService.importStation(file,stationType);
        return success(true);
    }

}