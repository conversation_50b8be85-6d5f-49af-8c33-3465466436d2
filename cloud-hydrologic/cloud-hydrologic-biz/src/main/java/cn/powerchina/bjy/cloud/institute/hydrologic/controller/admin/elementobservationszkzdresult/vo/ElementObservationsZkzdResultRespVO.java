package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 气象-六要素观测成果表(中科正奇ZK-ZDx) Response VO")
@Data
@ExcelIgnoreUnannotated
public class ElementObservationsZkzdResultRespVO {
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5949")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "气象站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23323")
    @ExcelProperty("气象站id")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "1")
    @ExcelProperty("站点类型，1：雨量站，2：水文站， 3：气象站")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "时间")
    @ExcelProperty("时间")
    private String time;

    @Schema(description = "日雨量（mm）")
    @ExcelProperty("日雨量（mm）")
    private String dailyRainfall;

    @Schema(description = "风向编码")
    @ExcelProperty("风向编码")
    private String windCode;

    @Schema(description = "风向")
    @ExcelProperty("风向")
    private String wind;

    @Schema(description = "温度(℃)")
    @ExcelProperty("温度(℃)")
    private String temperature;

    @Schema(description = "湿度(%)")
    @ExcelProperty("湿度(%)")
    private String humidity;

    @Schema(description = "大气压(Kpa)")
    @ExcelProperty("大气压(Kpa)")
    private String atmospheric;

    @Schema(description = "风速(m/s)")
    @ExcelProperty("风速(m/s)")
    private String velocity;

    @Schema(description = "分钟雨量（mm)")
    @ExcelProperty("分钟雨量（mm)")
    private String minuteRainfall;

    @Schema(description = "电压(V）")
    @ExcelProperty("电压(V）")
    private String voltage;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "记录时间")
    @ExcelProperty("记录时间")
    private LocalDate currentDay;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
