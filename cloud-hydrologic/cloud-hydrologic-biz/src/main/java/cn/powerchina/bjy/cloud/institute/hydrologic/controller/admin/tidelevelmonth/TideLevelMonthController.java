package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth;


import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TideLevelDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo.TidelevelDayUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo.TideLevelMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo.TideLevelMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo.TideLevelMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo.TidelevelMonthUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelmonth.TideLevelMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.TideLevelDayForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.TideLevelMonthForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.tidelevelmonth.TideLevelMonthService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 水文站—潮位月统计")
@RestController
@RequestMapping("/plan/hydrologic/tide-level-month")
@Validated
public class TideLevelMonthController {

    @Resource
    private TideLevelMonthService tideLevelMonthService;

    @PostMapping("/create")
    @Operation(summary = "创建水文站—潮位月统计")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-month:create')")
    public CommonResult<Long> createTideLevelMonth(@Valid @RequestBody TideLevelMonthSaveReqVO createReqVO) {
        return success(tideLevelMonthService.createTideLevelMonth(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水文站—潮位月统计")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-month:update')")
    public CommonResult<Boolean> updateTideLevelMonth(@Valid @RequestBody TideLevelMonthSaveReqVO updateReqVO) {
        tideLevelMonthService.updateTideLevelMonth(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水文站—潮位月统计")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-month:delete')")
    public CommonResult<Boolean> deleteTideLevelMonth(@RequestParam("id") Long id) {
        tideLevelMonthService.deleteTideLevelMonth(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水文站—潮位月统计")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-month:query')")
    public CommonResult<TideLevelMonthRespVO> getTideLevelMonth(@RequestParam("id") Long id) {
        TideLevelMonthDO tideLevelMonth = tideLevelMonthService.getTideLevelMonth(id);
        return success(BeanUtils.toBean(tideLevelMonth, TideLevelMonthRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水文站—潮位月统计分页")
    @PreAuthorize("@ss.hasPermission('hydrologic:tide-level-month:query')")
    public CommonResult<PageResult<TideLevelMonthRespVO>> getTideLevelMonthPage(@Valid TideLevelMonthPageReqVO pageReqVO) {
        PageResult<TideLevelMonthDO> pageResult = tideLevelMonthService.getTideLevelMonthPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TideLevelMonthRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水文站—潮位月统计 Excel")
    public void exportTideLevelMonthExcel(@Valid TideLevelMonthPageReqVO pageReqVO,
                                          HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TideLevelMonthDO> list = tideLevelMonthService.getTideLevelMonthPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "水文站—潮位月统计.xls", "数据", TideLevelMonthRespVO.class,
                BeanUtils.toBean(list, TideLevelMonthRespVO.class));
    }

    @GetMapping("/info")
    @Operation(summary = "逐潮高低潮位表-月统计查询")
    @HydrologicOperation
    public CommonResult<List<TideLevelMonthForm>> getYearBookList(TideLevelMonthPageReqVO reqVO) {
        //数据处理器
//        tideLevelDayService.getYearBookList(reqVO);

        return success(tideLevelMonthService.getYearBookList(reqVO));
    }

    @PostMapping("/update-month")
    @Operation(summary = "逐潮高低潮位表-月统计修改")
    @HydrologicOperation
    public CommonResult<Boolean> updateAndDeleteTideLevelMonth(@Valid @RequestBody TidelevelMonthUpdateReqVO reqVO) {
        //数据处理器
//        tideLevelDayService.getYearBookList(reqVO);
        tideLevelMonthService.updateAndDeleteTideLevelMonth(reqVO);
        return success(true);
    }

}