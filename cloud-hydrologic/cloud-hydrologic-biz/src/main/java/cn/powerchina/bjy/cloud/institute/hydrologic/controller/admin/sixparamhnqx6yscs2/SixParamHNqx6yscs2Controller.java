package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sixparamhnqx6yscs2;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 六要素观测成果表(南通惠能HNqx6ys-cs2) Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 资源站点管理")
@RestController
@RequestMapping("/plan/hydrologic/six-param-hnqx6ys-cs2")
@Validated
public class SixParamHNqx6yscs2Controller {
}
