package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelday;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelday.vo.WaterLevelDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelday.vo.WaterLevelDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelday.vo.WaterLevelDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelday.vo.WaterYearBookUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.YearForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.waterlevelday.WaterLevelDayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 水文站-水位-逐日平均水位")
@RestController
@RequestMapping("/plan/hydrologic/water/level/day")
@Validated
public class WaterLevelDayController {

    @Resource
    private WaterLevelDayService waterLevelDayService;

    @PutMapping("/update")
    @Operation(summary = "（序列）更新水位-逐日平均水位")
//    @PreAuthorize("@ss.hasPermission('hydrologic:water-level-day:update')")
    public CommonResult<Boolean> updateWaterLevelDay(@Valid @RequestBody WaterLevelDaySaveReqVO updateReqVO) {
        waterLevelDayService.updateWaterLevelDay(updateReqVO);
        return success(true);
    }

    @GetMapping("/book/info")
    @Operation(summary = "（年鉴）水文-年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-hydrologic-year:yearbook-info')")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "logId", description = "记录id")
    public CommonResult<List<YearForm>> getYearBookList(@RequestParam("stationId") Long stationId,
                                                        @RequestParam(value = "logId", required = false) Long logId) {
        return success(waterLevelDayService.getYearBookList(stationId, logId, null));
    }

    @GetMapping("/book/info/search")
    @Operation(summary = "（数据检索）水文-年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-hydrologic-year:yearbook-info')")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "year", description = "年")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_WATER_LEVEL_BOOK)
    public CommonResult<List<YearForm>> getYearBookDataList(@RequestParam("stationId") Long stationId,
                                                            @RequestParam(value = "year", required = false) String year) {
        if (StringUtils.isBlank(year)) {
            return success(new ArrayList<>());
        }
        return success(waterLevelDayService.getYearBookList(stationId, null, year));
    }

    @PostMapping("/book/update")
    @Operation(summary = "(年鉴)水文--修改数据")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:yearbook-update')")
    public CommonResult<Boolean> updateYearBookList(@RequestBody WaterYearBookUpdateReqVO reqVO) {
        waterLevelDayService.modifyYearBookList(reqVO.getStationId(), reqVO.getDataMode());
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得水位-逐日平均水位分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:water-level-day:query')")
    public CommonResult<PageResult<WaterLevelDayRespVO>> getWaterLevelDayPage(@Valid WaterLevelDayPageReqVO pageReqVO) {
        PageResult<WaterLevelDayRespVO> pageResult = waterLevelDayService.getWaterLevelDayPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "（检索）水文-逐日平均水位分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_WATER_LEVEL_DAY)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<WaterLevelDayRespVO>> getWaterLevelDayDataPage(@Valid WaterLevelDayPageReqVO pageReqVO) {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return success(PageResult.empty());
        }
        pageReqVO.setLogId(null);
        PageResult<WaterLevelDayRespVO> pageResult = waterLevelDayService.getWaterLevelDayPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（提交记录-序列）导出序列格式 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportWaterLevelDayExcel(@Valid WaterLevelDayPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        waterLevelDayService.exportExcel(response, pageReqVO);
    }

    @GetMapping("/export/book")
    @Operation(summary = "（提交记录-年鉴）导出年鉴格式 Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "logId", description = "记录Id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportWaterLevelDayBookExcel(@RequestParam("stationId") Long stationId,
                                             @RequestParam(value = "logId") Long logId,
                                             HttpServletResponse response) throws IOException {
        waterLevelDayService.exportBookExcel(response, stationId, logId, null);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索-序列）导出逐日平均水位 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportWaterLevelDayDataExcel(@Valid WaterLevelDayPageReqVO pageReqVO,
                                             HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        waterLevelDayService.exportExcel(response, pageReqVO);
    }

    @GetMapping("/export/databook")
    @Operation(summary = "（检索-年鉴）导出逐日平均水位年鉴格式 Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "year", description = "年", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportWaterLevelDayDataExcel(@RequestParam("stationId") Long stationId,
                                             @RequestParam(value = "year") String year,
                                             HttpServletResponse response) throws IOException {
        waterLevelDayService.exportBookExcel(response, stationId, null, year);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入水文站-水位-逐日平均水位 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:ice-day:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "type", description = "年鉴或序列（1:序列;2:年鉴）", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                                           @RequestParam("stationId") Long stationId,
                                                           @RequestParam("type") Integer type) throws IOException {
        waterLevelDayService.importData(file, stationId, type);
        return success(true);
    }

}