package cn.powerchina.bjy.cloud.institute.hydrologic.service.filetree;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.roleapply.RoleApplyDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.FileTreeTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.RoleApplyBindTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.filetree.vo.FileTreePageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.filetree.vo.FileTreeSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filetree.FileTreeDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.filetree.FileTreeMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.filedocument.FileDocumentService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.roleapply.RoleApplyService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.RoleUtil;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.FILE_TREE_HAS_CHILDREN;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.FILE_TREE_NOT_EXISTS;

/**
 * 文件资料树 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FileTreeServiceImpl implements FileTreeService {

    @Resource
    private FileTreeMapper fileTreeMapper;

    @Autowired
    private FileDocumentService fileDocumentService;

    @Autowired
    private RoleApplyService roleApplyService;

    @Override
    public Long createFileTree(FileTreeSaveReqVO createReqVO) {
        createReqVO.setId(null);
        // 插入
        FileTreeDO fileTree = BeanUtils.toBean(createReqVO, FileTreeDO.class);
        FileTreeDO fileTreeParentDO = validateParentFileTreeExists(createReqVO.getParentId());


        //校验同级名称是否重复
        validateFileTreeNameExists(createReqVO.getId(), createReqVO.getParentId(), createReqVO.getTreeName());
        fileTree.setTreeLevel(fileTreeParentDO.getTreeLevel() + 1);
        String parentIds = fileTreeParentDO.getParentIds();
        fileTree.setParentIds(StringUtils.isBlank(parentIds) ? (fileTreeParentDO.getId() + "") : (parentIds + "," + fileTreeParentDO.getId()));
        fileTree.setProjectId(fileTreeParentDO.getProjectId());
        fileTreeMapper.insert(fileTree);
        // 返回
        return fileTree.getId();
    }

    /**
     * 校验父节点是否存在
     *
     * @param parentId
     */
    private FileTreeDO validateParentFileTreeExists(Long parentId) {
        //校验父节点是否存在
        FileTreeDO fileTreeParentDO = fileTreeMapper.selectById(parentId);
        if (Objects.isNull(fileTreeParentDO)) {
            throw exception(ErrorCodeConstants.FILE_TREE_PARENT_ID_NO_EXISTS_ERROR);
        }
        return fileTreeParentDO;
    }


    @Override
    public void updateFileTree(FileTreeSaveReqVO updateReqVO) {
        // 校验存在
        FileTreeDO treeDO = validateFileTreeExists(updateReqVO.getId());
        validateTreeLevel(treeDO);
        //校验同级名称是否重复
        validateFileTreeNameExists(updateReqVO.getId(), updateReqVO.getParentId(), updateReqVO.getTreeName());
        // 更新
        FileTreeDO updateObj = BeanUtils.toBean(updateReqVO, FileTreeDO.class);
        fileTreeMapper.updateById(updateObj);
    }

    /**
     * 校验同一节点下同级名称是否重复
     *
     * @param id
     * @param parentId
     * @param treeName
     */
    private void validateFileTreeNameExists(Long id, Long parentId, String treeName) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("parent_id", parentId);
        queryMap.put("tree_name", treeName);
        List<FileTreeDO> treeList = fileTreeMapper.selectByMap(queryMap);
        if (!CollectionUtils.isEmpty(treeList) && (Objects.isNull(id) || !Objects.equals(treeList.get(0).getId(), id))) {
            throw exception(ErrorCodeConstants.FILE_TREE_NAME_EXISTS_ERROR);
        }
    }

    @Override
    public void deleteFileTree(Long id) {
        // 校验存在
        FileTreeDO treeDO = validateFileTreeExists(id);
        //校验一级目录不能操作
        validateTreeLevel(treeDO);
        //校验是否有子级目录
        if (!CollectionUtils.isEmpty(findFileTreeDoListByParentId(id))) {
            throw exception(FILE_TREE_HAS_CHILDREN);
        }
        //校验是否有文件关联
        if (!CollectionUtils.isEmpty(fileDocumentService.findFileDocumentDOByTreeId(id))) {
            throw exception(ErrorCodeConstants.FILE_TREE_DELETE_DOCUMENT_EXISTS_ERROR);
        }
        // 删除
        fileTreeMapper.deleteById(id);
    }

    /**
     * 校验一级目录不能操作
     *
     * @param treeDO
     */
    private void validateTreeLevel(FileTreeDO treeDO) {
        if (Objects.isNull(treeDO.getParentId()) || Objects.equals(treeDO.getTreeLevel(), 1)) {
            throw exception(ErrorCodeConstants.FILE_TREE_DELETE_PERMIT_ERROR);
        }
    }

    private FileTreeDO validateFileTreeExists(Long id) {
        FileTreeDO treeDO = fileTreeMapper.selectById(id);
        if (Objects.isNull(treeDO)) {
            throw exception(FILE_TREE_NOT_EXISTS);
        }
        return treeDO;
    }

    @Override
    public FileTreeDO getFileTree(Long id) {

        return fileTreeMapper.selectById(id);
    }

    @Override
    public List<FileTreeDO> findFileTreeDoListByParentIdAndTreeName(Long parentId, String treeName) {

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("parent_id", parentId);
        queryMap.put("tree_name", treeName);
        return fileTreeMapper.selectByMap(queryMap);
    }

    @Override
    public List<FileTreeDO> findFileTreeDoListByParentId(Long parentId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("parent_id", parentId);
        return fileTreeMapper.selectByMap(queryMap);
    }

    @Override
    public String findTreePathNameByTreeId(Long treeId) {
        FileTreeDO treeDO = fileTreeMapper.selectById(treeId);
        if (Objects.isNull(treeDO)) {
            return "";
        }
        if (Objects.isNull(treeDO.getParentId())) {
            return treeDO.getTreeName();
        }
        return findTreePathNameByTreeId(treeDO.getParentId()) + "/" + treeDO.getTreeName();
    }

    @Override
    public List<Long> findChildrenTreeIdByTreeId(Long treeId) {
        FileTreeDO treeDO = fileTreeMapper.selectById(treeId);
        if (Objects.isNull(treeDO)) {
            return null;
        }
        return fileTreeMapper.selectChildrenTreeIdByTreeId(treeId);
    }

    @Override
    public void modifyFileTreeNameByProjectId(Long projectId, String treeName) {
//        FileTreeDO treeDO = fileTreeMapper.selectFirstLevelFileTreeDO(projectId);
//        if (Objects.nonNull(treeDO)) {
//            treeDO.setTreeName(treeName);
//            fileTreeMapper.updateById(treeDO);
//        }
        QueryWrapper<FileTreeDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId)
                .eq("tree_level", 1)
                .isNull("parent_id")
                .eq("deleted", 0);  // 明确指定只查询未删除的记录

        FileTreeDO treeDO = fileTreeMapper.selectOne(queryWrapper, false);
        if (Objects.nonNull(treeDO)) {
            treeDO.setTreeName(treeName);
            fileTreeMapper.updateById(treeDO);
        }
    }

    @Override
    public void addFileTree(Long projectId, String treeName) {
        FileTreeDO treeDO = new FileTreeDO();
        treeDO.setProjectId(projectId);
        treeDO.setTreeName(treeName);
        treeDO.setTreeLevel(1);
        fileTreeMapper.insert(treeDO);
        generateSecondChildren(treeDO,"1");
    }

    @Override
    public void addFileTreeType(Long projectId, String treeName, Integer type) {
        FileTreeDO treeDO = new FileTreeDO();
        treeDO.setProjectId(projectId);
        treeDO.setTreeName(treeName);
        treeDO.setTreeLevel(1);
        treeDO.setType(String.valueOf(type));
        fileTreeMapper.insert(treeDO);
        // 只有项目信息类型(type=0)才生成默认二级目录
        if (type == 1) {
            generateSecondChildren(treeDO,String.valueOf(type));
        }
    }

    /**
     * 生成默认的二级菜单
     *
     * @param fileTreeDO
     */
    private void generateSecondChildren(FileTreeDO fileTreeDO,String type) {
        //添加默认的二级
        List<FileTreeDO> treeDOList = new ArrayList<>();
        treeDOList.add(generateFileTreeDO(fileTreeDO, "报告",type));
        treeDOList.add(generateFileTreeDO(fileTreeDO, "计算书",type));
        treeDOList.add(generateFileTreeDO(fileTreeDO, "重要计算表",type));
        treeDOList.add(generateFileTreeDO(fileTreeDO, "批复文件",type));
        treeDOList.add(generateFileTreeDO(fileTreeDO, "审查意见",type));
        treeDOList.add(generateFileTreeDO(fileTreeDO, "会议记录",type));
        treeDOList.add(generateFileTreeDO(fileTreeDO, "互提资料单",type));
        fileTreeMapper.insertBatch(treeDOList);
    }

    /**
     * 生成二级子树
     *
     * @param fileTreeDO
     * @param treeName
     * @return
     */
    private FileTreeDO generateFileTreeDO(FileTreeDO fileTreeDO, String treeName,String type) {
        FileTreeDO treeDO = new FileTreeDO();
        treeDO.setTreeName(treeName);
        treeDO.setTreeLevel(2);
        treeDO.setParentId(fileTreeDO.getId());
        treeDO.setParentIds(fileTreeDO.getId() + "");
        treeDO.setProjectId(fileTreeDO.getProjectId());
        treeDO.setType(type);
        return treeDO;
    }

    @Override
    public List<FileTreeDO> findFileTreeByProjectId(Long projectId) {
        return fileTreeMapper.selectList("project_id", projectId);
    }

    @Override
    public void insertBatch(List<FileTreeDO> treeDOList,String type) {
        treeDOList.forEach(item -> {
            fileTreeMapper.insert(item);
            if(type.equals("1")){
                generateSecondChildren(item,type);
            }
        });
    }

    @Override
    public void deleteFileTreeByProjectId(Long projectId) {
        fileTreeMapper.updateFileTreeByProjectId(projectId);
    }

    @Override
    public FileTreeDO selectFirstLevelFileTreeDO(Long projectId,String type) {
        return fileTreeMapper.selectFirstLevelFileTreeDO(projectId,type);
    }

    @Override
    public List<FileTreeDO> findFileTreeByProjectIds(List<Long> projectIds) {
        return fileTreeMapper.selectListByProjectIds(projectIds);
    }

    @Override
    public void batchInsert(List<FileTreeDO> fileTreeDOList) {
        fileTreeMapper.insertList(fileTreeDOList);
    }

    @Override
    public void updateProjectId(Long oldProjectId, Long newProjectId) {
        fileTreeMapper.updateProjectId(oldProjectId,newProjectId);
    }

    @Override
    public List<FileTreeDO> selectListTemporaryData(Long projectId) {
        return fileTreeMapper.selectListTemporaryData(projectId);
    }

    @Override
    public FileTreeDO selectOne(Long projectId, Integer treeLevel, String treeName) {
        QueryWrapper<FileTreeDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id",projectId);
        queryWrapper.eq("tree_level",treeLevel);
        queryWrapper.eq("tree_name",treeName);
        return fileTreeMapper.selectOne(queryWrapper,false);
    }

    @Override
    public int insert(FileTreeDO fileTreeDO) {
        return fileTreeMapper.insert(fileTreeDO);
    }

    @Override
    public void updateCreator() {
        fileTreeMapper.updateCreator();
    }


    @Override
    public PageResult<FileTreeDO> getFileTreePage(FileTreePageReqVO pageReqVO) {
        // 角色权限适用项目map(key:绑定类型; value:绑定数据id列表)
        Map<Integer, Set<Long>> bizIdMap = new HashMap<>();

        // 获取当前用户的角色
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());

        // 获取当前用户是否为超级管理员
        boolean superAdmin = false;
        for (RoleRespDTO roleRespDTO : roleList) {
            if (RoleUtil.isAdminRole(roleRespDTO.getCode())) {
                superAdmin = true;
                break;
            }
        }

        // 获取当前用户所属的角色选择的适用项目
        Set<Long> roleIdSet = roleList.stream().map(temp->temp.getId()).collect(Collectors.toSet());
        List<RoleApplyDO> roleApplyDOList = roleApplyService.listByRoleIdSet(roleIdSet);

        // 不同角色可能绑定了同一个项目，需要把不同角色的适用项目按绑定类型分组，每组的数据去重
        roleApplyDOList.stream().collect(Collectors.groupingBy(RoleApplyDO::getBindType)).forEach((bindType, tempRoleApplyDOList)->{
            Set<Long> bizIdSet = Optional.ofNullable(bizIdMap.get(bindType)).orElse(new HashSet<>());
            tempRoleApplyDOList.forEach(tempRoleApplyDO -> {
                Arrays.stream(Optional.ofNullable(tempRoleApplyDO.getBizId()).orElse("").split(",")).forEach(bizId->{
                    if (StringUtils.isNotBlank(bizId)) {
                        bizIdSet.add(Long.parseLong(bizId));
                    }
                });
            });
            bizIdMap.put(bindType, bizIdSet);
        });

        Set<Long> bizIdSet = new HashSet<>();
        if (Objects.equals(pageReqVO.getType(), FileTreeTypeEnum.RESOURCE_SITE.getType())) {
            bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.RESOURCE_SITE_PUMPED_STORAGE_HYDROPOWER.getType())).orElse(new HashSet<>()));
            bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.GETRESOURCE_SITE_CONVENTIONAL_HYDROPOWER.getType())).orElse(new HashSet<>()));
        }
        if (Objects.equals(pageReqVO.getType(), FileTreeTypeEnum.PROJECT.getType())) {
            bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.PROJECT.getType())).orElse(new HashSet<>()));
            bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.PROJECT_CONVENTIONAL_HYDROPOWER.getType())).orElse(new HashSet<>()));
        }

        if (!superAdmin) {
            if (CollectionUtils.isEmpty(bizIdSet)) {
                return PageResult.empty();
            } else {
                pageReqVO.setProjectIdList(new ArrayList<>(bizIdSet));
                return fileTreeMapper.selectPage(pageReqVO);
            }
        } else {
            return fileTreeMapper.selectPage(pageReqVO);
        }
    }

}