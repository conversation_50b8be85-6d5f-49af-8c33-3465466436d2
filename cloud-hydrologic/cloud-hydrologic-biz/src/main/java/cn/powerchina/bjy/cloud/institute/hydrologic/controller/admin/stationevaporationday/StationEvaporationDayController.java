package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo.StationDayDataPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo.StationEvaporationDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo.StationEvaporationDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationday.vo.StationEvaporationDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationevaporationday.StationEvaporationDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationevaporationday.StationEvaporationDayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 蒸发-逐日水面蒸发量")
@RestController
@RequestMapping("/plan/hydrologic/station/evaporation/day")
@Validated
public class StationEvaporationDayController {

    @Resource
    private StationEvaporationDayService stationEvaporationDayService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-获得蒸发-逐日水面蒸发量分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:query-rain')")
    public CommonResult<PageResult<StationEvaporationDayRespVO>> getRainStationEvaporationDayPage(@Valid StationEvaporationDayPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setCreateTime(null);
        PageResult<StationEvaporationDayDO> pageResult = stationEvaporationDayService.getStationEvaporationDayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationEvaporationDayRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入蒸发-逐日水面蒸发量序列格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importRainStationEvaporationDayFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                             @RequestParam("file") MultipartFile file) {
        stationEvaporationDayService.importStationEvaporateDayExcel(stationId, StationEnum.RAIN.getType(), file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新蒸发-逐日水面蒸发量")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:update-rain')")
    public CommonResult<Boolean> updateRainStationEvaporationDay(@Valid @RequestBody StationEvaporationDaySaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.RAIN.getType());
        stationEvaporationDayService.updateStationEvaporationDay(updateReqVO);
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-获得蒸发-逐日水面蒸发量分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:query-hydrologic')")
    public CommonResult<PageResult<StationEvaporationDayRespVO>> getHydrologicStationEvaporationDayPage(@Valid StationEvaporationDayPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        PageResult<StationEvaporationDayDO> pageResult = stationEvaporationDayService.getStationEvaporationDayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationEvaporationDayRespVO.class));
    }

    @PostMapping("/import/hydrologic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入蒸发-逐日水面蒸发量序列格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    public CommonResult<Boolean> importHydrologicStationEvaporationDayFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                                   @RequestParam("file") MultipartFile file) {
        stationEvaporationDayService.importStationEvaporateDayExcel(stationId, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新蒸发-逐日水面蒸发量")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:update-hydrologic')")
    public CommonResult<Boolean> updateHydrologicStationEvaporationDay(@Valid @RequestBody StationEvaporationDaySaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        stationEvaporationDayService.updateStationEvaporationDay(updateReqVO);
        return success(true);
    }

    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出蒸发-逐日水面蒸发量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:export-rain')")
    public void exportRainStationEvaporationDayExcel(@Valid StationEvaporationDayPageReqVO pageReqVO,
                                                     HttpServletResponse response) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setCreateTime(null);
        stationEvaporationDayService.exportStationEvaporationDayExcel(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出蒸发-逐日水面蒸发量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:export-hydrologic')")
    public void exportHydrologicStationEvaporationDayExcel(@Valid StationEvaporationDayPageReqVO pageReqVO,
                                                           HttpServletResponse response) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setCreateTime(null);
        stationEvaporationDayService.exportStationEvaporationDayExcel(response, pageReqVO);
    }

    @GetMapping("/data/page/rain")
    @HydrologicOperation(stationType = StationEnum.RAIN, dataType = StationDataTypeEnum.STATION_EVAPORATE_DAY)
    @Operation(summary = "雨量站-数据检索-获得蒸发-逐日水面蒸发量分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:query-data-rain')")
    public CommonResult<PageResult<StationEvaporationDayRespVO>> getRainStationEvaporationDayDataPage(@Valid StationDayDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationEvaporationDayPageReqVO pageReqVO1 = new StationEvaporationDayPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationEvaporationDayDO> pageResult = stationEvaporationDayService.getStationEvaporationDayPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationEvaporationDayRespVO.class));
    }

    @GetMapping("/data/page/hydrologic")
    @Operation(summary = "水文站-数据检索-获得蒸发-逐日水面蒸发量分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_EVAPORATE_DAY)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:query-data-hydrologic')")
    public CommonResult<PageResult<StationEvaporationDayRespVO>> getHydrologicStationEvaporationDayDataPage(@Valid StationDayDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationEvaporationDayPageReqVO pageReqVO1 = new StationEvaporationDayPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationEvaporationDayDO> pageResult = stationEvaporationDayService.getStationEvaporationDayPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationEvaporationDayRespVO.class));
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出蒸发-逐日水面蒸发量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:export-data-rain')")
    public void exportRainStationEvaporationDayDataExcel(@Valid StationDayDataPageReqVO pageReqVO,
                                                         HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationEvaporationDayPageReqVO pageReqVO1 = new StationEvaporationDayPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setLogId(null);
        stationEvaporationDayService.exportStationEvaporationDayExcel(response, pageReqVO1);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出蒸发-逐日水面蒸发量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-day:export-data-hydrologic')")
    public void exportHydrologicStationEvaporationDayDataExcel(@Valid StationDayDataPageReqVO pageReqVO,
                                                               HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationEvaporationDayPageReqVO pageReqVO1 = new StationEvaporationDayPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setLogId(null);
        stationEvaporationDayService.exportStationEvaporationDayExcel(response, pageReqVO1);
    }

}