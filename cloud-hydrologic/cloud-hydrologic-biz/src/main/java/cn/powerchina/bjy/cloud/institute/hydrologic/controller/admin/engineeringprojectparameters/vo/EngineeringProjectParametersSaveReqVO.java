package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 工程项目信息-设计参数新增/修改 Request VO")
@Data
public class EngineeringProjectParametersSaveReqVO {
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "27401")
    private Long id;

    @Schema(description = "项目关联id", example = "5737")
    private Long projectId;

    @Schema(description = "参数")
    private String value1;

    @Schema(description = "规划")
    private String value2;

    @Schema(description = "预可")
    private String value4;

    @Schema(description = "三专")
    private String value6;

    @Schema(description = "可研")
    private String value8;

    @Schema(description = "详图")
    private String value10;

    @Schema(description = "已建")
    private String value12;

    /**
     * 序号
     */
    private Long num;


}
