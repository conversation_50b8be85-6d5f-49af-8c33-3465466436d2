package cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodday;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 降水-各时段最大降水量(日时段) DO
 *
 * <AUTHOR>
 */
@TableName("hydrologic_station_rain_period_day")
@KeySequence("hydrologic_station_rain_period_day_seq")
// 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = false)
public class StationRainPeriodDayDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 雨量站id
     */
    private Long stationId;
    /**
     * 站点类型，1：雨量站，2：水文站
     */
    private Integer stationType;

    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 年
     */
    private Integer year;
    /**
     * 1d降水量
     */
    private String valueDay1;
    /**
     * 1d开始月日
     */
    private String monthDayStart1;
    /**
     * 3d降水量
     */
    private String valueDay3;
    /**
     * 3d开始月日
     */
    private String monthDayStart3;
    /**
     * 7d降水量
     */
    private String valueDay7;
    /**
     * 7d开始月日
     */
    private String monthDayStart7;
    /**
     * 15d降水量
     */
    private String valueDay15;
    /**
     * 15d开始月日
     */
    private String monthDayStart15;
    /**
     * 30d降水量
     */
    private String valueDay30;
    /**
     * 30d开始月日
     */
    private String monthDayStart30;

    /**
     * 备注
     */
    private String remark;
    /**
     * 版本
     */
    private Integer version;
    /**
     * 最新版本（1：最新，0：历史版本，默认为1）
     */
    private Integer latest;

    public boolean hasDifference(StationRainPeriodDayDO target) {
        return

                ObjectUtil.notEqual(this.getValueDay1(),target.getValueDay1())||
                        ObjectUtil.notEqual(this.getMonthDayStart1(),target.getMonthDayStart1())||
                        ObjectUtil.notEqual(this.getValueDay3(),target.getValueDay3())||
                        ObjectUtil.notEqual(this.getMonthDayStart3(),target.getMonthDayStart3())||
                        ObjectUtil.notEqual(this.getValueDay7(),target.getValueDay7())||
                        ObjectUtil.notEqual(this.getMonthDayStart7(),target.getMonthDayStart7())||
                        ObjectUtil.notEqual(this.getValueDay15(),target.getValueDay15())||
                        ObjectUtil.notEqual(this.getMonthDayStart15(),target.getMonthDayStart15())||
                        ObjectUtil.notEqual(this.getValueDay30(),target.getValueDay30())||
                        ObjectUtil.notEqual(this.getMonthDayStart30(),target.getMonthDayStart30())||
                        ObjectUtil.notEqual(this.getRemark(),target.getRemark());
    }
}