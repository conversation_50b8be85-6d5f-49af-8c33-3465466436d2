package cn.powerchina.bjy.cloud.institute.hydrologic.interceptor;

import cn.powerchina.bjy.cloud.framework.common.exception.DataLimitException;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.RoleApplyBindTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.roleapply.RoleApplyService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.RoleUtil;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import cn.powerchina.bjy.cloud.system.enums.permission.RoleCodeEnum;
import com.fhs.common.spring.SpringContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 拦截请求
 * @Author: yhx
 * @CreateDate: 2024/7/15
 */
@Slf4j
public class HydrologicInterceptor implements HandlerInterceptor {

    /**
     * 气象站---气象资料统计\逐日气温
     */
    static List<String> weatherStationUri = new ArrayList<>(Arrays.asList("/plan/hydrologic/weather", "/plan/hydrologic/weather/data/"));
    /**
     * 水文站---冰情\降水\颗粒级配\流量\输沙率\水位\蒸发
     */
    static List<String> hydrologicStationUri = new ArrayList<>(Arrays.asList("/plan/hydrologic/ice", "/plan/hydrologic/grain", "/plan/hydrologic/flow", "/plan/hydrologic/discharge", "/plan/hydrologic/water/level"));

    /**
     * 雨量站-降水\蒸发
     */
    static List<String> rainStationUri = new ArrayList<>(Arrays.asList("/plan/hydrologic/station/rain", "/plan/hydrologic/station/evaporation"));

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        boolean superAdmin = false;
        //查询当前用户有哪些角色
        RoleApplyService roleApplyService = SpringContextUtil.getBeanByClass(RoleApplyService.class);
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());
        if (!CollectionUtils.isEmpty(roleList)) {
            for (RoleRespDTO role : roleList) {
                if (RoleUtil.isAdminRole(role.getCode())) {
                    superAdmin = true;
                    break;
                }
            }
        }
        if (superAdmin) {
            return true;
        }
        String uri = request.getRequestURI();
        String stationId = request.getParameter("stationId");
        String stationType = request.getParameter("stationType");
        if (StringUtils.isBlank(stationId)) {
            RequestWrapper requestWrapper = new RequestWrapper(request);
            String body = requestWrapper.getBody();
            if (StringUtils.isNotBlank(body)) {
                JSONObject jsonObject = new JSONObject(body);
                stationId = jsonObject.has("stationId") ? (jsonObject.get("stationId") + "") : stationId;
                stationType = jsonObject.has("stationType") ? (jsonObject.get("stationType") + "") : stationType;
            }
        }
        Integer bindType = null;
        //校验气象站
        for (String str : weatherStationUri) {
            if (uri.contains(str) && StringUtils.isNotBlank(stationId)) {
                bindType = RoleApplyBindTypeEnum.STATION_WEATHER.getType();
                break;
            }
        }
        //校验水文站
        for (String str : hydrologicStationUri) {
            if (uri.contains(str) && StringUtils.isNotBlank(stationId)) {
                bindType = RoleApplyBindTypeEnum.STATION_HYDROLOGIC.getType();
                break;
            }
        }
        //校验雨量站
        for (String str : rainStationUri) {
            if (uri.contains(str) && StringUtils.isNotBlank(stationId)) {
                //判断是水文站
                bindType = (findHydrologic(uri, "hydrologic") >= 2 || uri.contains("hydrologicbook")) ? RoleApplyBindTypeEnum.STATION_HYDROLOGIC.getType() : RoleApplyBindTypeEnum.STATION_RAINFALL.getType();
                break;
            }
        }
        if (StringUtils.isNotBlank(stationType)) {
            bindType = Objects.equals(Integer.parseInt(stationType), StationEnum.RAIN.getType()) ? RoleApplyBindTypeEnum.STATION_RAINFALL.getType() :
                    (Objects.equals(Integer.parseInt(stationType), StationEnum.HYDROLOGIC.getType()) ? RoleApplyBindTypeEnum.STATION_HYDROLOGIC.getType() : RoleApplyBindTypeEnum.STATION_WEATHER.getType());
        }
        //校验是否拥有当前站点权限
        if (StringUtils.isNotBlank(stationId) && Objects.nonNull(bindType)) {
            List<Long> stationIdList = new ArrayList<>(List.of(-1L));
            if (!CollectionUtils.isEmpty(roleList)) {
                for (RoleRespDTO role : roleList) {
                    stationIdList.addAll(roleApplyService.findRoleApplyDOByRoleIdAndBindType(role.getId(), bindType));
                }
            }
            if (!stationIdList.contains(Long.parseLong(stationId))) {
                throw new DataLimitException(4000, "没有权限");
            }
        }
        return true; // 如果返回false，则停止流程，api不会被调用
    }

    /**
     * 查找uri中含有
     *
     * @param uri
     * @param str
     * @return
     */
    private int findHydrologic(String uri, String str) {
        int count = 0;
        if (StringUtils.isNotBlank(uri)) {
            String[] uriList = uri.split("/");
            for (String uriStr : uriList) {
                if (StringUtils.isNotBlank(uriStr) && uriStr.trim().equals(str)) {
                    count++;
                }
            }
        }
        return count;
    }

    private String getRequestBody(HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();
        try {
            InputStream inputStream = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (Exception e) {
            log.error("getRequestBody---error.", e);
        }
        return sb.toString();
    }
}
