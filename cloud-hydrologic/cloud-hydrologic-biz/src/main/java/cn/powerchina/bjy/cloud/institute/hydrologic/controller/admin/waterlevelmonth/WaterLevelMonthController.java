package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelmonth;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelmonth.vo.WaterLevelMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelmonth.vo.WaterLevelMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelmonth.vo.WaterLevelMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.waterlevelmonth.WaterLevelMonthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 水文站-水位-月水位特征值")
@RestController
@RequestMapping("/plan/hydrologic/water/level/month")
@Validated
public class WaterLevelMonthController {

    @Resource
    private WaterLevelMonthService waterLevelMonthService;

    @PutMapping("/update")
    @Operation(summary = "更新水文站-水位-月水位特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:water-level-month:update')")
    public CommonResult<Boolean> updateWaterLevelMonth(@Valid @RequestBody WaterLevelMonthSaveReqVO updateReqVO) {
        waterLevelMonthService.updateWaterLevelMonth(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得水位-月水位特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:water-level-month:query')")
    public CommonResult<PageResult<WaterLevelMonthRespVO>> getWaterLevelMonthPage(@Valid WaterLevelMonthPageReqVO pageReqVO) {
        PageResult<WaterLevelMonthRespVO> pageResult = waterLevelMonthService.getWaterLevelMonthPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-月水位特征值分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_WATER_LEVEL_MONTH)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<WaterLevelMonthRespVO>> getWaterLevelMonthDataPage(@Valid WaterLevelMonthPageReqVO pageReqVO) {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return success(PageResult.empty());
        }
        pageReqVO.setLogId(null);
        PageResult<WaterLevelMonthRespVO> pageResult = waterLevelMonthService.getWaterLevelMonthPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出月水位特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportWaterLevelMonthExcel(@Valid WaterLevelMonthPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        waterLevelMonthService.exportExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出月水位特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportWaterLevelMonthDataExcel(@Valid WaterLevelMonthPageReqVO pageReqVO,
                                               HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        waterLevelMonthService.exportExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入水文站水位月特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:ice-day:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        waterLevelMonthService.importData(file, stationId);
        return success(true);
    }

}