package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeresult;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeresult.vo.DischargeResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeresult.vo.DischargeResultRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeresult.vo.DischargeResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.dischargeresult.DischargeResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台-实测悬移质输沙率成果")
@RestController
@RequestMapping("/plan/hydrologic/discharge/result")
@Validated
public class DischargeResultController {

    @Resource
    private DischargeResultService dischargeResultService;

    @PutMapping("/update")
    @Operation(summary = "更新水文站-实测悬移质输沙率成果")
//    @PreAuthorize("@ss.hasPermission('hydrologic:discharge-result:update')")
    public CommonResult<Boolean> updateDischargeResult(@Valid @RequestBody DischargeResultSaveReqVO updateReqVO) {
        dischargeResultService.updateDischargeResult(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "实测悬移质输沙率成果分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:discharge-result:query')")
    public CommonResult<PageResult<DischargeResultRespVO>> getDischargeResultPage(@Valid DischargeResultPageReqVO pageReqVO) {
        PageResult<DischargeResultRespVO> pageResult = dischargeResultService.getDischargeResultPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-实测悬移质输沙率成果分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_DISCHARGE_RESULT)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<DischargeResultRespVO>> getDischargeMonthDataPage(@Valid DischargeResultPageReqVO pageReqVO) {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return success(PageResult.empty());
        }
        pageReqVO.setLogId(null);
        PageResult<DischargeResultRespVO> pageResult = dischargeResultService.getDischargeResultPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出水文站-输沙率-实测悬移质输沙率成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportDischargeResultExcel(@Valid DischargeResultPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        dischargeResultService.exportDischargeResultExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出水文站-输沙率-实测悬移质输沙率成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportDischargeResultDataExcel(@Valid DischargeResultPageReqVO pageReqVO,
                                               HttpServletResponse response) throws IOException {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return;
        }
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        dischargeResultService.exportDischargeResultExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入实测悬移质输沙率成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:flow-info:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        dischargeResultService.importData(file, stationId);
        return success(true);
    }

}