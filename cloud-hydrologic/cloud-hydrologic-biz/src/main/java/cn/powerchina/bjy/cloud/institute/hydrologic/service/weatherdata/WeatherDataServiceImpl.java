package cn.powerchina.bjy.cloud.institute.hydrologic.service.weatherdata;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherdata.vo.WeatherDataPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherdata.vo.WeatherDataSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationday.StationDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticaldirectory.StatisticalDirectoryDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticalinfo.StatisticalInfoDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.weatherdata.WeatherDataDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.station.StationMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.weatherdata.WeatherDataMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.weatherstation.WeatherStationMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.StationDataLogAddModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.WeatherDataImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.RedisService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationdatalog.StationDataLogService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticaldirectory.StatisticalDirectoryService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticalinfo.StatisticalInfoService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.WEATHER_DATA_NOT_EXISTS;

/**
 * 气象站-气象资料统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class WeatherDataServiceImpl implements WeatherDataService {

    @Resource
    private WeatherDataMapper weatherDataMapper;

    @Autowired
    private StationMapper stationMapper;


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private StationDataLogService stationDataLogService;

    @Autowired
    private RedisService redisService;

    @Override
    public Long createWeatherData(WeatherDataSaveReqVO createReqVO) {
        // 插入
        WeatherDataDO weatherData = BeanUtils.toBean(createReqVO, WeatherDataDO.class);
        weatherDataMapper.insert(weatherData);
        // 返回
        return weatherData.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWeatherData(Long stationId, List<WeatherDataImportModel> dataModel) {
        //查询站点是否存在
        if (Objects.isNull(stationId) || Objects.isNull(stationMapper.selectById(stationId))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        //增加分布式锁，只能有一个导入,针对站点增加锁，站点的所有类型，只能有一个导入,页面新增时也要增加锁控制
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(String.format(PlanningDesignConstants.STATION_RAINFALL_IMPORT_RAIN_KEY, stationId, StationEnum.WEATHER.getCode()), 1, 60, TimeUnit.SECONDS))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EXISTS_ERROR);
        }
        try {
            if (CollectionUtils.isEmpty(dataModel) || dataModel.size() != 51) {
                throw exception(ErrorCodeConstants.STATION_DATA_UPDATE_CONTENT_EMPTY_EXISTS_ERROR);
            }
            //数据处理
            consoleDataToDb(stationId, dataModel, 0);
        } finally {
            redisTemplate.delete(String.format(PlanningDesignConstants.STATION_RAINFALL_IMPORT_RAIN_KEY, stationId, StationEnum.WEATHER.getCode()));
        }
    }

    @Override
    public void deleteWeatherData(Long id) {
        // 校验存在
        validateWeatherDataExists(id);
        // 删除
        weatherDataMapper.deleteById(id);
    }

    private void validateWeatherDataExists(Long id) {
        if (weatherDataMapper.selectById(id) == null) {
            throw exception(WEATHER_DATA_NOT_EXISTS);
        }
    }

    @Override
    public WeatherDataDO getWeatherData(Long id) {
        return weatherDataMapper.selectById(id);
    }

    @Override
    public PageResult<WeatherDataDO> getWeatherDataPage(WeatherDataPageReqVO pageReqVO) {
        return weatherDataMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importStationWeatherDataExcel(Long stationId, MultipartFile file) {
        //查询站点是否存在
        if (Objects.isNull(stationId) || Objects.isNull(stationMapper.selectById(stationId))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        //增加分布式锁，只能有一个导入,针对站点增加锁，站点的所有类型，只能有一个导入,页面新增时也要增加锁控制
        String key = String.format(PlanningDesignConstants.STATION_RAINFALL_IMPORT_RAIN_KEY, stationId, StationDataTypeEnumV2.WEATHER_METEOROLOGICAL_DATA_STATISTICS.getCode());
        RLock rLock = redisService.acquireDistributedLock(key);
        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }
            //读取excel内容
            List<WeatherDataImportModel> importDataList = EasyExcel.read(file.getInputStream(), WeatherDataImportModel.class, null).sheet().headRowNumber(0).doReadSync();
            if (CollectionUtils.isEmpty(importDataList)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
            }

            //数据处理
            consoleDataToDb(stationId, importDataList, 0);
        } catch (IOException e) {
            log.error("importStationWeatherDataExcel--->error.", e);
        } finally {
            rLock.unlock();
        }
    }

    @Override
    public Integer findNextStationWeatherDataVersion(Long stationId) {
        Integer version = weatherDataMapper.selectMaxVersion(stationId);
        return Objects.isNull(version) ? 1 : (version + 1);
    }

    @Override
    public void insertBatch(List<WeatherDataDO> dataDOList) {
        if (CollectionUtils.isEmpty(dataDOList)) {
            return;
        }
        WeatherDataDO dataDO = dataDOList.get(0);
        //更新历史版本
        Long stationId = dataDO.getStationId();
        Integer dataType = StationDataTypeEnumV2.WEATHER_METEOROLOGICAL_DATA_STATISTICS.getCode();
        weatherDataMapper.updateHistoryVersion(stationId, LatestEnum.HISTORY.getType());

        //获取下一个版本
        Integer nextVersion = stationDataLogService.getNextVersion(stationId, dataType);
        dataDOList.forEach(item->{
            item.setVersion(nextVersion);
            item.setStationType(StationEnum.WEATHER.getType());
            item.setDataType(dataType);
        });
        //插入新数据
        weatherDataMapper.insertBatch(dataDOList);
        //插入变更记录
        stationDataLogService.addStationDataLog(StationDataLogAddModel.builder().stationType(StationEnum.WEATHER.getType()).stationId(stationId)
                .currentVersion(nextVersion-1).dataType(dataType).build());
    }



    @Override
    public List<WeatherDataImportModel> findWeatherDataImportModelList(Long stationId, Long logId) {
        List<WeatherDataImportModel> importModelList = new ArrayList<>();
        List<WeatherDataDO> dataDOList = findWeatherDataDOList(stationId, logId);
        if (CollectionUtils.isEmpty(dataDOList)) {
            return importModelList;
        }
        Map<Integer, WeatherDataDO> dataDOMap = dataDOList.stream().collect(Collectors.toMap(WeatherDataDO::getMonth, Function.identity()));
        //气温-平均气温
        generateFrontImportModel(importModelList, dataDOMap.get(1).getTemperatureAverageValue(), dataDOMap.get(2).getTemperatureAverageValue(), dataDOMap.get(3).getTemperatureAverageValue(), dataDOMap.get(4).getTemperatureAverageValue(),
                dataDOMap.get(5).getTemperatureAverageValue(), dataDOMap.get(6).getTemperatureAverageValue(), dataDOMap.get(7).getTemperatureAverageValue(), dataDOMap.get(8).getTemperatureAverageValue(),
                dataDOMap.get(9).getTemperatureAverageValue(), dataDOMap.get(10).getTemperatureAverageValue(), dataDOMap.get(11).getTemperatureAverageValue(), dataDOMap.get(12).getTemperatureAverageValue(), dataDOMap.get(13).getTemperatureAverageValue());
        //气温-极端最高—极值
        generateFrontImportModel(importModelList, dataDOMap.get(1).getTemperatureMaxExtremum(), dataDOMap.get(2).getTemperatureMaxExtremum(), dataDOMap.get(3).getTemperatureMaxExtremum(), dataDOMap.get(4).getTemperatureMaxExtremum(),
                dataDOMap.get(5).getTemperatureMaxExtremum(), dataDOMap.get(6).getTemperatureMaxExtremum(), dataDOMap.get(7).getTemperatureMaxExtremum(), dataDOMap.get(8).getTemperatureMaxExtremum(),
                dataDOMap.get(9).getTemperatureMaxExtremum(), dataDOMap.get(10).getTemperatureMaxExtremum(), dataDOMap.get(11).getTemperatureMaxExtremum(), dataDOMap.get(12).getTemperatureMaxExtremum(), dataDOMap.get(13).getTemperatureMaxExtremum());
        //气温-极端最高—日期
        generateFrontImportModel(importModelList, dataDOMap.get(1).getTemperatureMaxDate(), dataDOMap.get(2).getTemperatureMaxDate(), dataDOMap.get(3).getTemperatureMaxDate(), dataDOMap.get(4).getTemperatureMaxDate(),
                dataDOMap.get(5).getTemperatureMaxDate(), dataDOMap.get(6).getTemperatureMaxDate(), dataDOMap.get(7).getTemperatureMaxDate(), dataDOMap.get(8).getTemperatureMaxDate(),
                dataDOMap.get(9).getTemperatureMaxDate(), dataDOMap.get(10).getTemperatureMaxDate(), dataDOMap.get(11).getTemperatureMaxDate(), dataDOMap.get(12).getTemperatureMaxDate(), dataDOMap.get(13).getTemperatureMaxDate());
        //气温-极端最高—年份
        generateFrontImportModel(importModelList, dataDOMap.get(1).getTemperatureMaxYear(), dataDOMap.get(2).getTemperatureMaxYear(), dataDOMap.get(3).getTemperatureMaxYear(), dataDOMap.get(4).getTemperatureMaxYear(),
                dataDOMap.get(5).getTemperatureMaxYear(), dataDOMap.get(6).getTemperatureMaxYear(), dataDOMap.get(7).getTemperatureMaxYear(), dataDOMap.get(8).getTemperatureMaxYear(),
                dataDOMap.get(9).getTemperatureMaxYear(), dataDOMap.get(10).getTemperatureMaxYear(), dataDOMap.get(11).getTemperatureMaxYear(), dataDOMap.get(12).getTemperatureMaxYear(), dataDOMap.get(13).getTemperatureMaxYear());
        //气温-极端最低—极值
        generateFrontImportModel(importModelList, dataDOMap.get(1).getTemperatureMinExtremum(), dataDOMap.get(2).getTemperatureMinExtremum(), dataDOMap.get(3).getTemperatureMinExtremum(), dataDOMap.get(4).getTemperatureMinExtremum(),
                dataDOMap.get(5).getTemperatureMinExtremum(), dataDOMap.get(6).getTemperatureMinExtremum(), dataDOMap.get(7).getTemperatureMinExtremum(), dataDOMap.get(8).getTemperatureMinExtremum(),
                dataDOMap.get(9).getTemperatureMinExtremum(), dataDOMap.get(10).getTemperatureMinExtremum(), dataDOMap.get(11).getTemperatureMinExtremum(), dataDOMap.get(12).getTemperatureMinExtremum(), dataDOMap.get(13).getTemperatureMinExtremum());
        //气温-极端最低—日期
        generateFrontImportModel(importModelList, dataDOMap.get(1).getTemperatureMinDate(), dataDOMap.get(2).getTemperatureMinDate(), dataDOMap.get(3).getTemperatureMinDate(), dataDOMap.get(4).getTemperatureMinDate(),
                dataDOMap.get(5).getTemperatureMinDate(), dataDOMap.get(6).getTemperatureMinDate(), dataDOMap.get(7).getTemperatureMinDate(), dataDOMap.get(8).getTemperatureMinDate(),
                dataDOMap.get(9).getTemperatureMinDate(), dataDOMap.get(10).getTemperatureMinDate(), dataDOMap.get(11).getTemperatureMinDate(), dataDOMap.get(12).getTemperatureMinDate(), dataDOMap.get(13).getTemperatureMinDate());
        //气温-极端最低—年份
        generateFrontImportModel(importModelList, dataDOMap.get(1).getTemperatureMinYear(), dataDOMap.get(2).getTemperatureMinYear(), dataDOMap.get(3).getTemperatureMinYear(), dataDOMap.get(4).getTemperatureMinYear(),
                dataDOMap.get(5).getTemperatureMinYear(), dataDOMap.get(6).getTemperatureMinYear(), dataDOMap.get(7).getTemperatureMinYear(), dataDOMap.get(8).getTemperatureMinYear(),
                dataDOMap.get(9).getTemperatureMinYear(), dataDOMap.get(10).getTemperatureMinYear(), dataDOMap.get(11).getTemperatureMinYear(), dataDOMap.get(12).getTemperatureMinYear(), dataDOMap.get(13).getTemperatureMinYear());
        //降水量-平均降水量
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitationAverageValue(), dataDOMap.get(2).getPrecipitationAverageValue(), dataDOMap.get(3).getPrecipitationAverageValue(), dataDOMap.get(4).getPrecipitationAverageValue(),
                dataDOMap.get(5).getPrecipitationAverageValue(), dataDOMap.get(6).getPrecipitationAverageValue(), dataDOMap.get(7).getPrecipitationAverageValue(), dataDOMap.get(8).getPrecipitationAverageValue(),
                dataDOMap.get(9).getPrecipitationAverageValue(), dataDOMap.get(10).getPrecipitationAverageValue(), dataDOMap.get(11).getPrecipitationAverageValue(), dataDOMap.get(12).getPrecipitationAverageValue(), dataDOMap.get(13).getPrecipitationAverageValue());
        //降水量-日最大降水量
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitationMaxValue(), dataDOMap.get(2).getPrecipitationMaxValue(), dataDOMap.get(3).getPrecipitationMaxValue(), dataDOMap.get(4).getPrecipitationMaxValue(),
                dataDOMap.get(5).getPrecipitationMaxValue(), dataDOMap.get(6).getPrecipitationMaxValue(), dataDOMap.get(7).getPrecipitationMaxValue(), dataDOMap.get(8).getPrecipitationMaxValue(),
                dataDOMap.get(9).getPrecipitationMaxValue(), dataDOMap.get(10).getPrecipitationMaxValue(), dataDOMap.get(11).getPrecipitationMaxValue(), dataDOMap.get(12).getPrecipitationMaxValue(), dataDOMap.get(13).getPrecipitationMaxValue());
        //降水量-不小于0.1mm日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitation01Days(), dataDOMap.get(2).getPrecipitation01Days(), dataDOMap.get(3).getPrecipitation01Days(), dataDOMap.get(4).getPrecipitation01Days(),
                dataDOMap.get(5).getPrecipitation01Days(), dataDOMap.get(6).getPrecipitation01Days(), dataDOMap.get(7).getPrecipitation01Days(), dataDOMap.get(8).getPrecipitation01Days(),
                dataDOMap.get(9).getPrecipitation01Days(), dataDOMap.get(10).getPrecipitation01Days(), dataDOMap.get(11).getPrecipitation01Days(), dataDOMap.get(12).getPrecipitation01Days(), dataDOMap.get(13).getPrecipitation01Days());
        //降水量-不小于0.5mm日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitation05Days(), dataDOMap.get(2).getPrecipitation05Days(), dataDOMap.get(3).getPrecipitation05Days(), dataDOMap.get(4).getPrecipitation05Days(),
                dataDOMap.get(5).getPrecipitation05Days(), dataDOMap.get(6).getPrecipitation05Days(), dataDOMap.get(7).getPrecipitation05Days(), dataDOMap.get(8).getPrecipitation05Days(),
                dataDOMap.get(9).getPrecipitation05Days(), dataDOMap.get(10).getPrecipitation05Days(), dataDOMap.get(11).getPrecipitation05Days(), dataDOMap.get(12).getPrecipitation05Days(), dataDOMap.get(13).getPrecipitation05Days());
        //降水量-不小于2mm日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitation2Days(), dataDOMap.get(2).getPrecipitation2Days(), dataDOMap.get(3).getPrecipitation2Days(), dataDOMap.get(4).getPrecipitation2Days(),
                dataDOMap.get(5).getPrecipitation2Days(), dataDOMap.get(6).getPrecipitation2Days(), dataDOMap.get(7).getPrecipitation2Days(), dataDOMap.get(8).getPrecipitation2Days(),
                dataDOMap.get(9).getPrecipitation2Days(), dataDOMap.get(10).getPrecipitation2Days(), dataDOMap.get(11).getPrecipitation2Days(), dataDOMap.get(12).getPrecipitation2Days(), dataDOMap.get(13).getPrecipitation2Days());
        //降水量-不小于5mm日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitation5Days(), dataDOMap.get(2).getPrecipitation5Days(), dataDOMap.get(3).getPrecipitation5Days(), dataDOMap.get(4).getPrecipitation5Days(),
                dataDOMap.get(5).getPrecipitation5Days(), dataDOMap.get(6).getPrecipitation5Days(), dataDOMap.get(7).getPrecipitation5Days(), dataDOMap.get(8).getPrecipitation5Days(),
                dataDOMap.get(9).getPrecipitation5Days(), dataDOMap.get(10).getPrecipitation5Days(), dataDOMap.get(11).getPrecipitation5Days(), dataDOMap.get(12).getPrecipitation5Days(), dataDOMap.get(13).getPrecipitation5Days());
        //降水量-不小于10mm日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitation10Days(), dataDOMap.get(2).getPrecipitation10Days(), dataDOMap.get(3).getPrecipitation10Days(), dataDOMap.get(4).getPrecipitation10Days(),
                dataDOMap.get(5).getPrecipitation10Days(), dataDOMap.get(6).getPrecipitation10Days(), dataDOMap.get(7).getPrecipitation10Days(), dataDOMap.get(8).getPrecipitation10Days(),
                dataDOMap.get(9).getPrecipitation10Days(), dataDOMap.get(10).getPrecipitation10Days(), dataDOMap.get(11).getPrecipitation10Days(), dataDOMap.get(12).getPrecipitation10Days(), dataDOMap.get(13).getPrecipitation10Days());
        //降水量-不小于25mm日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitation25Days(), dataDOMap.get(2).getPrecipitation25Days(), dataDOMap.get(3).getPrecipitation25Days(), dataDOMap.get(4).getPrecipitation25Days(),
                dataDOMap.get(5).getPrecipitation25Days(), dataDOMap.get(6).getPrecipitation25Days(), dataDOMap.get(7).getPrecipitation25Days(), dataDOMap.get(8).getPrecipitation25Days(),
                dataDOMap.get(9).getPrecipitation25Days(), dataDOMap.get(10).getPrecipitation25Days(), dataDOMap.get(11).getPrecipitation25Days(), dataDOMap.get(12).getPrecipitation25Days(), dataDOMap.get(13).getPrecipitation25Days());
        //降水量-不小于30mm日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitation30Days(), dataDOMap.get(2).getPrecipitation30Days(), dataDOMap.get(3).getPrecipitation30Days(), dataDOMap.get(4).getPrecipitation30Days(),
                dataDOMap.get(5).getPrecipitation30Days(), dataDOMap.get(6).getPrecipitation30Days(), dataDOMap.get(7).getPrecipitation30Days(), dataDOMap.get(8).getPrecipitation30Days(),
                dataDOMap.get(9).getPrecipitation30Days(), dataDOMap.get(10).getPrecipitation30Days(), dataDOMap.get(11).getPrecipitation30Days(), dataDOMap.get(12).getPrecipitation30Days(), dataDOMap.get(13).getPrecipitation30Days());
        //降水量-不小于50mm日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getPrecipitation50Days(), dataDOMap.get(2).getPrecipitation50Days(), dataDOMap.get(3).getPrecipitation50Days(), dataDOMap.get(4).getPrecipitation50Days(),
                dataDOMap.get(5).getPrecipitation50Days(), dataDOMap.get(6).getPrecipitation50Days(), dataDOMap.get(7).getPrecipitation50Days(), dataDOMap.get(8).getPrecipitation50Days(),
                dataDOMap.get(9).getPrecipitation50Days(), dataDOMap.get(10).getPrecipitation50Days(), dataDOMap.get(11).getPrecipitation50Days(), dataDOMap.get(12).getPrecipitation50Days(), dataDOMap.get(13).getPrecipitation50Days());
        //风速-主导风向-风向
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindLeadDirection(), dataDOMap.get(2).getWindLeadDirection(), dataDOMap.get(3).getWindLeadDirection(), dataDOMap.get(4).getWindLeadDirection(),
                dataDOMap.get(5).getWindLeadDirection(), dataDOMap.get(6).getWindLeadDirection(), dataDOMap.get(7).getWindLeadDirection(), dataDOMap.get(8).getWindLeadDirection(),
                dataDOMap.get(9).getWindLeadDirection(), dataDOMap.get(10).getWindLeadDirection(), dataDOMap.get(11).getWindLeadDirection(), dataDOMap.get(12).getWindLeadDirection(), dataDOMap.get(13).getWindLeadDirection());
        //风速-主导风向-风向频率
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindLeadDirectionFrequency(), dataDOMap.get(2).getWindLeadDirectionFrequency(), dataDOMap.get(3).getWindLeadDirectionFrequency(), dataDOMap.get(4).getWindLeadDirectionFrequency(),
                dataDOMap.get(5).getWindLeadDirectionFrequency(), dataDOMap.get(6).getWindLeadDirectionFrequency(), dataDOMap.get(7).getWindLeadDirectionFrequency(), dataDOMap.get(8).getWindLeadDirectionFrequency(),
                dataDOMap.get(9).getWindLeadDirectionFrequency(), dataDOMap.get(10).getWindLeadDirectionFrequency(), dataDOMap.get(11).getWindLeadDirectionFrequency(), dataDOMap.get(12).getWindLeadDirectionFrequency(), dataDOMap.get(13).getWindLeadDirectionFrequency());
        //风速-平均风速
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindAverageValue(), dataDOMap.get(2).getWindAverageValue(), dataDOMap.get(3).getWindAverageValue(), dataDOMap.get(4).getWindAverageValue(),
                dataDOMap.get(5).getWindAverageValue(), dataDOMap.get(6).getWindAverageValue(), dataDOMap.get(7).getWindAverageValue(), dataDOMap.get(8).getWindAverageValue(),
                dataDOMap.get(9).getWindAverageValue(), dataDOMap.get(10).getWindAverageValue(), dataDOMap.get(11).getWindAverageValue(), dataDOMap.get(12).getWindAverageValue(), dataDOMap.get(13).getWindAverageValue());
        //风速-最大风速-极值
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindMaxExtremum(), dataDOMap.get(2).getWindMaxExtremum(), dataDOMap.get(3).getWindMaxExtremum(), dataDOMap.get(4).getWindMaxExtremum(),
                dataDOMap.get(5).getWindMaxExtremum(), dataDOMap.get(6).getWindMaxExtremum(), dataDOMap.get(7).getWindMaxExtremum(), dataDOMap.get(8).getWindMaxExtremum(),
                dataDOMap.get(9).getWindMaxExtremum(), dataDOMap.get(10).getWindMaxExtremum(), dataDOMap.get(11).getWindMaxExtremum(), dataDOMap.get(12).getWindMaxExtremum(), dataDOMap.get(13).getWindMaxExtremum());
        //风速-最大风速-日期
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindMaxDate(), dataDOMap.get(2).getWindMaxDate(), dataDOMap.get(3).getWindMaxDate(), dataDOMap.get(4).getWindMaxDate(),
                dataDOMap.get(5).getWindMaxDate(), dataDOMap.get(6).getWindMaxDate(), dataDOMap.get(7).getWindMaxDate(), dataDOMap.get(8).getWindMaxDate(),
                dataDOMap.get(9).getWindMaxDate(), dataDOMap.get(10).getWindMaxDate(), dataDOMap.get(11).getWindMaxDate(), dataDOMap.get(12).getWindMaxDate(), dataDOMap.get(13).getWindMaxDate());
        //风速-最大风速-年份
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindMaxYear(), dataDOMap.get(2).getWindMaxYear(), dataDOMap.get(3).getWindMaxYear(), dataDOMap.get(4).getWindMaxYear(),
                dataDOMap.get(5).getWindMaxYear(), dataDOMap.get(6).getWindMaxYear(), dataDOMap.get(7).getWindMaxYear(), dataDOMap.get(8).getWindMaxYear(),
                dataDOMap.get(9).getWindMaxYear(), dataDOMap.get(10).getWindMaxYear(), dataDOMap.get(11).getWindMaxYear(), dataDOMap.get(12).getWindMaxYear(), dataDOMap.get(13).getWindMaxYear());
        //风速-最大风速-风向
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindMaxDirection(), dataDOMap.get(2).getWindMaxDirection(), dataDOMap.get(3).getWindMaxDirection(), dataDOMap.get(4).getWindMaxDirection(),
                dataDOMap.get(5).getWindMaxDirection(), dataDOMap.get(6).getWindMaxDirection(), dataDOMap.get(7).getWindMaxDirection(), dataDOMap.get(8).getWindMaxDirection(),
                dataDOMap.get(9).getWindMaxDirection(), dataDOMap.get(10).getWindMaxDirection(), dataDOMap.get(11).getWindMaxDirection(), dataDOMap.get(12).getWindMaxDirection(), dataDOMap.get(13).getWindMaxDirection());
        //风速-风速日数-不小于5m/s
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindDays5(), dataDOMap.get(2).getWindDays5(), dataDOMap.get(3).getWindDays5(), dataDOMap.get(4).getWindDays5(),
                dataDOMap.get(5).getWindDays5(), dataDOMap.get(6).getWindDays5(), dataDOMap.get(7).getWindDays5(), dataDOMap.get(8).getWindDays5(),
                dataDOMap.get(9).getWindDays5(), dataDOMap.get(10).getWindDays5(), dataDOMap.get(11).getWindDays5(), dataDOMap.get(12).getWindDays5(), dataDOMap.get(13).getWindDays5());
        //风速-风速日数-不小于10m/s
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindDays10(), dataDOMap.get(2).getWindDays10(), dataDOMap.get(3).getWindDays10(), dataDOMap.get(4).getWindDays10(),
                dataDOMap.get(5).getWindDays10(), dataDOMap.get(6).getWindDays10(), dataDOMap.get(7).getWindDays10(), dataDOMap.get(8).getWindDays10(),
                dataDOMap.get(9).getWindDays10(), dataDOMap.get(10).getWindDays10(), dataDOMap.get(11).getWindDays10(), dataDOMap.get(12).getWindDays10(), dataDOMap.get(13).getWindDays10());
        //风速-风速日数-不小于12m/s
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindDays12(), dataDOMap.get(2).getWindDays12(), dataDOMap.get(3).getWindDays12(), dataDOMap.get(4).getWindDays12(),
                dataDOMap.get(5).getWindDays12(), dataDOMap.get(6).getWindDays12(), dataDOMap.get(7).getWindDays12(), dataDOMap.get(8).getWindDays12(),
                dataDOMap.get(9).getWindDays12(), dataDOMap.get(10).getWindDays12(), dataDOMap.get(11).getWindDays12(), dataDOMap.get(12).getWindDays12(), dataDOMap.get(13).getWindDays12());
        //风速-风速日数-不小于15m/s
        generateFrontImportModel(importModelList, dataDOMap.get(1).getWindDays15(), dataDOMap.get(2).getWindDays15(), dataDOMap.get(3).getWindDays15(), dataDOMap.get(4).getWindDays15(),
                dataDOMap.get(5).getWindDays15(), dataDOMap.get(6).getWindDays15(), dataDOMap.get(7).getWindDays15(), dataDOMap.get(8).getWindDays15(),
                dataDOMap.get(9).getWindDays15(), dataDOMap.get(10).getWindDays15(), dataDOMap.get(11).getWindDays15(), dataDOMap.get(12).getWindDays15(), dataDOMap.get(13).getWindDays15());
        //地温-平均地温
        generateFrontImportModel(importModelList, dataDOMap.get(1).getGroundTemAverageValue(), dataDOMap.get(2).getGroundTemAverageValue(), dataDOMap.get(3).getGroundTemAverageValue(), dataDOMap.get(4).getGroundTemAverageValue(),
                dataDOMap.get(5).getGroundTemAverageValue(), dataDOMap.get(6).getGroundTemAverageValue(), dataDOMap.get(7).getGroundTemAverageValue(), dataDOMap.get(8).getGroundTemAverageValue(),
                dataDOMap.get(9).getGroundTemAverageValue(), dataDOMap.get(10).getGroundTemAverageValue(), dataDOMap.get(11).getGroundTemAverageValue(), dataDOMap.get(12).getGroundTemAverageValue(), dataDOMap.get(13).getGroundTemAverageValue());
        //地温-极端最高-极值
        generateFrontImportModel(importModelList, dataDOMap.get(1).getGroundTemMaxExtremum(), dataDOMap.get(2).getGroundTemMaxExtremum(), dataDOMap.get(3).getGroundTemMaxExtremum(), dataDOMap.get(4).getGroundTemMaxExtremum(),
                dataDOMap.get(5).getGroundTemMaxExtremum(), dataDOMap.get(6).getGroundTemMaxExtremum(), dataDOMap.get(7).getGroundTemMaxExtremum(), dataDOMap.get(8).getGroundTemMaxExtremum(),
                dataDOMap.get(9).getGroundTemMaxExtremum(), dataDOMap.get(10).getGroundTemMaxExtremum(), dataDOMap.get(11).getGroundTemMaxExtremum(), dataDOMap.get(12).getGroundTemMaxExtremum(), dataDOMap.get(13).getGroundTemMaxExtremum());
        //地温-极端最高-日期
        generateFrontImportModel(importModelList, dataDOMap.get(1).getGroundTemMaxDate(), dataDOMap.get(2).getGroundTemMaxDate(), dataDOMap.get(3).getGroundTemMaxDate(), dataDOMap.get(4).getGroundTemMaxDate(),
                dataDOMap.get(5).getGroundTemMaxDate(), dataDOMap.get(6).getGroundTemMaxDate(), dataDOMap.get(7).getGroundTemMaxDate(), dataDOMap.get(8).getGroundTemMaxDate(),
                dataDOMap.get(9).getGroundTemMaxDate(), dataDOMap.get(10).getGroundTemMaxDate(), dataDOMap.get(11).getGroundTemMaxDate(), dataDOMap.get(12).getGroundTemMaxDate(), dataDOMap.get(13).getGroundTemMaxDate());
        //地温-极端最高-年份
        generateFrontImportModel(importModelList, dataDOMap.get(1).getGroundTemMaxYear(), dataDOMap.get(2).getGroundTemMaxYear(), dataDOMap.get(3).getGroundTemMaxYear(), dataDOMap.get(4).getGroundTemMaxYear(),
                dataDOMap.get(5).getGroundTemMaxYear(), dataDOMap.get(6).getGroundTemMaxYear(), dataDOMap.get(7).getGroundTemMaxYear(), dataDOMap.get(8).getGroundTemMaxYear(),
                dataDOMap.get(9).getGroundTemMaxYear(), dataDOMap.get(10).getGroundTemMaxYear(), dataDOMap.get(11).getGroundTemMaxYear(), dataDOMap.get(12).getGroundTemMaxYear(), dataDOMap.get(13).getGroundTemMaxYear());
        //地温-极端最低-极值
        generateFrontImportModel(importModelList, dataDOMap.get(1).getGroundTemMinExtremum(), dataDOMap.get(2).getGroundTemMinExtremum(), dataDOMap.get(3).getGroundTemMinExtremum(), dataDOMap.get(4).getGroundTemMinExtremum(),
                dataDOMap.get(5).getGroundTemMinExtremum(), dataDOMap.get(6).getGroundTemMinExtremum(), dataDOMap.get(7).getGroundTemMinExtremum(), dataDOMap.get(8).getGroundTemMinExtremum(),
                dataDOMap.get(9).getGroundTemMinExtremum(), dataDOMap.get(10).getGroundTemMinExtremum(), dataDOMap.get(11).getGroundTemMinExtremum(), dataDOMap.get(12).getGroundTemMinExtremum(), dataDOMap.get(13).getGroundTemMinExtremum());
        //地温-极端最低-日期
        generateFrontImportModel(importModelList, dataDOMap.get(1).getGroundTemMinDate(), dataDOMap.get(2).getGroundTemMinDate(), dataDOMap.get(3).getGroundTemMinDate(), dataDOMap.get(4).getGroundTemMinDate(),
                dataDOMap.get(5).getGroundTemMinDate(), dataDOMap.get(6).getGroundTemMinDate(), dataDOMap.get(7).getGroundTemMinDate(), dataDOMap.get(8).getGroundTemMinDate(),
                dataDOMap.get(9).getGroundTemMinDate(), dataDOMap.get(10).getGroundTemMinDate(), dataDOMap.get(11).getGroundTemMinDate(), dataDOMap.get(12).getGroundTemMinDate(), dataDOMap.get(13).getGroundTemMinDate());
        //地温-极端最低-年份
        generateFrontImportModel(importModelList, dataDOMap.get(1).getGroundTemMinYear(), dataDOMap.get(2).getGroundTemMinYear(), dataDOMap.get(3).getGroundTemMinYear(), dataDOMap.get(4).getGroundTemMinYear(),
                dataDOMap.get(5).getGroundTemMinYear(), dataDOMap.get(6).getGroundTemMinYear(), dataDOMap.get(7).getGroundTemMinYear(), dataDOMap.get(8).getGroundTemMinYear(),
                dataDOMap.get(9).getGroundTemMinYear(), dataDOMap.get(10).getGroundTemMinYear(), dataDOMap.get(11).getGroundTemMinYear(), dataDOMap.get(12).getGroundTemMinYear(), dataDOMap.get(13).getGroundTemMinYear());
        //湿度-平均相对湿度
        generateFrontImportModel(importModelList, dataDOMap.get(1).getHumidityAverageValue(), dataDOMap.get(2).getHumidityAverageValue(), dataDOMap.get(3).getHumidityAverageValue(), dataDOMap.get(4).getHumidityAverageValue(),
                dataDOMap.get(5).getHumidityAverageValue(), dataDOMap.get(6).getHumidityAverageValue(), dataDOMap.get(7).getHumidityAverageValue(), dataDOMap.get(8).getHumidityAverageValue(),
                dataDOMap.get(9).getHumidityAverageValue(), dataDOMap.get(10).getHumidityAverageValue(), dataDOMap.get(11).getHumidityAverageValue(), dataDOMap.get(12).getHumidityAverageValue(), dataDOMap.get(13).getHumidityAverageValue());
        //湿度-日最小湿度
        generateFrontImportModel(importModelList, dataDOMap.get(1).getHumidityDayMin(), dataDOMap.get(2).getHumidityDayMin(), dataDOMap.get(3).getHumidityDayMin(), dataDOMap.get(4).getHumidityDayMin(),
                dataDOMap.get(5).getHumidityDayMin(), dataDOMap.get(6).getHumidityDayMin(), dataDOMap.get(7).getHumidityDayMin(), dataDOMap.get(8).getHumidityDayMin(),
                dataDOMap.get(9).getHumidityDayMin(), dataDOMap.get(10).getHumidityDayMin(), dataDOMap.get(11).getHumidityDayMin(), dataDOMap.get(12).getHumidityDayMin(), dataDOMap.get(13).getHumidityDayMin());
        //积雪深度-极端最高-极值
        generateFrontImportModel(importModelList, dataDOMap.get(1).getSnowDepthMaxExtremum(), dataDOMap.get(2).getSnowDepthMaxExtremum(), dataDOMap.get(3).getSnowDepthMaxExtremum(), dataDOMap.get(4).getSnowDepthMaxExtremum(),
                dataDOMap.get(5).getSnowDepthMaxExtremum(), dataDOMap.get(6).getSnowDepthMaxExtremum(), dataDOMap.get(7).getSnowDepthMaxExtremum(), dataDOMap.get(8).getSnowDepthMaxExtremum(),
                dataDOMap.get(9).getSnowDepthMaxExtremum(), dataDOMap.get(10).getSnowDepthMaxExtremum(), dataDOMap.get(11).getSnowDepthMaxExtremum(), dataDOMap.get(12).getSnowDepthMaxExtremum(), dataDOMap.get(13).getSnowDepthMaxExtremum());
        //积雪深度-极端最高-日期
        generateFrontImportModel(importModelList, dataDOMap.get(1).getSnowDepthMaxDate(), dataDOMap.get(2).getSnowDepthMaxDate(), dataDOMap.get(3).getSnowDepthMaxDate(), dataDOMap.get(4).getSnowDepthMaxDate(),
                dataDOMap.get(5).getSnowDepthMaxDate(), dataDOMap.get(6).getSnowDepthMaxDate(), dataDOMap.get(7).getSnowDepthMaxDate(), dataDOMap.get(8).getSnowDepthMaxDate(),
                dataDOMap.get(9).getSnowDepthMaxDate(), dataDOMap.get(10).getSnowDepthMaxDate(), dataDOMap.get(11).getSnowDepthMaxDate(), dataDOMap.get(12).getSnowDepthMaxDate(), dataDOMap.get(13).getSnowDepthMaxDate());
        //积雪深度-极端最高-年份
        generateFrontImportModel(importModelList, dataDOMap.get(1).getSnowDepthMaxYear(), dataDOMap.get(2).getSnowDepthMaxYear(), dataDOMap.get(3).getSnowDepthMaxYear(), dataDOMap.get(4).getSnowDepthMaxYear(),
                dataDOMap.get(5).getSnowDepthMaxYear(), dataDOMap.get(6).getSnowDepthMaxYear(), dataDOMap.get(7).getSnowDepthMaxYear(), dataDOMap.get(8).getSnowDepthMaxYear(),
                dataDOMap.get(9).getSnowDepthMaxYear(), dataDOMap.get(10).getSnowDepthMaxYear(), dataDOMap.get(11).getSnowDepthMaxYear(), dataDOMap.get(12).getSnowDepthMaxYear(), dataDOMap.get(13).getSnowDepthMaxYear());
        //冻土-最大深度-极值
        generateFrontImportModel(importModelList, dataDOMap.get(1).getFrozenDepthMaxExtremum(), dataDOMap.get(2).getFrozenDepthMaxExtremum(), dataDOMap.get(3).getFrozenDepthMaxExtremum(), dataDOMap.get(4).getFrozenDepthMaxExtremum(),
                dataDOMap.get(5).getFrozenDepthMaxExtremum(), dataDOMap.get(6).getFrozenDepthMaxExtremum(), dataDOMap.get(7).getFrozenDepthMaxExtremum(), dataDOMap.get(8).getFrozenDepthMaxExtremum(),
                dataDOMap.get(9).getFrozenDepthMaxExtremum(), dataDOMap.get(10).getFrozenDepthMaxExtremum(), dataDOMap.get(11).getFrozenDepthMaxExtremum(), dataDOMap.get(12).getFrozenDepthMaxExtremum(), dataDOMap.get(13).getFrozenDepthMaxExtremum());
        //冻土-最大深度-日期
        generateFrontImportModel(importModelList, dataDOMap.get(1).getFrozenDepthMaxDate(), dataDOMap.get(2).getFrozenDepthMaxDate(), dataDOMap.get(3).getFrozenDepthMaxDate(), dataDOMap.get(4).getFrozenDepthMaxDate(),
                dataDOMap.get(5).getFrozenDepthMaxDate(), dataDOMap.get(6).getFrozenDepthMaxDate(), dataDOMap.get(7).getFrozenDepthMaxDate(), dataDOMap.get(8).getFrozenDepthMaxDate(),
                dataDOMap.get(9).getFrozenDepthMaxDate(), dataDOMap.get(10).getFrozenDepthMaxDate(), dataDOMap.get(11).getFrozenDepthMaxDate(), dataDOMap.get(12).getFrozenDepthMaxDate(), dataDOMap.get(13).getFrozenDepthMaxDate());
        // 冻土-最大深度-年份
        generateFrontImportModel(importModelList, dataDOMap.get(1).getFrozenDepthMaxYear(), dataDOMap.get(2).getFrozenDepthMaxYear(), dataDOMap.get(3).getFrozenDepthMaxYear(), dataDOMap.get(4).getFrozenDepthMaxYear(),
                dataDOMap.get(5).getFrozenDepthMaxYear(), dataDOMap.get(6).getFrozenDepthMaxYear(), dataDOMap.get(7).getFrozenDepthMaxYear(), dataDOMap.get(8).getFrozenDepthMaxYear(),
                dataDOMap.get(9).getFrozenDepthMaxYear(), dataDOMap.get(10).getFrozenDepthMaxYear(), dataDOMap.get(11).getFrozenDepthMaxYear(), dataDOMap.get(12).getFrozenDepthMaxYear(), dataDOMap.get(13).getFrozenDepthMaxYear());
        //平均气压
        generateFrontImportModel(importModelList, dataDOMap.get(1).getAverageAirPressure(), dataDOMap.get(2).getAverageAirPressure(), dataDOMap.get(3).getAverageAirPressure(), dataDOMap.get(4).getAverageAirPressure(),
                dataDOMap.get(5).getAverageAirPressure(), dataDOMap.get(6).getAverageAirPressure(), dataDOMap.get(7).getAverageAirPressure(), dataDOMap.get(8).getAverageAirPressure(),
                dataDOMap.get(9).getAverageAirPressure(), dataDOMap.get(10).getAverageAirPressure(), dataDOMap.get(11).getAverageAirPressure(), dataDOMap.get(12).getAverageAirPressure(), dataDOMap.get(13).getAverageAirPressure());
        //日照时数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getSunlightHours(), dataDOMap.get(2).getSunlightHours(), dataDOMap.get(3).getSunlightHours(), dataDOMap.get(4).getSunlightHours(),
                dataDOMap.get(5).getSunlightHours(), dataDOMap.get(6).getSunlightHours(), dataDOMap.get(7).getSunlightHours(), dataDOMap.get(8).getSunlightHours(),
                dataDOMap.get(9).getSunlightHours(), dataDOMap.get(10).getSunlightHours(), dataDOMap.get(11).getSunlightHours(), dataDOMap.get(12).getSunlightHours(), dataDOMap.get(13).getSunlightHours());
        //雾日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getNumberOfFoggyDays(), dataDOMap.get(2).getNumberOfFoggyDays(), dataDOMap.get(3).getNumberOfFoggyDays(), dataDOMap.get(4).getNumberOfFoggyDays(),
                dataDOMap.get(5).getNumberOfFoggyDays(), dataDOMap.get(6).getNumberOfFoggyDays(), dataDOMap.get(7).getNumberOfFoggyDays(), dataDOMap.get(8).getNumberOfFoggyDays(),
                dataDOMap.get(9).getNumberOfFoggyDays(), dataDOMap.get(10).getNumberOfFoggyDays(), dataDOMap.get(11).getNumberOfFoggyDays(), dataDOMap.get(12).getNumberOfFoggyDays(), dataDOMap.get(13).getNumberOfFoggyDays());
        //冰雹日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getNumberOfHailDays(), dataDOMap.get(2).getNumberOfHailDays(), dataDOMap.get(3).getNumberOfHailDays(), dataDOMap.get(4).getNumberOfHailDays(),
                dataDOMap.get(5).getNumberOfHailDays(), dataDOMap.get(6).getNumberOfHailDays(), dataDOMap.get(7).getNumberOfHailDays(), dataDOMap.get(8).getNumberOfHailDays(),
                dataDOMap.get(9).getNumberOfHailDays(), dataDOMap.get(10).getNumberOfHailDays(), dataDOMap.get(11).getNumberOfHailDays(), dataDOMap.get(12).getNumberOfHailDays(), dataDOMap.get(13).getNumberOfHailDays());
        //雷暴日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getNumberOfThunderstormDays(), dataDOMap.get(2).getNumberOfThunderstormDays(), dataDOMap.get(3).getNumberOfThunderstormDays(), dataDOMap.get(4).getNumberOfThunderstormDays(),
                dataDOMap.get(5).getNumberOfThunderstormDays(), dataDOMap.get(6).getNumberOfThunderstormDays(), dataDOMap.get(7).getNumberOfThunderstormDays(), dataDOMap.get(8).getNumberOfThunderstormDays(),
                dataDOMap.get(9).getNumberOfThunderstormDays(), dataDOMap.get(10).getNumberOfThunderstormDays(), dataDOMap.get(11).getNumberOfThunderstormDays(), dataDOMap.get(12).getNumberOfThunderstormDays(), dataDOMap.get(13).getNumberOfThunderstormDays());
        //降雪日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getNumberOfSnowfallDays(), dataDOMap.get(2).getNumberOfSnowfallDays(), dataDOMap.get(3).getNumberOfSnowfallDays(), dataDOMap.get(4).getNumberOfSnowfallDays(),
                dataDOMap.get(5).getNumberOfSnowfallDays(), dataDOMap.get(6).getNumberOfSnowfallDays(), dataDOMap.get(7).getNumberOfSnowfallDays(), dataDOMap.get(8).getNumberOfSnowfallDays(),
                dataDOMap.get(9).getNumberOfSnowfallDays(), dataDOMap.get(10).getNumberOfSnowfallDays(), dataDOMap.get(11).getNumberOfSnowfallDays(), dataDOMap.get(12).getNumberOfSnowfallDays(), dataDOMap.get(13).getNumberOfSnowfallDays());
        //积雪日数
        generateFrontImportModel(importModelList, dataDOMap.get(1).getNumberOfSnowCoveredDays(), dataDOMap.get(2).getNumberOfSnowCoveredDays(), dataDOMap.get(3).getNumberOfSnowCoveredDays(), dataDOMap.get(4).getNumberOfSnowCoveredDays(),
                dataDOMap.get(5).getNumberOfSnowCoveredDays(), dataDOMap.get(6).getNumberOfSnowCoveredDays(), dataDOMap.get(7).getNumberOfSnowCoveredDays(), dataDOMap.get(8).getNumberOfSnowCoveredDays(),
                dataDOMap.get(9).getNumberOfSnowCoveredDays(), dataDOMap.get(10).getNumberOfSnowCoveredDays(), dataDOMap.get(11).getNumberOfSnowCoveredDays(), dataDOMap.get(12).getNumberOfSnowCoveredDays(), dataDOMap.get(13).getNumberOfSnowCoveredDays());
        //蒸发量
        generateFrontImportModel(importModelList, dataDOMap.get(1).getEvaporationCapacity(), dataDOMap.get(2).getEvaporationCapacity(), dataDOMap.get(3).getEvaporationCapacity(), dataDOMap.get(4).getEvaporationCapacity(),
                dataDOMap.get(5).getEvaporationCapacity(), dataDOMap.get(6).getEvaporationCapacity(), dataDOMap.get(7).getEvaporationCapacity(), dataDOMap.get(8).getEvaporationCapacity(),
                dataDOMap.get(9).getEvaporationCapacity(), dataDOMap.get(10).getEvaporationCapacity(), dataDOMap.get(11).getEvaporationCapacity(), dataDOMap.get(12).getEvaporationCapacity(), dataDOMap.get(13).getEvaporationCapacity());
        return importModelList;
    }


    /**
     * 组装前端数据
     *
     * @param importModelList
     * @param value1
     * @param value2
     * @param value3
     * @param value4
     * @param value5
     * @param value6
     * @param value7
     * @param value8
     * @param value9
     * @param value10
     * @param value11
     * @param value12
     * @param value13
     */
    private void generateFrontImportModel(List<WeatherDataImportModel> importModelList, String value1, String value2, String value3, String value4, String value5, String value6,
                                          String value7, String value8, String value9, String value10, String value11, String value12, String value13) {
        importModelList.add(WeatherDataImportModel.builder().value1(value1).value2(value2).value3(value3).value4(value4).value5(value5).value6(value6).value7(value7).value8(value8)
                .value9(value9).value10(value10).value11(value11).value12(value12).value13(value13).build());
    }


    @Override
    public List<WeatherDataDO> findWeatherDataDOList(Long stationId, Long logId) {
        //查询最新的
        Integer version = null, latest = null;
        StationDataLogDO dataLogDO;
        if (Objects.nonNull(logId)) {
            dataLogDO = stationDataLogService.findById(logId);
            if (Objects.nonNull(dataLogDO) && !Objects.equals(StationEnum.WEATHER.getType(), dataLogDO.getStationType())) {
                dataLogDO = null;
            }
            if (Objects.isNull(dataLogDO)) {
                return new ArrayList<>();
            }
            version = dataLogDO.getCurrentVersion();
        } else {
            latest = LatestEnum.LATEST.getType();
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("station_id", stationId);
        if (Objects.nonNull(version)) {
            queryMap.put("version", version);
        }
        if (Objects.nonNull(latest)) {
            queryMap.put("latest", latest);
        }
        return weatherDataMapper.selectByMap(queryMap);
    }

    @Override
    public void exportWeatherDataExcel(HttpServletResponse response, Long stationId, Long logId) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(StationDataTypeEnum.WEATHER_STATION_DATA.getDesc() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            List<WeatherDataImportModel> importModelList = findWeatherDataImportModelList(stationId, logId);
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + StationDataTypeEnumV2.WEATHER_METEOROLOGICAL_DATA_STATISTICS.getCode() + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream()) // 利用模板的输出流
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            excelWriter.fill(importModelList, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("exportWeatherDayExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }

    @Override
    public List<WeatherDataDO> listByStationIds(List<Long> stationIds) {
        return weatherDataMapper.listByStationIds(stationIds);
    }

    @Override
    public void insertList(List<WeatherDataDO> weatherDataDOS) {
        weatherDataMapper.insertList(weatherDataDOS);
    }

    /**
     * 处理数据到数据库
     *
     * @param stationId
     * @param importDataList
     * @param headRemarkLine
     */
    private void consoleDataToDb(Long stationId, List<WeatherDataImportModel> importDataList, int headRemarkLine) {
        Integer version = findNextStationWeatherDataVersion(stationId);
        Map<Integer, WeatherDataDO> dataDOMap = new HashMap<>();
        for (int i = 1; i <= 13; i++) {
            WeatherDataDO dataDO = new WeatherDataDO();
            dataDO.setStationId(stationId);
            dataDO.setMonth(i);
            dataDO.setVersion(version);
            dataDO.setLatest(LatestEnum.LATEST.getType());
            dataDOMap.put(i, dataDO);
        }
        WeatherDataImportModel model = importDataList.get(0);
        if(model.getValue1()!=null&&model.getValue1().toString().equals("终止年份")){
            importDataList.remove(0);
            importDataList.remove(0);
        }
        for (int i = 0 + headRemarkLine; i < importDataList.size(); i++) {
            //气温-平均气温
            generateTemperatureAverageValue(i, dataDOMap, importDataList.get(i));
            //气温-极端最高-极值
            generateTemperatureMaxExtremum(i, dataDOMap, importDataList.get(i));
            //气温-极端最高-日期
            generateTemperatureMaxDate(i, dataDOMap, importDataList.get(i));
            //气温-极端最高-年份
            generateTemperatureMaxYear(i, dataDOMap, importDataList.get(i));
            //气温-极端最低-极值
            generateTemperatureMinExtremum(i, dataDOMap, importDataList.get(i));
            //气温-极端最低-日期
            generateTemperatureMinDate(i, dataDOMap, importDataList.get(i));
            //气温-极端最低-年份
            generateTemperatureMinYear(i, dataDOMap, importDataList.get(i));
            //降水量-平均降水量
            generatePrecipitationAverageValue(i, dataDOMap, importDataList.get(i));
            //降水量-日最大降水量
            generatePrecipitationMaxValue(i, dataDOMap, importDataList.get(i));
            //降水量-≥0.1mm日数
            generatePrecipitation01Days(i, dataDOMap, importDataList.get(i));
            //降水量-≥0.5mm日数
            generatePrecipitation05Days(i, dataDOMap, importDataList.get(i));
            //降水量-≥2mm日数
            generatePrecipitation2Days(i, dataDOMap, importDataList.get(i));
            //降水量-≥5mm日数
            generatePrecipitation5Days(i, dataDOMap, importDataList.get(i));
            //降水量-≥10mm日数
            generatePrecipitation10Days(i, dataDOMap, importDataList.get(i));
            //降水量-≥25mm日数
            generatePrecipitation25Days(i, dataDOMap, importDataList.get(i));
            //降水量-≥30mm日数
            generatePrecipitation30Days(i, dataDOMap, importDataList.get(i));
            //降水量-≥50mm日数
            generatePrecipitation50Days(i, dataDOMap, importDataList.get(i));
            //风速-主导风向-风向
            generateWindLeadDirection(i, dataDOMap, importDataList.get(i));
            //风速-主导风向-风向频率
            generateWindLeadDirectionFrequency(i, dataDOMap, importDataList.get(i));
            //风速-平均风速
            generateWindAverageValue(i, dataDOMap, importDataList.get(i));
            //风速-最大风速-极值
            generateWindMaxExtremum(i, dataDOMap, importDataList.get(i));
            //风速-最大风速-日期
            generateWindMaxDate(i, dataDOMap, importDataList.get(i));
            //风速-最大风速-年份
            generateWindMaxYear(i, dataDOMap, importDataList.get(i));
            //风速-最大风速-风向
            generateWindMaxDirection(i, dataDOMap, importDataList.get(i));
            //风速-风速日数->=5m/s
            generateWindDays5(i, dataDOMap, importDataList.get(i));
            //风速-风速日数->=10m/s
            generateWindDays10(i, dataDOMap, importDataList.get(i));
            //风速-风速日数->=12m/s
            generateWindDays12(i, dataDOMap, importDataList.get(i));
            //风速-风速日数->=15m/s
            generateWindDays15(i, dataDOMap, importDataList.get(i));
            //地温-平均地温
            generateGroundTemAverageValue(i, dataDOMap, importDataList.get(i));
            //地温-极端最高-极值
            generateGroundTemMaxExtremum(i, dataDOMap, importDataList.get(i));
            //地温-极端最高-日期
            generateGroundTemMaxDate(i, dataDOMap, importDataList.get(i));
            //地温-极端最高-年份
            generateGroundTemMaxYear(i, dataDOMap, importDataList.get(i));
            //地温-极端最低-极值
            generateGroundTemMinExtremum(i, dataDOMap, importDataList.get(i));
            //地温-极端最低-日期
            generateGroundTemMinDate(i, dataDOMap, importDataList.get(i));
            //地温-极端最低-年份
            generateGroundTemMinYear(i, dataDOMap, importDataList.get(i));
            //湿度-平均相对湿度
            generateHumidityAverageValue(i, dataDOMap, importDataList.get(i));
            //湿度-日最小湿度
            generateHumidityDayMin(i, dataDOMap, importDataList.get(i));
            //积雪深度-极端最高-极值
            generateSnowDepthMaxExtremum(i, dataDOMap, importDataList.get(i));
            //积雪深度-极端最高-日期
            generateSnowDepthMaxDate(i, dataDOMap, importDataList.get(i));
            //积雪深度-极端最高-年份
            generateSnowDepthMaxYear(i, dataDOMap, importDataList.get(i));
            //冻土-最大深度-极值
            generateFrozenDepthMaxExtremum(i, dataDOMap, importDataList.get(i));
            //冻土-最大深度-日期
            generateFrozenDepthMaxDate(i, dataDOMap, importDataList.get(i));
            //冻土-最大深度-年份
            generateFrozenDepthMaxYear(i, dataDOMap, importDataList.get(i));
            //平均气压
            generateAverageAirPressure(i, dataDOMap, importDataList.get(i));
            //日照时数
            generateSunlightHours(i, dataDOMap, importDataList.get(i));
            //雾日数
            generateNumberOfFoggyDays(i, dataDOMap, importDataList.get(i));
            //冰雹日数
            generateNumberOfHailDays(i, dataDOMap, importDataList.get(i));
            //雷暴日数
            generateNumberOfThunderstormDays(i, dataDOMap, importDataList.get(i));
            //降雪日数
            generateNumberOfSnowfallDays(i, dataDOMap, importDataList.get(i));
            //积雪日数
            generateNumberOfSnowCoveredDays(i, dataDOMap, importDataList.get(i));
            //蒸发量
            generateEvaporationCapacity(i, dataDOMap, importDataList.get(i));
        }
        //插入年数据
        StatisticalDirectoryDO statisticalDirectory = statisticalDirectoryService.getStatisticalDirectory(330101);
        insertBatch(new ArrayList<>(dataDOMap.values()));
        if(model.getValue1()!=null&&model.getValue1().equals("终止年份")){
            StatisticalInfoDO statisticalInfo = statisticalInfoService.getStatisticalInfo(stationId, statisticalDirectory.getId());
            StatisticalInfoDO statisticalInfoParent = statisticalInfoService.getStatisticalInfo(stationId, statisticalDirectory.getParentId());
            if (Objects.isNull(statisticalInfo)) {
                statisticalInfo=new StatisticalInfoDO();
                statisticalInfo.setStationId(stationId);
                statisticalInfo.setStatsId(statisticalDirectory.getId());
                statisticalInfo.setStartYear(Integer.parseInt(model.getStartYear()));
                statisticalInfo.setEndYear(Integer.parseInt(model.getValue2()));
                statisticalInfo.setYearCount(1);
                statisticalInfo.setYears(Integer.parseInt(model.getStartYear())+","+Integer.parseInt(model.getValue2()));
                statisticalInfo.setStationType(3);
                statisticalInfoService.createStatisticalInfo(statisticalInfo);
            }else{
                statisticalInfo.setStartYear(Integer.parseInt(model.getStartYear()));
                statisticalInfo.setEndYear(Integer.parseInt(model.getValue2()));
                statisticalInfo.setYearCount(1);
                statisticalInfo.setYears(Integer.parseInt(model.getStartYear())+","+Integer.parseInt(model.getValue2()));
                statisticalInfoService.updateStatisticalInfo(statisticalInfo);
            }
            if (Objects.isNull(statisticalInfoParent)) {
                statisticalInfoParent = new StatisticalInfoDO();
                statisticalInfoParent.setStationId(stationId);
                statisticalInfoParent.setStatsId(statisticalDirectory.getParentId());
                statisticalInfoParent.setStartYear(Integer.parseInt(model.getStartYear()));
                statisticalInfoParent.setEndYear(Integer.parseInt(model.getValue2()));
                statisticalInfoParent.setYearCount(1);
                statisticalInfoParent.setYears(Integer.parseInt(model.getStartYear())+","+Integer.parseInt(model.getValue2()));
                statisticalInfoParent.setStationType(3);
                statisticalInfoService.createStatisticalInfo(statisticalInfoParent);
            } else {
                //如果年份数跟以前相同则不更新
                statisticalInfoParent.setStartYear(Integer.parseInt(model.getStartYear()));
                statisticalInfoParent.setEndYear(Integer.parseInt(model.getValue2()));
                statisticalInfoParent.setYearCount(1);
                statisticalInfoParent.setYears(Integer.parseInt(model.getStartYear())+","+Integer.parseInt(model.getValue2()));
                statisticalInfoService.updateStatisticalInfo(statisticalInfoParent);
            }
        }
    }

    @Autowired
    private StatisticalDirectoryService statisticalDirectoryService;
    @Autowired
    private StatisticalInfoService statisticalInfoService;
    /**
     * //蒸发量
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateEvaporationCapacity(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 50) {
            return;
        }
        dataDOMap.get(1).setEvaporationCapacity(importModel.getValue1());
        dataDOMap.get(2).setEvaporationCapacity(importModel.getValue2());
        dataDOMap.get(3).setEvaporationCapacity(importModel.getValue3());
        dataDOMap.get(4).setEvaporationCapacity(importModel.getValue4());
        dataDOMap.get(5).setEvaporationCapacity(importModel.getValue5());
        dataDOMap.get(6).setEvaporationCapacity(importModel.getValue6());
        dataDOMap.get(7).setEvaporationCapacity(importModel.getValue7());
        dataDOMap.get(8).setEvaporationCapacity(importModel.getValue8());
        dataDOMap.get(9).setEvaporationCapacity(importModel.getValue9());
        dataDOMap.get(10).setEvaporationCapacity(importModel.getValue10());
        dataDOMap.get(11).setEvaporationCapacity(importModel.getValue11());
        dataDOMap.get(12).setEvaporationCapacity(importModel.getValue12());
        dataDOMap.get(13).setEvaporationCapacity(importModel.getValue13());
    }

    /**
     * //积雪日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateNumberOfSnowCoveredDays(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 49) {
            return;
        }
        dataDOMap.get(1).setNumberOfSnowCoveredDays(importModel.getValue1());
        dataDOMap.get(2).setNumberOfSnowCoveredDays(importModel.getValue2());
        dataDOMap.get(3).setNumberOfSnowCoveredDays(importModel.getValue3());
        dataDOMap.get(4).setNumberOfSnowCoveredDays(importModel.getValue4());
        dataDOMap.get(5).setNumberOfSnowCoveredDays(importModel.getValue5());
        dataDOMap.get(6).setNumberOfSnowCoveredDays(importModel.getValue6());
        dataDOMap.get(7).setNumberOfSnowCoveredDays(importModel.getValue7());
        dataDOMap.get(8).setNumberOfSnowCoveredDays(importModel.getValue8());
        dataDOMap.get(9).setNumberOfSnowCoveredDays(importModel.getValue9());
        dataDOMap.get(10).setNumberOfSnowCoveredDays(importModel.getValue10());
        dataDOMap.get(11).setNumberOfSnowCoveredDays(importModel.getValue11());
        dataDOMap.get(12).setNumberOfSnowCoveredDays(importModel.getValue12());
        dataDOMap.get(13).setNumberOfSnowCoveredDays(importModel.getValue13());
    }

    /**
     * //降雪日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateNumberOfSnowfallDays(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 48) {
            return;
        }
        dataDOMap.get(1).setNumberOfSnowfallDays(importModel.getValue1());
        dataDOMap.get(2).setNumberOfSnowfallDays(importModel.getValue2());
        dataDOMap.get(3).setNumberOfSnowfallDays(importModel.getValue3());
        dataDOMap.get(4).setNumberOfSnowfallDays(importModel.getValue4());
        dataDOMap.get(5).setNumberOfSnowfallDays(importModel.getValue5());
        dataDOMap.get(6).setNumberOfSnowfallDays(importModel.getValue6());
        dataDOMap.get(7).setNumberOfSnowfallDays(importModel.getValue7());
        dataDOMap.get(8).setNumberOfSnowfallDays(importModel.getValue8());
        dataDOMap.get(9).setNumberOfSnowfallDays(importModel.getValue9());
        dataDOMap.get(10).setNumberOfSnowfallDays(importModel.getValue10());
        dataDOMap.get(11).setNumberOfSnowfallDays(importModel.getValue11());
        dataDOMap.get(12).setNumberOfSnowfallDays(importModel.getValue12());
        dataDOMap.get(13).setNumberOfSnowfallDays(importModel.getValue13());
    }

    /**
     * //雷暴日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateNumberOfThunderstormDays(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 47) {
            return;
        }
        dataDOMap.get(1).setNumberOfThunderstormDays(importModel.getValue1());
        dataDOMap.get(2).setNumberOfThunderstormDays(importModel.getValue2());
        dataDOMap.get(3).setNumberOfThunderstormDays(importModel.getValue3());
        dataDOMap.get(4).setNumberOfThunderstormDays(importModel.getValue4());
        dataDOMap.get(5).setNumberOfThunderstormDays(importModel.getValue5());
        dataDOMap.get(6).setNumberOfThunderstormDays(importModel.getValue6());
        dataDOMap.get(7).setNumberOfThunderstormDays(importModel.getValue7());
        dataDOMap.get(8).setNumberOfThunderstormDays(importModel.getValue8());
        dataDOMap.get(9).setNumberOfThunderstormDays(importModel.getValue9());
        dataDOMap.get(10).setNumberOfThunderstormDays(importModel.getValue10());
        dataDOMap.get(11).setNumberOfThunderstormDays(importModel.getValue11());
        dataDOMap.get(12).setNumberOfThunderstormDays(importModel.getValue12());
        dataDOMap.get(13).setNumberOfThunderstormDays(importModel.getValue13());
    }

    /**
     * //冰雹日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateNumberOfHailDays(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 46) {
            return;
        }
        dataDOMap.get(1).setNumberOfHailDays(importModel.getValue1());
        dataDOMap.get(2).setNumberOfHailDays(importModel.getValue2());
        dataDOMap.get(3).setNumberOfHailDays(importModel.getValue3());
        dataDOMap.get(4).setNumberOfHailDays(importModel.getValue4());
        dataDOMap.get(5).setNumberOfHailDays(importModel.getValue5());
        dataDOMap.get(6).setNumberOfHailDays(importModel.getValue6());
        dataDOMap.get(7).setNumberOfHailDays(importModel.getValue7());
        dataDOMap.get(8).setNumberOfHailDays(importModel.getValue8());
        dataDOMap.get(9).setNumberOfHailDays(importModel.getValue9());
        dataDOMap.get(10).setNumberOfHailDays(importModel.getValue10());
        dataDOMap.get(11).setNumberOfHailDays(importModel.getValue11());
        dataDOMap.get(12).setNumberOfHailDays(importModel.getValue12());
        dataDOMap.get(13).setNumberOfHailDays(importModel.getValue13());
    }

    /**
     * //雾日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateNumberOfFoggyDays(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 45) {
            return;
        }
        dataDOMap.get(1).setNumberOfFoggyDays(importModel.getValue1());
        dataDOMap.get(2).setNumberOfFoggyDays(importModel.getValue2());
        dataDOMap.get(3).setNumberOfFoggyDays(importModel.getValue3());
        dataDOMap.get(4).setNumberOfFoggyDays(importModel.getValue4());
        dataDOMap.get(5).setNumberOfFoggyDays(importModel.getValue5());
        dataDOMap.get(6).setNumberOfFoggyDays(importModel.getValue6());
        dataDOMap.get(7).setNumberOfFoggyDays(importModel.getValue7());
        dataDOMap.get(8).setNumberOfFoggyDays(importModel.getValue8());
        dataDOMap.get(9).setNumberOfFoggyDays(importModel.getValue9());
        dataDOMap.get(10).setNumberOfFoggyDays(importModel.getValue10());
        dataDOMap.get(11).setNumberOfFoggyDays(importModel.getValue11());
        dataDOMap.get(12).setNumberOfFoggyDays(importModel.getValue12());
        dataDOMap.get(13).setNumberOfFoggyDays(importModel.getValue13());
    }

    /**
     * //日照时数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateSunlightHours(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 44) {
            return;
        }
        dataDOMap.get(1).setSunlightHours(importModel.getValue1());
        dataDOMap.get(2).setSunlightHours(importModel.getValue2());
        dataDOMap.get(3).setSunlightHours(importModel.getValue3());
        dataDOMap.get(4).setSunlightHours(importModel.getValue4());
        dataDOMap.get(5).setSunlightHours(importModel.getValue5());
        dataDOMap.get(6).setSunlightHours(importModel.getValue6());
        dataDOMap.get(7).setSunlightHours(importModel.getValue7());
        dataDOMap.get(8).setSunlightHours(importModel.getValue8());
        dataDOMap.get(9).setSunlightHours(importModel.getValue9());
        dataDOMap.get(10).setSunlightHours(importModel.getValue10());
        dataDOMap.get(11).setSunlightHours(importModel.getValue11());
        dataDOMap.get(12).setSunlightHours(importModel.getValue12());
        dataDOMap.get(13).setSunlightHours(importModel.getValue13());
    }

    /**
     * //平均气压
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateAverageAirPressure(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 43) {
            return;
        }
        dataDOMap.get(1).setAverageAirPressure(importModel.getValue1());
        dataDOMap.get(2).setAverageAirPressure(importModel.getValue2());
        dataDOMap.get(3).setAverageAirPressure(importModel.getValue3());
        dataDOMap.get(4).setAverageAirPressure(importModel.getValue4());
        dataDOMap.get(5).setAverageAirPressure(importModel.getValue5());
        dataDOMap.get(6).setAverageAirPressure(importModel.getValue6());
        dataDOMap.get(7).setAverageAirPressure(importModel.getValue7());
        dataDOMap.get(8).setAverageAirPressure(importModel.getValue8());
        dataDOMap.get(9).setAverageAirPressure(importModel.getValue9());
        dataDOMap.get(10).setAverageAirPressure(importModel.getValue10());
        dataDOMap.get(11).setAverageAirPressure(importModel.getValue11());
        dataDOMap.get(12).setAverageAirPressure(importModel.getValue12());
        dataDOMap.get(13).setAverageAirPressure(importModel.getValue13());
    }

    /**
     * //冻土-最大深度-年份
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateFrozenDepthMaxYear(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 42) {
            return;
        }
        dataDOMap.get(1).setFrozenDepthMaxYear(importModel.getValue1());
        dataDOMap.get(2).setFrozenDepthMaxYear(importModel.getValue2());
        dataDOMap.get(3).setFrozenDepthMaxYear(importModel.getValue3());
        dataDOMap.get(4).setFrozenDepthMaxYear(importModel.getValue4());
        dataDOMap.get(5).setFrozenDepthMaxYear(importModel.getValue5());
        dataDOMap.get(6).setFrozenDepthMaxYear(importModel.getValue6());
        dataDOMap.get(7).setFrozenDepthMaxYear(importModel.getValue7());
        dataDOMap.get(8).setFrozenDepthMaxYear(importModel.getValue8());
        dataDOMap.get(9).setFrozenDepthMaxYear(importModel.getValue9());
        dataDOMap.get(10).setFrozenDepthMaxYear(importModel.getValue10());
        dataDOMap.get(11).setFrozenDepthMaxYear(importModel.getValue11());
        dataDOMap.get(12).setFrozenDepthMaxYear(importModel.getValue12());
        dataDOMap.get(13).setFrozenDepthMaxYear(importModel.getValue13());
    }

    /**
     * //冻土-最大深度-日期
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateFrozenDepthMaxDate(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 41) {
            return;
        }
        dataDOMap.get(1).setFrozenDepthMaxDate(importModel.getValue1());
        dataDOMap.get(2).setFrozenDepthMaxDate(importModel.getValue2());
        dataDOMap.get(3).setFrozenDepthMaxDate(importModel.getValue3());
        dataDOMap.get(4).setFrozenDepthMaxDate(importModel.getValue4());
        dataDOMap.get(5).setFrozenDepthMaxDate(importModel.getValue5());
        dataDOMap.get(6).setFrozenDepthMaxDate(importModel.getValue6());
        dataDOMap.get(7).setFrozenDepthMaxDate(importModel.getValue7());
        dataDOMap.get(8).setFrozenDepthMaxDate(importModel.getValue8());
        dataDOMap.get(9).setFrozenDepthMaxDate(importModel.getValue9());
        dataDOMap.get(10).setFrozenDepthMaxDate(importModel.getValue10());
        dataDOMap.get(11).setFrozenDepthMaxDate(importModel.getValue11());
        dataDOMap.get(12).setFrozenDepthMaxDate(importModel.getValue12());
        dataDOMap.get(13).setFrozenDepthMaxDate(importModel.getValue13());
    }

    /**
     * //冻土-最大深度-极值
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateFrozenDepthMaxExtremum(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 40) {
            return;
        }
        dataDOMap.get(1).setFrozenDepthMaxExtremum(importModel.getValue1());
        dataDOMap.get(2).setFrozenDepthMaxExtremum(importModel.getValue2());
        dataDOMap.get(3).setFrozenDepthMaxExtremum(importModel.getValue3());
        dataDOMap.get(4).setFrozenDepthMaxExtremum(importModel.getValue4());
        dataDOMap.get(5).setFrozenDepthMaxExtremum(importModel.getValue5());
        dataDOMap.get(6).setFrozenDepthMaxExtremum(importModel.getValue6());
        dataDOMap.get(7).setFrozenDepthMaxExtremum(importModel.getValue7());
        dataDOMap.get(8).setFrozenDepthMaxExtremum(importModel.getValue8());
        dataDOMap.get(9).setFrozenDepthMaxExtremum(importModel.getValue9());
        dataDOMap.get(10).setFrozenDepthMaxExtremum(importModel.getValue10());
        dataDOMap.get(11).setFrozenDepthMaxExtremum(importModel.getValue11());
        dataDOMap.get(12).setFrozenDepthMaxExtremum(importModel.getValue12());
        dataDOMap.get(13).setFrozenDepthMaxExtremum(importModel.getValue13());
    }

    /**
     * //积雪深度-极端最高-年份
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateSnowDepthMaxYear(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 39) {
            return;
        }
        dataDOMap.get(1).setSnowDepthMaxYear(importModel.getValue1());
        dataDOMap.get(2).setSnowDepthMaxYear(importModel.getValue2());
        dataDOMap.get(3).setSnowDepthMaxYear(importModel.getValue3());
        dataDOMap.get(4).setSnowDepthMaxYear(importModel.getValue4());
        dataDOMap.get(5).setSnowDepthMaxYear(importModel.getValue5());
        dataDOMap.get(6).setSnowDepthMaxYear(importModel.getValue6());
        dataDOMap.get(7).setSnowDepthMaxYear(importModel.getValue7());
        dataDOMap.get(8).setSnowDepthMaxYear(importModel.getValue8());
        dataDOMap.get(9).setSnowDepthMaxYear(importModel.getValue9());
        dataDOMap.get(10).setSnowDepthMaxYear(importModel.getValue10());
        dataDOMap.get(11).setSnowDepthMaxYear(importModel.getValue11());
        dataDOMap.get(12).setSnowDepthMaxYear(importModel.getValue12());
        dataDOMap.get(13).setSnowDepthMaxYear(importModel.getValue13());
    }

    /**
     * //积雪深度-极端最高-日期
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateSnowDepthMaxDate(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 38) {
            return;
        }
        dataDOMap.get(1).setSnowDepthMaxDate(importModel.getValue1());
        dataDOMap.get(2).setSnowDepthMaxDate(importModel.getValue2());
        dataDOMap.get(3).setSnowDepthMaxDate(importModel.getValue3());
        dataDOMap.get(4).setSnowDepthMaxDate(importModel.getValue4());
        dataDOMap.get(5).setSnowDepthMaxDate(importModel.getValue5());
        dataDOMap.get(6).setSnowDepthMaxDate(importModel.getValue6());
        dataDOMap.get(7).setSnowDepthMaxDate(importModel.getValue7());
        dataDOMap.get(8).setSnowDepthMaxDate(importModel.getValue8());
        dataDOMap.get(9).setSnowDepthMaxDate(importModel.getValue9());
        dataDOMap.get(10).setSnowDepthMaxDate(importModel.getValue10());
        dataDOMap.get(11).setSnowDepthMaxDate(importModel.getValue11());
        dataDOMap.get(12).setSnowDepthMaxDate(importModel.getValue12());
        dataDOMap.get(13).setSnowDepthMaxDate(importModel.getValue13());
    }

    /**
     * //积雪深度-极端最高-极值
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateSnowDepthMaxExtremum(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 37) {
            return;
        }
        dataDOMap.get(1).setSnowDepthMaxExtremum(importModel.getValue1());
        dataDOMap.get(2).setSnowDepthMaxExtremum(importModel.getValue2());
        dataDOMap.get(3).setSnowDepthMaxExtremum(importModel.getValue3());
        dataDOMap.get(4).setSnowDepthMaxExtremum(importModel.getValue4());
        dataDOMap.get(5).setSnowDepthMaxExtremum(importModel.getValue5());
        dataDOMap.get(6).setSnowDepthMaxExtremum(importModel.getValue6());
        dataDOMap.get(7).setSnowDepthMaxExtremum(importModel.getValue7());
        dataDOMap.get(8).setSnowDepthMaxExtremum(importModel.getValue8());
        dataDOMap.get(9).setSnowDepthMaxExtremum(importModel.getValue9());
        dataDOMap.get(10).setSnowDepthMaxExtremum(importModel.getValue10());
        dataDOMap.get(11).setSnowDepthMaxExtremum(importModel.getValue11());
        dataDOMap.get(12).setSnowDepthMaxExtremum(importModel.getValue12());
        dataDOMap.get(13).setSnowDepthMaxExtremum(importModel.getValue13());
    }

    /**
     * //湿度-日最小湿度
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateHumidityDayMin(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 36) {
            return;
        }
        dataDOMap.get(1).setHumidityDayMin(importModel.getValue1());
        dataDOMap.get(2).setHumidityDayMin(importModel.getValue2());
        dataDOMap.get(3).setHumidityDayMin(importModel.getValue3());
        dataDOMap.get(4).setHumidityDayMin(importModel.getValue4());
        dataDOMap.get(5).setHumidityDayMin(importModel.getValue5());
        dataDOMap.get(6).setHumidityDayMin(importModel.getValue6());
        dataDOMap.get(7).setHumidityDayMin(importModel.getValue7());
        dataDOMap.get(8).setHumidityDayMin(importModel.getValue8());
        dataDOMap.get(9).setHumidityDayMin(importModel.getValue9());
        dataDOMap.get(10).setHumidityDayMin(importModel.getValue10());
        dataDOMap.get(11).setHumidityDayMin(importModel.getValue11());
        dataDOMap.get(12).setHumidityDayMin(importModel.getValue12());
        dataDOMap.get(13).setHumidityDayMin(importModel.getValue13());
    }

    /**
     * //湿度-平均相对湿度
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateHumidityAverageValue(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 35) {
            return;
        }
        dataDOMap.get(1).setHumidityAverageValue(importModel.getValue1());
        dataDOMap.get(2).setHumidityAverageValue(importModel.getValue2());
        dataDOMap.get(3).setHumidityAverageValue(importModel.getValue3());
        dataDOMap.get(4).setHumidityAverageValue(importModel.getValue4());
        dataDOMap.get(5).setHumidityAverageValue(importModel.getValue5());
        dataDOMap.get(6).setHumidityAverageValue(importModel.getValue6());
        dataDOMap.get(7).setHumidityAverageValue(importModel.getValue7());
        dataDOMap.get(8).setHumidityAverageValue(importModel.getValue8());
        dataDOMap.get(9).setHumidityAverageValue(importModel.getValue9());
        dataDOMap.get(10).setHumidityAverageValue(importModel.getValue10());
        dataDOMap.get(11).setHumidityAverageValue(importModel.getValue11());
        dataDOMap.get(12).setHumidityAverageValue(importModel.getValue12());
        dataDOMap.get(13).setHumidityAverageValue(importModel.getValue13());
    }

    /**
     * //地温-极端最低-年份
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateGroundTemMinYear(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 34) {
            return;
        }
        dataDOMap.get(1).setGroundTemMinYear(importModel.getValue1());
        dataDOMap.get(2).setGroundTemMinYear(importModel.getValue2());
        dataDOMap.get(3).setGroundTemMinYear(importModel.getValue3());
        dataDOMap.get(4).setGroundTemMinYear(importModel.getValue4());
        dataDOMap.get(5).setGroundTemMinYear(importModel.getValue5());
        dataDOMap.get(6).setGroundTemMinYear(importModel.getValue6());
        dataDOMap.get(7).setGroundTemMinYear(importModel.getValue7());
        dataDOMap.get(8).setGroundTemMinYear(importModel.getValue8());
        dataDOMap.get(9).setGroundTemMinYear(importModel.getValue9());
        dataDOMap.get(10).setGroundTemMinYear(importModel.getValue10());
        dataDOMap.get(11).setGroundTemMinYear(importModel.getValue11());
        dataDOMap.get(12).setGroundTemMinYear(importModel.getValue12());
        dataDOMap.get(13).setGroundTemMinYear(importModel.getValue13());
    }

    /**
     * //地温-极端最低-日期
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateGroundTemMinDate(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 33) {
            return;
        }
        dataDOMap.get(1).setGroundTemMinDate(importModel.getValue1());
        dataDOMap.get(2).setGroundTemMinDate(importModel.getValue2());
        dataDOMap.get(3).setGroundTemMinDate(importModel.getValue3());
        dataDOMap.get(4).setGroundTemMinDate(importModel.getValue4());
        dataDOMap.get(5).setGroundTemMinDate(importModel.getValue5());
        dataDOMap.get(6).setGroundTemMinDate(importModel.getValue6());
        dataDOMap.get(7).setGroundTemMinDate(importModel.getValue7());
        dataDOMap.get(8).setGroundTemMinDate(importModel.getValue8());
        dataDOMap.get(9).setGroundTemMinDate(importModel.getValue9());
        dataDOMap.get(10).setGroundTemMinDate(importModel.getValue10());
        dataDOMap.get(11).setGroundTemMinDate(importModel.getValue11());
        dataDOMap.get(12).setGroundTemMinDate(importModel.getValue12());
        dataDOMap.get(13).setGroundTemMinDate(importModel.getValue13());
    }

    /**
     * //地温-极端最低-极值
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateGroundTemMinExtremum(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 32) {
            return;
        }
        dataDOMap.get(1).setGroundTemMinExtremum(importModel.getValue1());
        dataDOMap.get(2).setGroundTemMinExtremum(importModel.getValue2());
        dataDOMap.get(3).setGroundTemMinExtremum(importModel.getValue3());
        dataDOMap.get(4).setGroundTemMinExtremum(importModel.getValue4());
        dataDOMap.get(5).setGroundTemMinExtremum(importModel.getValue5());
        dataDOMap.get(6).setGroundTemMinExtremum(importModel.getValue6());
        dataDOMap.get(7).setGroundTemMinExtremum(importModel.getValue7());
        dataDOMap.get(8).setGroundTemMinExtremum(importModel.getValue8());
        dataDOMap.get(9).setGroundTemMinExtremum(importModel.getValue9());
        dataDOMap.get(10).setGroundTemMinExtremum(importModel.getValue10());
        dataDOMap.get(11).setGroundTemMinExtremum(importModel.getValue11());
        dataDOMap.get(12).setGroundTemMinExtremum(importModel.getValue12());
        dataDOMap.get(13).setGroundTemMinExtremum(importModel.getValue13());
    }

    /**
     * //地温-极端最高-年份
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateGroundTemMaxYear(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 31) {
            return;
        }
        dataDOMap.get(1).setGroundTemMaxYear(importModel.getValue1());
        dataDOMap.get(2).setGroundTemMaxYear(importModel.getValue2());
        dataDOMap.get(3).setGroundTemMaxYear(importModel.getValue3());
        dataDOMap.get(4).setGroundTemMaxYear(importModel.getValue4());
        dataDOMap.get(5).setGroundTemMaxYear(importModel.getValue5());
        dataDOMap.get(6).setGroundTemMaxYear(importModel.getValue6());
        dataDOMap.get(7).setGroundTemMaxYear(importModel.getValue7());
        dataDOMap.get(8).setGroundTemMaxYear(importModel.getValue8());
        dataDOMap.get(9).setGroundTemMaxYear(importModel.getValue9());
        dataDOMap.get(10).setGroundTemMaxYear(importModel.getValue10());
        dataDOMap.get(11).setGroundTemMaxYear(importModel.getValue11());
        dataDOMap.get(12).setGroundTemMaxYear(importModel.getValue12());
        dataDOMap.get(13).setGroundTemMaxYear(importModel.getValue13());
    }

    /**
     * //地温-极端最高-日期
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateGroundTemMaxDate(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 30) {
            return;
        }
        dataDOMap.get(1).setGroundTemMaxDate(importModel.getValue1());
        dataDOMap.get(2).setGroundTemMaxDate(importModel.getValue2());
        dataDOMap.get(3).setGroundTemMaxDate(importModel.getValue3());
        dataDOMap.get(4).setGroundTemMaxDate(importModel.getValue4());
        dataDOMap.get(5).setGroundTemMaxDate(importModel.getValue5());
        dataDOMap.get(6).setGroundTemMaxDate(importModel.getValue6());
        dataDOMap.get(7).setGroundTemMaxDate(importModel.getValue7());
        dataDOMap.get(8).setGroundTemMaxDate(importModel.getValue8());
        dataDOMap.get(9).setGroundTemMaxDate(importModel.getValue9());
        dataDOMap.get(10).setGroundTemMaxDate(importModel.getValue10());
        dataDOMap.get(11).setGroundTemMaxDate(importModel.getValue11());
        dataDOMap.get(12).setGroundTemMaxDate(importModel.getValue12());
        dataDOMap.get(13).setGroundTemMaxDate(importModel.getValue13());
    }

    /**
     * //地温-极端最高-极值
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateGroundTemMaxExtremum(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 29) {
            return;
        }
        dataDOMap.get(1).setGroundTemMaxExtremum(importModel.getValue1());
        dataDOMap.get(2).setGroundTemMaxExtremum(importModel.getValue2());
        dataDOMap.get(3).setGroundTemMaxExtremum(importModel.getValue3());
        dataDOMap.get(4).setGroundTemMaxExtremum(importModel.getValue4());
        dataDOMap.get(5).setGroundTemMaxExtremum(importModel.getValue5());
        dataDOMap.get(6).setGroundTemMaxExtremum(importModel.getValue6());
        dataDOMap.get(7).setGroundTemMaxExtremum(importModel.getValue7());
        dataDOMap.get(8).setGroundTemMaxExtremum(importModel.getValue8());
        dataDOMap.get(9).setGroundTemMaxExtremum(importModel.getValue9());
        dataDOMap.get(10).setGroundTemMaxExtremum(importModel.getValue10());
        dataDOMap.get(11).setGroundTemMaxExtremum(importModel.getValue11());
        dataDOMap.get(12).setGroundTemMaxExtremum(importModel.getValue12());
        dataDOMap.get(13).setGroundTemMaxExtremum(importModel.getValue13());
    }

    /**
     * //地温-平均地温
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateGroundTemAverageValue(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 28) {
            return;
        }
        dataDOMap.get(1).setGroundTemAverageValue(importModel.getValue1());
        dataDOMap.get(2).setGroundTemAverageValue(importModel.getValue2());
        dataDOMap.get(3).setGroundTemAverageValue(importModel.getValue3());
        dataDOMap.get(4).setGroundTemAverageValue(importModel.getValue4());
        dataDOMap.get(5).setGroundTemAverageValue(importModel.getValue5());
        dataDOMap.get(6).setGroundTemAverageValue(importModel.getValue6());
        dataDOMap.get(7).setGroundTemAverageValue(importModel.getValue7());
        dataDOMap.get(8).setGroundTemAverageValue(importModel.getValue8());
        dataDOMap.get(9).setGroundTemAverageValue(importModel.getValue9());
        dataDOMap.get(10).setGroundTemAverageValue(importModel.getValue10());
        dataDOMap.get(11).setGroundTemAverageValue(importModel.getValue11());
        dataDOMap.get(12).setGroundTemAverageValue(importModel.getValue12());
        dataDOMap.get(13).setGroundTemAverageValue(importModel.getValue13());
    }

    /**
     * //风速-风速日数->=15m/s
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindDays15(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 27) {
            return;
        }
        dataDOMap.get(1).setWindDays15(importModel.getValue1());
        dataDOMap.get(2).setWindDays15(importModel.getValue2());
        dataDOMap.get(3).setWindDays15(importModel.getValue3());
        dataDOMap.get(4).setWindDays15(importModel.getValue4());
        dataDOMap.get(5).setWindDays15(importModel.getValue5());
        dataDOMap.get(6).setWindDays15(importModel.getValue6());
        dataDOMap.get(7).setWindDays15(importModel.getValue7());
        dataDOMap.get(8).setWindDays15(importModel.getValue8());
        dataDOMap.get(9).setWindDays15(importModel.getValue9());
        dataDOMap.get(10).setWindDays15(importModel.getValue10());
        dataDOMap.get(11).setWindDays15(importModel.getValue11());
        dataDOMap.get(12).setWindDays15(importModel.getValue12());
        dataDOMap.get(13).setWindDays15(importModel.getValue13());
    }

    /**
     * //风速-风速日数->=12m/s
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindDays12(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 26) {
            return;
        }
        dataDOMap.get(1).setWindDays12(importModel.getValue1());
        dataDOMap.get(2).setWindDays12(importModel.getValue2());
        dataDOMap.get(3).setWindDays12(importModel.getValue3());
        dataDOMap.get(4).setWindDays12(importModel.getValue4());
        dataDOMap.get(5).setWindDays12(importModel.getValue5());
        dataDOMap.get(6).setWindDays12(importModel.getValue6());
        dataDOMap.get(7).setWindDays12(importModel.getValue7());
        dataDOMap.get(8).setWindDays12(importModel.getValue8());
        dataDOMap.get(9).setWindDays12(importModel.getValue9());
        dataDOMap.get(10).setWindDays12(importModel.getValue10());
        dataDOMap.get(11).setWindDays12(importModel.getValue11());
        dataDOMap.get(12).setWindDays12(importModel.getValue12());
        dataDOMap.get(13).setWindDays12(importModel.getValue13());
    }

    /**
     * //风速-风速日数->=10m/s
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindDays10(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 25) {
            return;
        }
        dataDOMap.get(1).setWindDays10(importModel.getValue1());
        dataDOMap.get(2).setWindDays10(importModel.getValue2());
        dataDOMap.get(3).setWindDays10(importModel.getValue3());
        dataDOMap.get(4).setWindDays10(importModel.getValue4());
        dataDOMap.get(5).setWindDays10(importModel.getValue5());
        dataDOMap.get(6).setWindDays10(importModel.getValue6());
        dataDOMap.get(7).setWindDays10(importModel.getValue7());
        dataDOMap.get(8).setWindDays10(importModel.getValue8());
        dataDOMap.get(9).setWindDays10(importModel.getValue9());
        dataDOMap.get(10).setWindDays10(importModel.getValue10());
        dataDOMap.get(11).setWindDays10(importModel.getValue11());
        dataDOMap.get(12).setWindDays10(importModel.getValue12());
        dataDOMap.get(13).setWindDays10(importModel.getValue13());
    }

    /**
     * //风速-风速日数->=5m/s
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindDays5(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 24) {
            return;
        }
        dataDOMap.get(1).setWindDays5(importModel.getValue1());
        dataDOMap.get(2).setWindDays5(importModel.getValue2());
        dataDOMap.get(3).setWindDays5(importModel.getValue3());
        dataDOMap.get(4).setWindDays5(importModel.getValue4());
        dataDOMap.get(5).setWindDays5(importModel.getValue5());
        dataDOMap.get(6).setWindDays5(importModel.getValue6());
        dataDOMap.get(7).setWindDays5(importModel.getValue7());
        dataDOMap.get(8).setWindDays5(importModel.getValue8());
        dataDOMap.get(9).setWindDays5(importModel.getValue9());
        dataDOMap.get(10).setWindDays5(importModel.getValue10());
        dataDOMap.get(11).setWindDays5(importModel.getValue11());
        dataDOMap.get(12).setWindDays5(importModel.getValue12());
        dataDOMap.get(13).setWindDays5(importModel.getValue13());
    }

    /**
     * //风速-最大风速-风向
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindMaxDirection(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 23) {
            return;
        }
        dataDOMap.get(1).setWindMaxDirection(importModel.getValue1());
        dataDOMap.get(2).setWindMaxDirection(importModel.getValue2());
        dataDOMap.get(3).setWindMaxDirection(importModel.getValue3());
        dataDOMap.get(4).setWindMaxDirection(importModel.getValue4());
        dataDOMap.get(5).setWindMaxDirection(importModel.getValue5());
        dataDOMap.get(6).setWindMaxDirection(importModel.getValue6());
        dataDOMap.get(7).setWindMaxDirection(importModel.getValue7());
        dataDOMap.get(8).setWindMaxDirection(importModel.getValue8());
        dataDOMap.get(9).setWindMaxDirection(importModel.getValue9());
        dataDOMap.get(10).setWindMaxDirection(importModel.getValue10());
        dataDOMap.get(11).setWindMaxDirection(importModel.getValue11());
        dataDOMap.get(12).setWindMaxDirection(importModel.getValue12());
        dataDOMap.get(13).setWindMaxDirection(importModel.getValue13());
    }

    /**
     * //风速-最大风速-年份
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindMaxYear(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 22) {
            return;
        }
        dataDOMap.get(1).setWindMaxYear(importModel.getValue1());
        dataDOMap.get(2).setWindMaxYear(importModel.getValue2());
        dataDOMap.get(3).setWindMaxYear(importModel.getValue3());
        dataDOMap.get(4).setWindMaxYear(importModel.getValue4());
        dataDOMap.get(5).setWindMaxYear(importModel.getValue5());
        dataDOMap.get(6).setWindMaxYear(importModel.getValue6());
        dataDOMap.get(7).setWindMaxYear(importModel.getValue7());
        dataDOMap.get(8).setWindMaxYear(importModel.getValue8());
        dataDOMap.get(9).setWindMaxYear(importModel.getValue9());
        dataDOMap.get(10).setWindMaxYear(importModel.getValue10());
        dataDOMap.get(11).setWindMaxYear(importModel.getValue11());
        dataDOMap.get(12).setWindMaxYear(importModel.getValue12());
        dataDOMap.get(13).setWindMaxYear(importModel.getValue13());
    }

    /**
     * //风速-最大风速-日期
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindMaxDate(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 21) {
            return;
        }
        dataDOMap.get(1).setWindMaxDate(importModel.getValue1());
        dataDOMap.get(2).setWindMaxDate(importModel.getValue2());
        dataDOMap.get(3).setWindMaxDate(importModel.getValue3());
        dataDOMap.get(4).setWindMaxDate(importModel.getValue4());
        dataDOMap.get(5).setWindMaxDate(importModel.getValue5());
        dataDOMap.get(6).setWindMaxDate(importModel.getValue6());
        dataDOMap.get(7).setWindMaxDate(importModel.getValue7());
        dataDOMap.get(8).setWindMaxDate(importModel.getValue8());
        dataDOMap.get(9).setWindMaxDate(importModel.getValue9());
        dataDOMap.get(10).setWindMaxDate(importModel.getValue10());
        dataDOMap.get(11).setWindMaxDate(importModel.getValue11());
        dataDOMap.get(12).setWindMaxDate(importModel.getValue12());
        dataDOMap.get(13).setWindMaxDate(importModel.getValue13());
    }

    /**
     * //风速-最大风速-极值
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindMaxExtremum(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 20) {
            return;
        }
        dataDOMap.get(1).setWindMaxExtremum(importModel.getValue1());
        dataDOMap.get(2).setWindMaxExtremum(importModel.getValue2());
        dataDOMap.get(3).setWindMaxExtremum(importModel.getValue3());
        dataDOMap.get(4).setWindMaxExtremum(importModel.getValue4());
        dataDOMap.get(5).setWindMaxExtremum(importModel.getValue5());
        dataDOMap.get(6).setWindMaxExtremum(importModel.getValue6());
        dataDOMap.get(7).setWindMaxExtremum(importModel.getValue7());
        dataDOMap.get(8).setWindMaxExtremum(importModel.getValue8());
        dataDOMap.get(9).setWindMaxExtremum(importModel.getValue9());
        dataDOMap.get(10).setWindMaxExtremum(importModel.getValue10());
        dataDOMap.get(11).setWindMaxExtremum(importModel.getValue11());
        dataDOMap.get(12).setWindMaxExtremum(importModel.getValue12());
        dataDOMap.get(13).setWindMaxExtremum(importModel.getValue13());
    }

    /**
     * //风速-平均风速
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindAverageValue(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 19) {
            return;
        }
        dataDOMap.get(1).setWindAverageValue(importModel.getValue1());
        dataDOMap.get(2).setWindAverageValue(importModel.getValue2());
        dataDOMap.get(3).setWindAverageValue(importModel.getValue3());
        dataDOMap.get(4).setWindAverageValue(importModel.getValue4());
        dataDOMap.get(5).setWindAverageValue(importModel.getValue5());
        dataDOMap.get(6).setWindAverageValue(importModel.getValue6());
        dataDOMap.get(7).setWindAverageValue(importModel.getValue7());
        dataDOMap.get(8).setWindAverageValue(importModel.getValue8());
        dataDOMap.get(9).setWindAverageValue(importModel.getValue9());
        dataDOMap.get(10).setWindAverageValue(importModel.getValue10());
        dataDOMap.get(11).setWindAverageValue(importModel.getValue11());
        dataDOMap.get(12).setWindAverageValue(importModel.getValue12());
        dataDOMap.get(13).setWindAverageValue(importModel.getValue13());
    }

    /**
     * //风速-主导风向-风向频率
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindLeadDirectionFrequency(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 18) {
            return;
        }
        dataDOMap.get(1).setWindLeadDirectionFrequency(importModel.getValue1());
        dataDOMap.get(2).setWindLeadDirectionFrequency(importModel.getValue2());
        dataDOMap.get(3).setWindLeadDirectionFrequency(importModel.getValue3());
        dataDOMap.get(4).setWindLeadDirectionFrequency(importModel.getValue4());
        dataDOMap.get(5).setWindLeadDirectionFrequency(importModel.getValue5());
        dataDOMap.get(6).setWindLeadDirectionFrequency(importModel.getValue6());
        dataDOMap.get(7).setWindLeadDirectionFrequency(importModel.getValue7());
        dataDOMap.get(8).setWindLeadDirectionFrequency(importModel.getValue8());
        dataDOMap.get(9).setWindLeadDirectionFrequency(importModel.getValue9());
        dataDOMap.get(10).setWindLeadDirectionFrequency(importModel.getValue10());
        dataDOMap.get(11).setWindLeadDirectionFrequency(importModel.getValue11());
        dataDOMap.get(12).setWindLeadDirectionFrequency(importModel.getValue12());
        dataDOMap.get(13).setWindLeadDirectionFrequency(importModel.getValue13());
    }

    /**
     * //风速-主导风向-风向
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateWindLeadDirection(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 17) {
            return;
        }
        dataDOMap.get(1).setWindLeadDirection(importModel.getValue1());
        dataDOMap.get(2).setWindLeadDirection(importModel.getValue2());
        dataDOMap.get(3).setWindLeadDirection(importModel.getValue3());
        dataDOMap.get(4).setWindLeadDirection(importModel.getValue4());
        dataDOMap.get(5).setWindLeadDirection(importModel.getValue5());
        dataDOMap.get(6).setWindLeadDirection(importModel.getValue6());
        dataDOMap.get(7).setWindLeadDirection(importModel.getValue7());
        dataDOMap.get(8).setWindLeadDirection(importModel.getValue8());
        dataDOMap.get(9).setWindLeadDirection(importModel.getValue9());
        dataDOMap.get(10).setWindLeadDirection(importModel.getValue10());
        dataDOMap.get(11).setWindLeadDirection(importModel.getValue11());
        dataDOMap.get(12).setWindLeadDirection(importModel.getValue12());
        dataDOMap.get(13).setWindLeadDirection(importModel.getValue13());
    }

    /**
     * //降水量-≥50mm日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitation50Days(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 16) {
            return;
        }
        dataDOMap.get(1).setPrecipitation50Days(importModel.getValue1());
        dataDOMap.get(2).setPrecipitation50Days(importModel.getValue2());
        dataDOMap.get(3).setPrecipitation50Days(importModel.getValue3());
        dataDOMap.get(4).setPrecipitation50Days(importModel.getValue4());
        dataDOMap.get(5).setPrecipitation50Days(importModel.getValue5());
        dataDOMap.get(6).setPrecipitation50Days(importModel.getValue6());
        dataDOMap.get(7).setPrecipitation50Days(importModel.getValue7());
        dataDOMap.get(8).setPrecipitation50Days(importModel.getValue8());
        dataDOMap.get(9).setPrecipitation50Days(importModel.getValue9());
        dataDOMap.get(10).setPrecipitation50Days(importModel.getValue10());
        dataDOMap.get(11).setPrecipitation50Days(importModel.getValue11());
        dataDOMap.get(12).setPrecipitation50Days(importModel.getValue12());
        dataDOMap.get(13).setPrecipitation50Days(importModel.getValue13());
    }

    /**
     * //降水量-≥30mm日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitation30Days(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 15) {
            return;
        }
        dataDOMap.get(1).setPrecipitation30Days(importModel.getValue1());
        dataDOMap.get(2).setPrecipitation30Days(importModel.getValue2());
        dataDOMap.get(3).setPrecipitation30Days(importModel.getValue3());
        dataDOMap.get(4).setPrecipitation30Days(importModel.getValue4());
        dataDOMap.get(5).setPrecipitation30Days(importModel.getValue5());
        dataDOMap.get(6).setPrecipitation30Days(importModel.getValue6());
        dataDOMap.get(7).setPrecipitation30Days(importModel.getValue7());
        dataDOMap.get(8).setPrecipitation30Days(importModel.getValue8());
        dataDOMap.get(9).setPrecipitation30Days(importModel.getValue9());
        dataDOMap.get(10).setPrecipitation30Days(importModel.getValue10());
        dataDOMap.get(11).setPrecipitation30Days(importModel.getValue11());
        dataDOMap.get(12).setPrecipitation30Days(importModel.getValue12());
        dataDOMap.get(13).setPrecipitation30Days(importModel.getValue13());
    }

    /**
     * //降水量-≥25mm日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitation25Days(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 14) {
            return;
        }
        dataDOMap.get(1).setPrecipitation25Days(importModel.getValue1());
        dataDOMap.get(2).setPrecipitation25Days(importModel.getValue2());
        dataDOMap.get(3).setPrecipitation25Days(importModel.getValue3());
        dataDOMap.get(4).setPrecipitation25Days(importModel.getValue4());
        dataDOMap.get(5).setPrecipitation25Days(importModel.getValue5());
        dataDOMap.get(6).setPrecipitation25Days(importModel.getValue6());
        dataDOMap.get(7).setPrecipitation25Days(importModel.getValue7());
        dataDOMap.get(8).setPrecipitation25Days(importModel.getValue8());
        dataDOMap.get(9).setPrecipitation25Days(importModel.getValue9());
        dataDOMap.get(10).setPrecipitation25Days(importModel.getValue10());
        dataDOMap.get(11).setPrecipitation25Days(importModel.getValue11());
        dataDOMap.get(12).setPrecipitation25Days(importModel.getValue12());
        dataDOMap.get(13).setPrecipitation25Days(importModel.getValue13());
    }

    /**
     * //降水量-≥10mm日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitation10Days(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 13) {
            return;
        }
        dataDOMap.get(1).setPrecipitation10Days(importModel.getValue1());
        dataDOMap.get(2).setPrecipitation10Days(importModel.getValue2());
        dataDOMap.get(3).setPrecipitation10Days(importModel.getValue3());
        dataDOMap.get(4).setPrecipitation10Days(importModel.getValue4());
        dataDOMap.get(5).setPrecipitation10Days(importModel.getValue5());
        dataDOMap.get(6).setPrecipitation10Days(importModel.getValue6());
        dataDOMap.get(7).setPrecipitation10Days(importModel.getValue7());
        dataDOMap.get(8).setPrecipitation10Days(importModel.getValue8());
        dataDOMap.get(9).setPrecipitation10Days(importModel.getValue9());
        dataDOMap.get(10).setPrecipitation10Days(importModel.getValue10());
        dataDOMap.get(11).setPrecipitation10Days(importModel.getValue11());
        dataDOMap.get(12).setPrecipitation10Days(importModel.getValue12());
        dataDOMap.get(13).setPrecipitation10Days(importModel.getValue13());
    }

    /**
     * //降水量-≥5mm日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitation5Days(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 12) {
            return;
        }
        dataDOMap.get(1).setPrecipitation5Days(importModel.getValue1());
        dataDOMap.get(2).setPrecipitation5Days(importModel.getValue2());
        dataDOMap.get(3).setPrecipitation5Days(importModel.getValue3());
        dataDOMap.get(4).setPrecipitation5Days(importModel.getValue4());
        dataDOMap.get(5).setPrecipitation5Days(importModel.getValue5());
        dataDOMap.get(6).setPrecipitation5Days(importModel.getValue6());
        dataDOMap.get(7).setPrecipitation5Days(importModel.getValue7());
        dataDOMap.get(8).setPrecipitation5Days(importModel.getValue8());
        dataDOMap.get(9).setPrecipitation5Days(importModel.getValue9());
        dataDOMap.get(10).setPrecipitation5Days(importModel.getValue10());
        dataDOMap.get(11).setPrecipitation5Days(importModel.getValue11());
        dataDOMap.get(12).setPrecipitation5Days(importModel.getValue12());
        dataDOMap.get(13).setPrecipitation5Days(importModel.getValue13());
    }

    /**
     * //降水量-≥2mm日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitation2Days(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 11) {
            return;
        }
        dataDOMap.get(1).setPrecipitation2Days(importModel.getValue1());
        dataDOMap.get(2).setPrecipitation2Days(importModel.getValue2());
        dataDOMap.get(3).setPrecipitation2Days(importModel.getValue3());
        dataDOMap.get(4).setPrecipitation2Days(importModel.getValue4());
        dataDOMap.get(5).setPrecipitation2Days(importModel.getValue5());
        dataDOMap.get(6).setPrecipitation2Days(importModel.getValue6());
        dataDOMap.get(7).setPrecipitation2Days(importModel.getValue7());
        dataDOMap.get(8).setPrecipitation2Days(importModel.getValue8());
        dataDOMap.get(9).setPrecipitation2Days(importModel.getValue9());
        dataDOMap.get(10).setPrecipitation2Days(importModel.getValue10());
        dataDOMap.get(11).setPrecipitation2Days(importModel.getValue11());
        dataDOMap.get(12).setPrecipitation2Days(importModel.getValue12());
        dataDOMap.get(13).setPrecipitation2Days(importModel.getValue13());
    }

    /**
     * //降水量-≥0.5mm日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitation05Days(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 10) {
            return;
        }
        dataDOMap.get(1).setPrecipitation05Days(importModel.getValue1());
        dataDOMap.get(2).setPrecipitation05Days(importModel.getValue2());
        dataDOMap.get(3).setPrecipitation05Days(importModel.getValue3());
        dataDOMap.get(4).setPrecipitation05Days(importModel.getValue4());
        dataDOMap.get(5).setPrecipitation05Days(importModel.getValue5());
        dataDOMap.get(6).setPrecipitation05Days(importModel.getValue6());
        dataDOMap.get(7).setPrecipitation05Days(importModel.getValue7());
        dataDOMap.get(8).setPrecipitation05Days(importModel.getValue8());
        dataDOMap.get(9).setPrecipitation05Days(importModel.getValue9());
        dataDOMap.get(10).setPrecipitation05Days(importModel.getValue10());
        dataDOMap.get(11).setPrecipitation05Days(importModel.getValue11());
        dataDOMap.get(12).setPrecipitation05Days(importModel.getValue12());
        dataDOMap.get(13).setPrecipitation05Days(importModel.getValue13());
    }

    /**
     * //降水量-≥0.1mm日数
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitation01Days(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 9) {
            return;
        }
        dataDOMap.get(1).setPrecipitation01Days(importModel.getValue1());
        dataDOMap.get(2).setPrecipitation01Days(importModel.getValue2());
        dataDOMap.get(3).setPrecipitation01Days(importModel.getValue3());
        dataDOMap.get(4).setPrecipitation01Days(importModel.getValue4());
        dataDOMap.get(5).setPrecipitation01Days(importModel.getValue5());
        dataDOMap.get(6).setPrecipitation01Days(importModel.getValue6());
        dataDOMap.get(7).setPrecipitation01Days(importModel.getValue7());
        dataDOMap.get(8).setPrecipitation01Days(importModel.getValue8());
        dataDOMap.get(9).setPrecipitation01Days(importModel.getValue9());
        dataDOMap.get(10).setPrecipitation01Days(importModel.getValue10());
        dataDOMap.get(11).setPrecipitation01Days(importModel.getValue11());
        dataDOMap.get(12).setPrecipitation01Days(importModel.getValue12());
        dataDOMap.get(13).setPrecipitation01Days(importModel.getValue13());
    }

    /**
     * //降水量-日最大降水量
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitationMaxValue(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 8) {
            return;
        }
        dataDOMap.get(1).setPrecipitationMaxValue(importModel.getValue1());
        dataDOMap.get(2).setPrecipitationMaxValue(importModel.getValue2());
        dataDOMap.get(3).setPrecipitationMaxValue(importModel.getValue3());
        dataDOMap.get(4).setPrecipitationMaxValue(importModel.getValue4());
        dataDOMap.get(5).setPrecipitationMaxValue(importModel.getValue5());
        dataDOMap.get(6).setPrecipitationMaxValue(importModel.getValue6());
        dataDOMap.get(7).setPrecipitationMaxValue(importModel.getValue7());
        dataDOMap.get(8).setPrecipitationMaxValue(importModel.getValue8());
        dataDOMap.get(9).setPrecipitationMaxValue(importModel.getValue9());
        dataDOMap.get(10).setPrecipitationMaxValue(importModel.getValue10());
        dataDOMap.get(11).setPrecipitationMaxValue(importModel.getValue11());
        dataDOMap.get(12).setPrecipitationMaxValue(importModel.getValue12());
        dataDOMap.get(13).setPrecipitationMaxValue(importModel.getValue13());
    }

    /**
     * //降水量-平均降水量
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generatePrecipitationAverageValue(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 7) {
            return;
        }
        dataDOMap.get(1).setPrecipitationAverageValue(importModel.getValue1());
        dataDOMap.get(2).setPrecipitationAverageValue(importModel.getValue2());
        dataDOMap.get(3).setPrecipitationAverageValue(importModel.getValue3());
        dataDOMap.get(4).setPrecipitationAverageValue(importModel.getValue4());
        dataDOMap.get(5).setPrecipitationAverageValue(importModel.getValue5());
        dataDOMap.get(6).setPrecipitationAverageValue(importModel.getValue6());
        dataDOMap.get(7).setPrecipitationAverageValue(importModel.getValue7());
        dataDOMap.get(8).setPrecipitationAverageValue(importModel.getValue8());
        dataDOMap.get(9).setPrecipitationAverageValue(importModel.getValue9());
        dataDOMap.get(10).setPrecipitationAverageValue(importModel.getValue10());
        dataDOMap.get(11).setPrecipitationAverageValue(importModel.getValue11());
        dataDOMap.get(12).setPrecipitationAverageValue(importModel.getValue12());
        dataDOMap.get(13).setPrecipitationAverageValue(importModel.getValue13());
    }

    /**
     * //气温-极端最低-年份
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateTemperatureMinYear(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 6) {
            return;
        }
        dataDOMap.get(1).setTemperatureMinYear(importModel.getValue1());
        dataDOMap.get(2).setTemperatureMinYear(importModel.getValue2());
        dataDOMap.get(3).setTemperatureMinYear(importModel.getValue3());
        dataDOMap.get(4).setTemperatureMinYear(importModel.getValue4());
        dataDOMap.get(5).setTemperatureMinYear(importModel.getValue5());
        dataDOMap.get(6).setTemperatureMinYear(importModel.getValue6());
        dataDOMap.get(7).setTemperatureMinYear(importModel.getValue7());
        dataDOMap.get(8).setTemperatureMinYear(importModel.getValue8());
        dataDOMap.get(9).setTemperatureMinYear(importModel.getValue9());
        dataDOMap.get(10).setTemperatureMinYear(importModel.getValue10());
        dataDOMap.get(11).setTemperatureMinYear(importModel.getValue11());
        dataDOMap.get(12).setTemperatureMinYear(importModel.getValue12());
        dataDOMap.get(13).setTemperatureMinYear(importModel.getValue13());
    }

    /**
     * //气温-极端最低-日期
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateTemperatureMinDate(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 5) {
            return;
        }
        dataDOMap.get(1).setTemperatureMinDate(importModel.getValue1());
        dataDOMap.get(2).setTemperatureMinDate(importModel.getValue2());
        dataDOMap.get(3).setTemperatureMinDate(importModel.getValue3());
        dataDOMap.get(4).setTemperatureMinDate(importModel.getValue4());
        dataDOMap.get(5).setTemperatureMinDate(importModel.getValue5());
        dataDOMap.get(6).setTemperatureMinDate(importModel.getValue6());
        dataDOMap.get(7).setTemperatureMinDate(importModel.getValue7());
        dataDOMap.get(8).setTemperatureMinDate(importModel.getValue8());
        dataDOMap.get(9).setTemperatureMinDate(importModel.getValue9());
        dataDOMap.get(10).setTemperatureMinDate(importModel.getValue10());
        dataDOMap.get(11).setTemperatureMinDate(importModel.getValue11());
        dataDOMap.get(12).setTemperatureMinDate(importModel.getValue12());
        dataDOMap.get(13).setTemperatureMinDate(importModel.getValue13());
    }

    /**
     * //气温-极端最低-极值
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateTemperatureMinExtremum(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 4) {
            return;
        }
        dataDOMap.get(1).setTemperatureMinExtremum(importModel.getValue1());
        dataDOMap.get(2).setTemperatureMinExtremum(importModel.getValue2());
        dataDOMap.get(3).setTemperatureMinExtremum(importModel.getValue3());
        dataDOMap.get(4).setTemperatureMinExtremum(importModel.getValue4());
        dataDOMap.get(5).setTemperatureMinExtremum(importModel.getValue5());
        dataDOMap.get(6).setTemperatureMinExtremum(importModel.getValue6());
        dataDOMap.get(7).setTemperatureMinExtremum(importModel.getValue7());
        dataDOMap.get(8).setTemperatureMinExtremum(importModel.getValue8());
        dataDOMap.get(9).setTemperatureMinExtremum(importModel.getValue9());
        dataDOMap.get(10).setTemperatureMinExtremum(importModel.getValue10());
        dataDOMap.get(11).setTemperatureMinExtremum(importModel.getValue11());
        dataDOMap.get(12).setTemperatureMinExtremum(importModel.getValue12());
        dataDOMap.get(13).setTemperatureMinExtremum(importModel.getValue13());
    }

    /**
     * //气温-极端最高-年份
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateTemperatureMaxYear(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 3) {
            return;
        }
        dataDOMap.get(1).setTemperatureMaxYear(importModel.getValue1());
        dataDOMap.get(2).setTemperatureMaxYear(importModel.getValue2());
        dataDOMap.get(3).setTemperatureMaxYear(importModel.getValue3());
        dataDOMap.get(4).setTemperatureMaxYear(importModel.getValue4());
        dataDOMap.get(5).setTemperatureMaxYear(importModel.getValue5());
        dataDOMap.get(6).setTemperatureMaxYear(importModel.getValue6());
        dataDOMap.get(7).setTemperatureMaxYear(importModel.getValue7());
        dataDOMap.get(8).setTemperatureMaxYear(importModel.getValue8());
        dataDOMap.get(9).setTemperatureMaxYear(importModel.getValue9());
        dataDOMap.get(10).setTemperatureMaxYear(importModel.getValue10());
        dataDOMap.get(11).setTemperatureMaxYear(importModel.getValue11());
        dataDOMap.get(12).setTemperatureMaxYear(importModel.getValue12());
        dataDOMap.get(13).setTemperatureMaxYear(importModel.getValue13());
    }

    /**
     * 气温-极端最高-日期
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateTemperatureMaxDate(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 2) {
            return;
        }
        dataDOMap.get(1).setTemperatureMaxDate(importModel.getValue1());
        dataDOMap.get(2).setTemperatureMaxDate(importModel.getValue2());
        dataDOMap.get(3).setTemperatureMaxDate(importModel.getValue3());
        dataDOMap.get(4).setTemperatureMaxDate(importModel.getValue4());
        dataDOMap.get(5).setTemperatureMaxDate(importModel.getValue5());
        dataDOMap.get(6).setTemperatureMaxDate(importModel.getValue6());
        dataDOMap.get(7).setTemperatureMaxDate(importModel.getValue7());
        dataDOMap.get(8).setTemperatureMaxDate(importModel.getValue8());
        dataDOMap.get(9).setTemperatureMaxDate(importModel.getValue9());
        dataDOMap.get(10).setTemperatureMaxDate(importModel.getValue10());
        dataDOMap.get(11).setTemperatureMaxDate(importModel.getValue11());
        dataDOMap.get(12).setTemperatureMaxDate(importModel.getValue12());
        dataDOMap.get(13).setTemperatureMaxDate(importModel.getValue13());
    }

    /**
     * 气温-极端最高—极值
     *
     * @param lineNumber
     * @param dataDOMap
     * @param importModel
     */
    private void generateTemperatureMaxExtremum(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 1) {
            return;
        }
        dataDOMap.get(1).setTemperatureMaxExtremum(importModel.getValue1());
        dataDOMap.get(2).setTemperatureMaxExtremum(importModel.getValue2());
        dataDOMap.get(3).setTemperatureMaxExtremum(importModel.getValue3());
        dataDOMap.get(4).setTemperatureMaxExtremum(importModel.getValue4());
        dataDOMap.get(5).setTemperatureMaxExtremum(importModel.getValue5());
        dataDOMap.get(6).setTemperatureMaxExtremum(importModel.getValue6());
        dataDOMap.get(7).setTemperatureMaxExtremum(importModel.getValue7());
        dataDOMap.get(8).setTemperatureMaxExtremum(importModel.getValue8());
        dataDOMap.get(9).setTemperatureMaxExtremum(importModel.getValue9());
        dataDOMap.get(10).setTemperatureMaxExtremum(importModel.getValue10());
        dataDOMap.get(11).setTemperatureMaxExtremum(importModel.getValue11());
        dataDOMap.get(12).setTemperatureMaxExtremum(importModel.getValue12());
        dataDOMap.get(13).setTemperatureMaxExtremum(importModel.getValue13());
    }

    /**
     * 气温-平均气温
     *
     * @param dataDOMap
     * @param importModel
     */
    private void generateTemperatureAverageValue(int lineNumber, Map<Integer, WeatherDataDO> dataDOMap, WeatherDataImportModel importModel) {
        if (lineNumber != 0) {
            return;
        }
        dataDOMap.get(1).setTemperatureAverageValue(importModel.getValue1());
        dataDOMap.get(2).setTemperatureAverageValue(importModel.getValue2());
        dataDOMap.get(3).setTemperatureAverageValue(importModel.getValue3());
        dataDOMap.get(4).setTemperatureAverageValue(importModel.getValue4());
        dataDOMap.get(5).setTemperatureAverageValue(importModel.getValue5());
        dataDOMap.get(6).setTemperatureAverageValue(importModel.getValue6());
        dataDOMap.get(7).setTemperatureAverageValue(importModel.getValue7());
        dataDOMap.get(8).setTemperatureAverageValue(importModel.getValue8());
        dataDOMap.get(9).setTemperatureAverageValue(importModel.getValue9());
        dataDOMap.get(10).setTemperatureAverageValue(importModel.getValue10());
        dataDOMap.get(11).setTemperatureAverageValue(importModel.getValue11());
        dataDOMap.get(12).setTemperatureAverageValue(importModel.getValue12());
        dataDOMap.get(13).setTemperatureAverageValue(importModel.getValue13());
    }


    @Override
    public WeatherDataDO selectOne(Long stationId, Integer month) {
        QueryWrapper<WeatherDataDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("station_id", stationId);
        queryWrapper.eq("month", month);
        queryWrapper.eq("latest", 1);
        return weatherDataMapper.selectOne(queryWrapper, false);
    }

    @Override
    public List<WeatherDataDO> selectListTemporaryData(Long stationId) {
        return weatherDataMapper.selectListTemporaryData(stationId);
    }

    @Override
    public Integer getLatestVersion(Long stationId) {
        return weatherDataMapper.selectMaxVersion(stationId);
    }

    @Override
    public void batchInsert(List<WeatherDataDO> list, int batchSize) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        int totalSize = list.size();
        for (int start = 0; start < totalSize; start += batchSize) {
            int end = Math.min(start + batchSize, totalSize);
            List<WeatherDataDO> batch = list.subList(start, end);
            weatherDataMapper.batchInsert(batch);
        }
    }

    @Override
    public void updateBatchById(List<WeatherDataDO> updateList, int batchSize) {
        int totalSize = updateList.size();
        for (int start = 0; start < totalSize; start += batchSize) {
            int end = Math.min(start + batchSize, totalSize);
            List<WeatherDataDO> batch = updateList.subList(start, end);
            weatherDataMapper.updateBatchById(batch);
        }
    }

    @Override
    public List<WeatherDataDO> listCurrent(Long stationId) {
        LambdaQueryWrapper<WeatherDataDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WeatherDataDO::getStationId,stationId);
        queryWrapper.eq(WeatherDataDO::getLatest,LatestEnum.LATEST.getType());
        return weatherDataMapper.selectList(queryWrapper);
    }


}