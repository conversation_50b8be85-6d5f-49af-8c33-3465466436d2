package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo.VelocityWindResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo.VelocityWindResultRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo.VelocityWindResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.velocitywindresult.VelocityWindResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.velocitywindresult.VelocityWindResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 气象-历年最大风速及风向")
@RestController
@RequestMapping("/plan/hydrologic/velocity-wind-result")
@Validated
public class VelocityWindResultController {
    @Resource
    private VelocityWindResultService velocityWindResultService;

    @PostMapping("/create")
    @Operation(summary = "创建气象-历年最大风速及风向")
    @PreAuthorize("@ss.hasPermission('hydrologic:velocity-wind-result:create')")
    public CommonResult<Long> createVelocityWindResult(@Valid @RequestBody VelocityWindResultSaveReqVO createReqVO) {
        return success(velocityWindResultService.createVelocityWindResult(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新气象-历年最大风速及风向")
//    @PreAuthorize("@ss.hasPermission('hydrologic:velocity-wind-result:update')")
    public CommonResult<Boolean> updateVelocityWindResult(@Valid @org.springframework.web.bind.annotation.RequestBody VelocityWindResultSaveReqVO updateReqVO) {
        velocityWindResultService.updateVelocityWindResult(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除气象-历年最大风速及风向")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hydrologic:velocity-wind-result:delete')")
    public CommonResult<Boolean> deleteVelocityWindResult(@RequestParam("id") Long id) {
        velocityWindResultService.deleteVelocityWindResult(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得气象-历年最大风速及风向")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('hydrologic:velocity-wind-result:query')")
    public CommonResult<VelocityWindResultRespVO> getVelocityWindResult(@RequestParam("id") Long id) {
        VelocityWindResultDO velocityWindResult = velocityWindResultService.getVelocityWindResult(id);
        return success(BeanUtils.toBean(velocityWindResult, VelocityWindResultRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得气象-历年最大风速及风向分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:velocity-wind-result:query')")
    public CommonResult<PageResult<VelocityWindResultRespVO>> getVelocityWindResultPage(@Valid VelocityWindResultPageReqVO pageReqVO) {
        PageResult<VelocityWindResultDO> pageResult = velocityWindResultService.getVelocityWindResultPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, VelocityWindResultRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出气象-历年最大风速及风向 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:velocity-wind-result:export')")
    public void exportVelocityWindResultExcel(@Valid VelocityWindResultPageReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        velocityWindResultService.exportExcel(pageReqVO,response);
    }
    @PostMapping("/import")
    @Operation(summary = "导入历年最大风速及风向")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:import')")
    public CommonResult<Boolean> importSuspendedSedimentGradingResult(@RequestParam("file") MultipartFile file,
                                                                      @RequestParam("stationId") Long stationId,
                                                                      @RequestParam("stationType") Integer stationType,
                                                                      @RequestParam("dataType") Integer dataType) throws IOException {
        velocityWindResultService.importExcel(file, stationId, stationType, dataType);
        return success(true);
    }

    @PostMapping("/import_check")
    @Operation(summary = "校验导入历年最大风速及风向")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:import')")
    public CommonResult<Boolean> checkImportSuspendedSedimentGradingResult(@RequestParam("file") MultipartFile file,
                                                                      @RequestParam("stationId") Long stationId,
                                                                      @RequestParam("stationType") Integer stationType,
                                                                      @RequestParam("dataType") Integer dataType) throws IOException {
        boolean b = velocityWindResultService.checkImportExcel(file, stationId, stationType, dataType);
        return success(b);
    }
}
