package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.rainfallstation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "管理后台 - 雨量站新增/修改 Request VO")
@Data
public class RainfallStationSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "测站编码")
    private String hydrologicCode;

    @Schema(description = "水系", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请输入水系")
    private String riverSystem;

    @Schema(description = "河名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请输入河名")
    private String riverName;

    @Schema(description = "站名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "请输入站名")
    private String hydrologicName;

    @NotBlank(message = "请选择国")
    @Schema(description = "国", requiredMode = Schema.RequiredMode.REQUIRED)
    private String country;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "县/区")
    private String county;

    @Schema(description = "详细位置")
    private String address;

    @NotBlank(message = "请输入经度")
    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED)
    private String longitude;

    @NotBlank(message = "请输入纬度")
    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED)
    private String latitude;

    @NotBlank(message = "请输入年份")
    @Schema(description = "设立日期-年份", requiredMode = Schema.RequiredMode.REQUIRED)
    private String year;

    @Schema(description = "设立日期-月份")
    private String month;

    @Schema(description = "绝对高程")
    private String absoluteHeight;

    @Schema(description = "地面高度")
    private String groundHeight;

    @Schema(description = "型式")
    private String type;

    @Schema(description = "资料项目")
    private String dataProject;

    @Schema(description = "领导机关")
    private String leadershipOrganization;

    @Schema(description = "年限")
    private Integer dataAge;

    @Schema(description = "开始年份")
    private String dataStartYear;

    @Schema(description = "结束年份")
    private String dataEndYear;

}