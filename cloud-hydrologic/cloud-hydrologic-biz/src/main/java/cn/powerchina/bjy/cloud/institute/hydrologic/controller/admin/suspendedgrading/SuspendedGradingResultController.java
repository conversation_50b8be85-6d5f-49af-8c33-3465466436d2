package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedgrading;

import cn.hutool.extra.spring.SpringUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.bedsedimentgradingresult.vo.BedSedimentGradingResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedgrading.vo.SuspendedGradingResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedgrading.vo.SuspendedGradingResultRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedgrading.vo.SuspendedGradingResultSave;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedgrading.vo.SuspendedGradingResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.suspendedgradingresult.SuspendedGradingResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnumV2;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationalmanac.StationAlmanacService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.suspendedgradingresult.SuspendedGradingResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 月年平均悬移质颗粒级配成果")
@RestController
@RequestMapping("/plan/hydrologic/suspended-grading-result")
@Validated
public class SuspendedGradingResultController {

    @Resource
    private SuspendedGradingResultService suspendedGradingResultService;

    @PostMapping("/create")
    @Operation(summary = "创建月年平均悬移质颗粒级配成果")
    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-grading-result:create')")
    public CommonResult<Long> createSuspendedGradingResult(@Valid @RequestBody SuspendedGradingResultSaveReqVO createReqVO) {
        return success(suspendedGradingResultService.createSuspendedGradingResult(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新月年平均悬移质颗粒级配成果")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-grading-result:update')")
    public CommonResult<Boolean> updateSuspendedGradingResult(@Valid @RequestBody SuspendedGradingResultSave updateReqVO) {
        suspendedGradingResultService.updateSuspendedGradingResult(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除月年平均悬移质颗粒级配成果")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-grading-result:delete')")
    public CommonResult<Boolean> deleteSuspendedGradingResult(@RequestParam("id") Long id) {
        suspendedGradingResultService.deleteSuspendedGradingResult(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得月年平均悬移质颗粒级配成果")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-grading-result:query')")
    public CommonResult<SuspendedGradingResultRespVO> getSuspendedGradingResult(@RequestParam("id") Long id) {
        SuspendedGradingResultDO suspendedGradingResult = suspendedGradingResultService.getSuspendedGradingResult(id);
        return success(BeanUtils.toBean(suspendedGradingResult, SuspendedGradingResultRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得月年平均悬移质颗粒级配成果分页")
    @HydrologicOperation
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-grading-result:query')")
    public CommonResult<PageResult<SuspendedGradingResultRespVO>> getSuspendedGradingResultPage(@Valid SuspendedGradingResultPageReqVO pageReqVO) {
        PageResult<SuspendedGradingResultDO> pageResult = suspendedGradingResultService.getSuspendedGradingResultPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SuspendedGradingResultRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出月年平均悬移质颗粒级配成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-grading-result:export')")
    public void exportSuspendedGradingResultExcel(@Valid SuspendedGradingResultPageReqVO pageReqVO,
                                                  HttpServletResponse response) throws IOException {
        //检索和记录 两个页面使用
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        suspendedGradingResultService.exportExcel(response, pageReqVO);
    }

    @PostMapping("/import")
    @Operation(summary = "导入月年平均悬移质颗粒级配成果")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:import')")
    public CommonResult<Boolean> importSuspendedGradingResultExcel(@RequestParam("file") MultipartFile file,
                                                                      @RequestParam("stationId") Long stationId,
                                                                      @RequestParam("stationType") Integer stationType,
                                                                      @RequestParam("dataType") Integer dataType) throws IOException {
        suspendedGradingResultService.importExcel(file, stationId, stationType, dataType);
        return success(true);
    }
}