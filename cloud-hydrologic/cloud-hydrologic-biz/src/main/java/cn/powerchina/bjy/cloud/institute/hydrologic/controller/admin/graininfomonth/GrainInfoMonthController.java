package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.graininfomonth;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.graininfomonth.vo.GrainInfoMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.graininfomonth.vo.GrainInfoMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.graininfomonth.vo.GrainInfoMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.graininfomonth.GrainInfoMonthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 水文-月平均悬移质颗粒级配")
@RestController
@RequestMapping("/plan/hydrologic/grain/info/month")
@Validated
public class GrainInfoMonthController {

    @Resource
    private GrainInfoMonthService grainInfoMonthService;

    @PutMapping("/update")
    @Operation(summary = "更新月平均悬移质颗粒级配")
//    @PreAuthorize("@ss.hasPermission('hydrologic:grain-info:update')")
    public CommonResult<Boolean> updateGrainInfo(@Valid @RequestBody GrainInfoMonthSaveReqVO updateReqVO) {
        grainInfoMonthService.updateGrainInfo(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得月平均悬移质颗粒级配分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:grain-info:query')")
    public CommonResult<PageResult<GrainInfoMonthRespVO>> getGrainInfoPage(@Valid GrainInfoMonthPageReqVO pageReqVO) {
        PageResult<GrainInfoMonthRespVO> pageResult = grainInfoMonthService.getGrainInfoPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-月平均悬移质颗粒级配分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_GRAIN_INFO_MONTH)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<GrainInfoMonthRespVO>> getGrainInfoDataPage(@Valid GrainInfoMonthPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        PageResult<GrainInfoMonthRespVO> pageResult = grainInfoMonthService.getGrainInfoPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出月平均悬移质颗粒级配 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportGrainInfoExcel(@Valid GrainInfoMonthPageReqVO pageReqVO,
                                     HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        grainInfoMonthService.exportGrainInfoExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出月平均悬移质颗粒级配 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportGrainInfoDataExcel(@Valid GrainInfoMonthPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        grainInfoMonthService.exportGrainInfoExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入月平均悬移质颗粒级配 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:grain-info:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        grainInfoMonthService.importData(file, stationId);
        return success(true);
    }

}