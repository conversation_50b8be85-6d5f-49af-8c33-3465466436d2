package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentyear.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 水文站-输沙率-年含沙量特征值新增/修改 Request VO")
@Data
public class SandContentYearSaveReqVO {

    @Schema(description = "站点id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11254")
    @NotNull(message = "站点id不能为空")
    private Long stationId;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<Item> dataList;
    @Schema(description = "管理后台 - 降水-日降水量数据 Request VO")
    @Data
    public static class Item {
        @Schema(description = "主键，修改时必传",example = "25821")
        private Long id;

        @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8891")
        @NotNull(message = "水文站id不能为空")
        private Long stationId;

        @Schema(description = "年", requiredMode = Schema.RequiredMode.REQUIRED)
        private String year;

        @Schema(description = "最大断面平均含沙量(kg/m3)")
        private String maxSectionAverage;

        @Schema(description = "最大断面平均含沙量出现日期")
        private String maxSectionAverageDate;

        @Schema(description = "最小断面平均含沙量(kg/m3)")
        private String minSectionAverage;

        @Schema(description = "最小断面平均含沙量出现日期")
        private String minSectionAverageDate;

        @Schema(description = "年平均含沙量(kg/m3)")
        private String averageSandContent;

        @Schema(description = "年平均流量(m3/s)")
        private String averageFlow;

        @Schema(description = "年平均输沙率(kg/s)")
        private String averageDischargeRate;

        @Schema(description = "备注", example = "你猜")
        private String remark;

        @Schema(description = "版本（根据原型待定）")
        private Integer version;

        @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
        private Integer latest;
    }


}