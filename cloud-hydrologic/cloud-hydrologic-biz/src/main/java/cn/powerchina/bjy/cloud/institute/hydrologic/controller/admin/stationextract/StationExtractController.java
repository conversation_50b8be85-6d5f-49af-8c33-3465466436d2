package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationextract;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationday.vo.StationDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationextract.vo.StationExtractPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationextract.vo.StationExtractRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationextract.vo.StationExtractSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationday.StationDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationextract.StationExtractDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationextract.StationExtractService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 站点-摘录")
@RestController
@RequestMapping("/plan/hydrologic/station-extract")
@Validated
public class StationExtractController {

    @Resource
    private StationExtractService stationExtractService;

    @PostMapping("/create")
    @Operation(summary = "创建站点-摘录")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-extract:create')")
    public CommonResult<Long> createStationExtract(@Valid @RequestBody StationExtractSaveReqVO createReqVO) {
        return success(stationExtractService.createStationExtract(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新站点-摘录")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-extract:update')")
    public CommonResult<Boolean> updateStationExtract(@Valid @RequestBody StationExtractSaveReqVO updateReqVO) {
        stationExtractService.updateStationExtract(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除站点-摘录")
    @Parameter(name = "id", description = "编号", required = true)
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-extract:delete')")
    public CommonResult<Boolean> deleteStationExtract(@RequestParam("id") Long id) {
        stationExtractService.deleteStationExtract(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得站点-摘录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-extract:query')")
    public CommonResult<StationExtractRespVO> getStationExtract(@RequestParam("id") Long id) {
        StationExtractDO stationExtract = stationExtractService.getStationExtract(id);
        return success(BeanUtils.toBean(stationExtract, StationExtractRespVO.class));
    }

//    @GetMapping("/page")
//    @Operation(summary = "获得站点-摘录分页")
//   // @PreAuthorize("@ss.hasPermission('hydrologic:station-extract:query')")
//    public CommonResult<PageResult<StationExtractRespVO>> getStationExtractPage(@Valid StationExtractPageReqVO pageReqVO) {
//        PageResult<StationExtractDO> pageResult = stationExtractService.getStationExtractPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, StationExtractRespVO.class));
//    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出站点-摘录 Excel")
   // @PreAuthorize("@ss.hasPermission('hydrologic:station-extract:export')")
//    @OperateLog(type = EXPORT)
    public void exportStationExtractExcel(@Valid StationExtractPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setCreateTime(null);
        stationExtractService.exportStationExtractExcel(pageReqVO, response);
    }

    @PostMapping("/import")
	@ExcelImportCheck
    @Operation(summary = "导入-站点数据-摘录")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "stationType", description = "站点类型", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> importRainStationRainExtractFeatureExcel(@RequestParam("file") MultipartFile file,
                                                                      @RequestParam("stationId") Long stationId,
                                                                      @RequestParam("stationType") Integer stationType,
                                                                      @RequestParam("dataType") Integer  dataType
    ) {
        stationExtractService.importStationExtractExcel(file, stationId, stationType, dataType);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "分页查看-站点数据-摘录")
    @HydrologicOperation
    public CommonResult<PageResult<StationExtractDO>> getStationRainExtractPage(@Valid StationExtractPageReqVO pageReqVO) {
        pageReqVO.setCreateTime(null);
        PageResult<StationExtractDO> pageResult = stationExtractService.getStationExtractPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationExtractDO.class));
    }
    @GetMapping("/page/search")
    @Operation(summary = "数据检索-获得冰情摘录分页")
    @HydrologicOperation
    public CommonResult<PageResult<StationExtractDO>> getIceExtractDataPage(@Valid StationExtractPageReqVO pageReqVO) {
        if (pageReqVO.getCurrentDay() == null || pageReqVO.getCurrentDay().length == 0 || Arrays.stream(pageReqVO.getCurrentDay()).anyMatch(Objects::isNull)) {
            return success(PageResult.empty());
        }
        pageReqVO.setLogId(null);
        PageResult<StationExtractDO> pageResult = stationExtractService.getStationExtractPage(pageReqVO);
        return success(pageResult);
    }
}