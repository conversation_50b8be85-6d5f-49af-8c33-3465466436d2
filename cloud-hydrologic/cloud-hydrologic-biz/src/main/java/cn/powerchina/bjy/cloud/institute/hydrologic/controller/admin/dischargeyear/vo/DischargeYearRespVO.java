package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeyear.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水文站-输沙率-冰情水温年化 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DischargeYearRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "11344")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3745")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "年平均输沙率(kg/s)")
    @ExcelProperty("年平均输沙率(kg/s)")
    private String averageValue;

    @Schema(description = "最大日输沙率(kg/s)")
    @ExcelProperty("最大日输沙率(kg/s)")
    private String maxValue;

    @Schema(description = "最大日输沙率出现日期")
    @ExcelProperty("最大日输沙率出现日期")
    private String maxValueDate;

    @Schema(description = "年输沙量(万t)")
    @ExcelProperty("年输沙量(万t)")
    private String totalValue;

    @Schema(description = "输沙模数(t/km2)")
    @ExcelProperty("输沙模数(t/km2)")
    private String dischargeModulus;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}