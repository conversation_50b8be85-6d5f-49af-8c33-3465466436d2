package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringproject.vo;


import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo.EngineeringProjectParametersSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo.TidelevelYearUpdateReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 工程项目信息-项目基本情况新增/修改 Request VO")
@Data
public class EngineeringProjectuUpdateReqVO {
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17069")
    private Long id;

    @Schema(description = "项目名称", example = "王五")
    private String name;

    @Schema(description = "所属国家")
    private String country;

    @Schema(description = "所属省级行政区")
    private String province;

    @Schema(description = "所属市级行政区")
    private String city;

    @Schema(description = "所属县级行政区")
    private String county;

    @Schema(description = "详细位置")
    private String address;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "开发方式")
    private String developWay;

    @Schema(description = "是否纳规")
    private String designStage;

    @Schema(description = "工作进展")
    private String progress;

    @Schema(description = "业主单位")
    private String ownerUnit;

    @Schema(description = "重大变更情况说明")
    private String explanation;

    @Schema(description = "要删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "编辑的数据")
    private List<EngineeringProjectParametersSaveReqVO> engineeringProjectParametersList;
}
