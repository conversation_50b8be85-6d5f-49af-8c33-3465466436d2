package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationmonth.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationday.vo.StationDaySaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.List;

import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 站点-月统计新增/修改 Request VO")
@Data
public class StationMonthSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13184")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13254")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "2")
    private Integer stationType;

    @Schema(description = "数据类型", example = "2")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "月")
    private Integer month;

    @Schema(description = "月平均值")
    private String averageValue;

    @Schema(description = "月平均值备注", example = "你说的对")
    private String averageValueRemark;

    @Schema(description = "月最大值")
    private String maxValue;

    @Schema(description = "月最大值出现日期")
    private String maxValueDate;

    @Schema(description = "月最大值备注", example = "你说的对")
    private String maxValueRemark;

    @Schema(description = "月最小值")
    private String minValue;

    @Schema(description = "月最小值出现日期")
    private String minValueDate;

    @Schema(description = "月最小值备注", example = "随便")
    private String minValueRemark;

    @Schema(description = "降水-月降水量(mm)")
    private String value;

    @Schema(description = "降水-月降水日数")
    private Integer valueDays;

    @Schema(description = "降水-最大日降水量(mm)")
    private String maxDayValue;

    @Schema(description = "0.005粒径级（mm）")
    private String grain005;

    @Schema(description = "0.007粒径级（mm）")
    private String grain007;

    @Schema(description = "0.01粒径级（mm）")
    private String grain01;

    @Schema(description = "0.025粒径级（mm）")
    private String grain025;

    @Schema(description = "0.05粒径级（mm）")
    private String grain05;

    @Schema(description = "0.1粒径级（mm）")
    private String grain1;

    @Schema(description = "0.25粒径级（mm）")
    private String grain25;

    @Schema(description = "0.5粒径级（mm）")
    private String grain5;

    @Schema(description = "1.0粒径级（mm）")
    private String grain10;

    @Schema(description = "2.0粒径级（mm）")
    private String grain20;

    @Schema(description = "3.0粒径级（mm）")
    private String grain30;

    @Schema(description = "中数粒径(mm)")
    private String grainMid;

    @Schema(description = "平均粒径(mm)")
    private String grainAverage;

    @Schema(description = "最大粒径(mm)")
    private String grainMax;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer latest;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationMonthData> dataList;

    @Schema(description = "记录时间")
    private LocalDate currentDay;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "本次更新删除的数据id")
    private List<Long> deleteIds;

    @Data
    public static class StationMonthData {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13254")
        private Long stationId;

        @Schema(description = "数据类型", example = "2")
        private Integer dataType;

        @Schema(description = "年")
        private String year;

        @Schema(description = "月")
        private String month;

        @Schema(description = "月平均值")
        private String averageValue;

        @Schema(description = "月平均值备注", example = "你说的对")
        private String averageValueRemark;

        @Schema(description = "月最大值")
        private String maxValue;

        @Schema(description = "月最大值出现日期")
        private String maxValueDate;

        @Schema(description = "月最大值备注", example = "你说的对")
        private String maxValueRemark;

        @Schema(description = "月最小值")
        private String minValue;

        @Schema(description = "月最小值出现日期")
        private String minValueDate;

        @Schema(description = "月最小值备注", example = "随便")
        private String minValueRemark;

        @Schema(description = "降水-月降水量(mm)")
        private String value;

        @Schema(description = "降水-月降水日数")
        private Integer valueDays;

        @Schema(description = "降水-最大日降水量(mm)")
        private String maxDayValue;

        @Schema(description = "0.005粒径级（mm）")
        private String grain005;

        @Schema(description = "0.007粒径级（mm）")
        private String grain007;

        @Schema(description = "0.01粒径级（mm）")
        private String grain01;

        @Schema(description = "0.025粒径级（mm）")
        private String grain025;

        @Schema(description = "0.05粒径级（mm）")
        private String grain05;

        @Schema(description = "0.1粒径级（mm）")
        private String grain1;

        @Schema(description = "0.25粒径级（mm）")
        private String grain25;

        @Schema(description = "0.5粒径级（mm）")
        private String grain5;

        @Schema(description = "1.0粒径级（mm）")
        private String grain10;

        @Schema(description = "2.0粒径级（mm）")
        private String grain20;

        @Schema(description = "3.0粒径级（mm）")
        private String grain30;

        @Schema(description = "中数粒径(mm)")
        private String grainMid;

        @Schema(description = "平均粒径(mm)")
        private String grainAverage;

        @Schema(description = "最大粒径(mm)")
        private String grainMax;

        @Schema(description = "备注", example = "你说的对")
        private String remark;
    }
}