package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodhour;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodhour.vo.StationRainPeriodHourPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodhour.vo.StationRainPeriodHourRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodhour.vo.StationRainPeriodHourSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodhour.StationRainPeriodHourDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationrainperiodhour.StationRainPeriodHourService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 降水-各时段最大降水量(小时时段)")
@RestController
@RequestMapping("/plan/hydrologic/station/rain/period/hour")
@Validated
public class StationRainPeriodHourController {

    @Resource
    private StationRainPeriodHourService stationRainPeriodHourService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-获得降水-各时段最大降水量(小时时段)分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:query-rain')")
    public CommonResult<PageResult<StationRainPeriodHourRespVO>> getStationRainPeriodHourPage(@Valid StationRainPeriodHourPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        PageResult<StationRainPeriodHourDO> pageResult = stationRainPeriodHourService.getStationRainPeriodHourPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodHourRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入降水-各时段最大降水量(小时时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> importRainStationRainPeriodHourFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                             @RequestParam("dataType") Integer dataType,
                                                                             @RequestParam("file") MultipartFile file) {
        stationRainPeriodHourService.importStationRainPeriodHourFeatureExcel(stationId, StationEnum.RAIN.getType(),dataType, file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新降水-各时段最大降水量(小时时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:update-rain')")
    public CommonResult<Boolean> updateStationRainPeriodHour(@Valid @RequestBody StationRainPeriodHourSaveReqVO updateReqVO) {
        stationRainPeriodHourService.updateStationRainPeriodHour(updateReqVO);
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-获得降水-各时段最大降水量(小时时段)分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:query-hydrologic')")
    public CommonResult<PageResult<StationRainPeriodHourRespVO>> getStationHydrologicPeriodHourPage(@Valid StationRainPeriodHourPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        PageResult<StationRainPeriodHourDO> pageResult = stationRainPeriodHourService.getStationRainPeriodHourPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodHourRespVO.class));
    }

    @PostMapping("/import/hydrologic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入降水-各时段最大降水量(小时时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "dataType", description = "数据类型", required = true)
    public CommonResult<Boolean> importRainStationHydrologicPeriodHourFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                                   @RequestParam("dataType") Integer dataType,
                                                                                   @RequestParam("file") MultipartFile file) {
        stationRainPeriodHourService.importStationRainPeriodHourFeatureExcel(stationId, StationEnum.HYDROLOGIC.getType(), dataType, file);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新降水-各时段最大降水量(小时时段)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:update-hydrologic')")
    public CommonResult<Boolean> updateStationHydrologicPeriodHour(@Valid @RequestBody StationRainPeriodHourSaveReqVO updateReqVO) {
        stationRainPeriodHourService.updateStationRainPeriodHour(updateReqVO);
        return success(true);
    }

    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出降水-各时段最大降水量(小时时段) Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:export-rain')")
    public void exportStationRainPeriodHourExcel(@Valid StationRainPeriodHourPageReqVO pageReqVO,
                                                 HttpServletResponse response) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodHourService.exportStationRainPeriodHourExcel(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出降水-各时段最大降水量(小时时段) Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:export-hydrologic')")
    public void exportStationHydrologicPeriodHourExcel(@Valid StationRainPeriodHourPageReqVO pageReqVO,
                                                       HttpServletResponse response) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodHourService.exportStationRainPeriodHourExcel(response, pageReqVO);
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出降水-各时段最大降水量(小时时段) Excel")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:export-data-rain')")
    public void exportStationRainPeriodHourDataExcel(@RequestParam("stationId") Long stationId,
                                                     HttpServletResponse response) {
        StationRainPeriodHourPageReqVO pageReqVO = new StationRainPeriodHourPageReqVO();
        pageReqVO.setStationId(stationId);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodHourService.exportStationRainPeriodHourExcel(response, pageReqVO);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出降水-各时段最大降水量(小时时段) Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:export-data-hydrologic')")
    public void exportStationHydrologicPeriodHourDataExcel(@RequestParam("stationId") Long stationId,
                                                           HttpServletResponse response) {
        StationRainPeriodHourPageReqVO pageReqVO = new StationRainPeriodHourPageReqVO();
        pageReqVO.setStationId(stationId);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainPeriodHourService.exportStationRainPeriodHourExcel(response, pageReqVO);
    }

    @GetMapping("/page/data/rain")
    @Operation(summary = "雨量站-数据检索-获得降水-各时段最大降水量(小时时段)分页")
    @HydrologicOperation()
    @Parameter(name = "stationId", description = "雨量站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:query-data-rain')")
    public CommonResult<PageResult<StationRainPeriodHourRespVO>> getStationRainPeriodHourDataPage(StationRainPeriodHourPageReqVO pageReqVO) {
        PageResult<StationRainPeriodHourDO> pageResult = stationRainPeriodHourService.getStationRainPeriodHourPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodHourRespVO.class));
    }

    @GetMapping("/page/data/hydrologic")
    @Operation(summary = "水文站-数据检索-获得降水-各时段最大降水量(小时时段)分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_RAIN_PERIOD_HOUR)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-period-hour:query-data-hydrologic')")
    public CommonResult<PageResult<StationRainPeriodHourRespVO>> getStationHydrologicPeriodHourDataPage(StationRainPeriodHourPageReqVO pageReqVO) {
        PageResult<StationRainPeriodHourDO> pageResult = stationRainPeriodHourService.getStationRainPeriodHourPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainPeriodHourRespVO.class));
    }

}