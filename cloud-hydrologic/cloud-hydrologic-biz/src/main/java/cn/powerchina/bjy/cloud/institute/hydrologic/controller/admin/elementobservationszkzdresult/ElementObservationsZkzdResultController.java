package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult.vo.ElementObservationsZkzdResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult.vo.ElementObservationsZkzdResultRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult.vo.ElementObservationsZkzdResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult.vo.UpdateElementObservationsZkzdReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.elementobservationszkzdresult.ElementObservationsZkzdResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.elementobservationszkzdresult.ElementObservationsZkzdResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 气象-六要素观测成果表(中科正奇ZK-ZDx)")
@RestController
@RequestMapping("/plan/hydrologic/element-observations-zkzd-result")
@Validated
public class ElementObservationsZkzdResultController {
    @Resource
    private ElementObservationsZkzdResultService elementObservationsZkzdResultService;

    @PostMapping("/create")
    @Operation(summary = "创建气象-六要素观测成果表(中科正奇ZK-ZDx)")
    @PreAuthorize("@ss.hasPermission('hydrologic:element-observations-zkzd-result:create')")
    public CommonResult<Long> createElementObservationsZkzdResult(@Valid @RequestBody ElementObservationsZkzdResultSaveReqVO createReqVO) {
        return success(elementObservationsZkzdResultService.createElementObservationsZkzdResult(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新气象-六要素观测成果表(中科正奇ZK-ZDx)")
//    @PreAuthorize("@ss.hasPermission('hydrologic:element-observations-zkzd-result:update')")
    public CommonResult<Boolean> updateElementObservationsZkzdResult(@Valid @RequestBody UpdateElementObservationsZkzdReqVO updateReqVO) {
        elementObservationsZkzdResultService.updateElementObservationsZkzdResult(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除气象-六要素观测成果表(中科正奇ZK-ZDx)")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hydrologic:element-observations-zkzd-result:delete')")
    public CommonResult<Boolean> deleteElementObservationsZkzdResult(@RequestParam("id") Long id) {
        elementObservationsZkzdResultService.deleteElementObservationsZkzdResult(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得气象-六要素观测成果表(中科正奇ZK-ZDx)")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hydrologic:element-observations-zkzd-result:query')")
    public CommonResult<ElementObservationsZkzdResultRespVO> getElementObservationsZkzdResult(@RequestParam("id") Long id) {
        ElementObservationsZkzdResultDO elementObservationsZkzdResult = elementObservationsZkzdResultService.getElementObservationsZkzdResult(id);
        return success(BeanUtils.toBean(elementObservationsZkzdResult, ElementObservationsZkzdResultRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得气象-六要素观测成果表(中科正奇ZK-ZDx)分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:element-observations-zkzd-result:query')")
    public CommonResult<PageResult<ElementObservationsZkzdResultRespVO>> getElementObservationsZkzdResultPage(@Valid ElementObservationsZkzdResultPageReqVO pageReqVO) {
        PageResult<ElementObservationsZkzdResultDO> pageResult = elementObservationsZkzdResultService.getElementObservationsZkzdResultPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ElementObservationsZkzdResultRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出气象-六要素观测成果表(中科正奇ZK-ZDx) Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:element-observations-zkzd-result:export')")
    public void exportElementObservationsZkzdResultExcel(@Valid ElementObservationsZkzdResultPageReqVO pageReqVO,
                                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ElementObservationsZkzdResultDO> list = elementObservationsZkzdResultService.getElementObservationsZkzdResultPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "气象-六要素观测成果表(中科正奇ZK-ZDx).xls", "数据", ElementObservationsZkzdResultRespVO.class,
                BeanUtils.toBean(list, ElementObservationsZkzdResultRespVO.class));
    }

    @PostMapping("/import")
    @Operation(summary = "导入历年最大风速及风向")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:import')")
    public CommonResult<Boolean> importSuspendedSedimentGradingResult(@RequestParam("file") MultipartFile file,
                                                                      @RequestParam("stationId") Long stationId,
                                                                      @RequestParam("stationType") Integer stationType,
                                                                      @RequestParam("dataType") Integer dataType) throws IOException {
        elementObservationsZkzdResultService.importExcel(file, stationId, stationType, dataType);
        return success(true);
    }

    @PostMapping("/import_check")
    @Operation(summary = "校验导入历年最大风速及风向")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:import')")
    public CommonResult<Boolean> checkImportSuspendedSedimentGradingResult(@RequestParam("file") MultipartFile file,
                                                                           @RequestParam("stationId") Long stationId,
                                                                           @RequestParam("stationType") Integer stationType,
                                                                           @RequestParam("dataType") Integer dataType) throws IOException {
        boolean b = elementObservationsZkzdResultService.checkImportExcel(file, stationId, stationType, dataType);
        return success(b);
    }
}
