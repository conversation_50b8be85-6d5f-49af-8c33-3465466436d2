package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.write.style.ContentLoopMerge;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 水文-统计表表单和统计图数据信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StatisticalIndexRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "29540")
    private Long id;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "2")
    private Integer stationType;

    @Schema(description = "站名")
    private String hydrologicName;

    @Schema(description = "列表")
    public List<StatisticalFrom> statisticalFroms;

    @Schema(description = "图表")
    public List<StatisticalHistogram> statisticalHistograms;

    @Schema(description = "目录标签")
    private List<Label> labels;

    @Data
    public static class Label{
        @Schema(description = "目录标签名")
        private String name;

        @Schema(description = "目录id")
        private Long statsId;
    }

    @Data
    public static class StatisticalFrom {

        @Schema(description = "站点id")
        private Long stationId;

        @Schema(description = "表单名称")
        private String fromName;

        @Schema(description = "年限")
        private String years;

        @Schema(description = "起止时间")
        private String date;

        @Schema(description = "站名")
        private String stationName;

        @Schema(description = "位置")
        private String location;
    }

    @Data
    public static class StatisticalHistogram {
        @Schema(description = "表单名称")
        private String name;

        @Schema(description = "年限")
        private int[] node;
    }

}
