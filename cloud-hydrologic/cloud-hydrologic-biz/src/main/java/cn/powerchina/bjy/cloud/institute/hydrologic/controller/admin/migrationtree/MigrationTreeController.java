package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.migrationtree;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.migrationtree.vo.MigrationTreePageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.migrationtree.vo.MigrationTreeRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.migrationtree.vo.MigrationTreeSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.migrationtree.MigrationTreeDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.migrationtree.MigrationTreeService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;


@Tag(name = "管理后台 - 备份与还原tree")
@RestController
@RequestMapping("/hydrologic/migration-tree")
@Validated
public class MigrationTreeController {

    @Resource
    private MigrationTreeService migrationTreeService;

//    @PostMapping("/create")
//    @Operation(summary = "创建备份与还原tree")
//    @PreAuthorize("@ss.hasPermission('hydrologic:migration-tree:create')")
//    public CommonResult<Long> createMigrationTree(@Valid @RequestBody MigrationTreeSaveReqVO createReqVO) {
//        return success(migrationTreeService.createMigrationTree(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新备份与还原tree")
//    @PreAuthorize("@ss.hasPermission('hydrologic:migration-tree:update')")
//    public CommonResult<Boolean> updateMigrationTree(@Valid @RequestBody MigrationTreeSaveReqVO updateReqVO) {
//        migrationTreeService.updateMigrationTree(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除备份与还原tree")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:migration-tree:delete')")
//    public CommonResult<Boolean> deleteMigrationTree(@RequestParam("id") Long id) {
//        migrationTreeService.deleteMigrationTree(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得备份与还原tree")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('hydrologic:migration-tree:query')")
//    public CommonResult<MigrationTreeRespVO> getMigrationTree(@RequestParam("id") Long id) {
//        MigrationTreeDO migrationTree = migrationTreeService.getMigrationTree(id);
//        return success(BeanUtils.toBean(migrationTree, MigrationTreeRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得备份与还原tree分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:migration-tree:query')")
//    public CommonResult<PageResult<MigrationTreeRespVO>> getMigrationTreePage(@Valid MigrationTreePageReqVO pageReqVO) {
//        PageResult<MigrationTreeDO> pageResult = migrationTreeService.getMigrationTreePage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, MigrationTreeRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出备份与还原tree Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:migration-tree:export')")
//    @OperateLog(type = EXPORT)
//    public void exportMigrationTreeExcel(@Valid MigrationTreePageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<MigrationTreeDO> list = migrationTreeService.getMigrationTreePage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "备份与还原tree.xls", "数据", MigrationTreeRespVO.class,
//                        BeanUtils.toBean(list, MigrationTreeRespVO.class));
//    }

}