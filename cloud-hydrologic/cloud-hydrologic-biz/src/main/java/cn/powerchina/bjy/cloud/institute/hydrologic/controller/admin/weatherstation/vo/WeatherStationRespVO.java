package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherstation.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 气象站 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WeatherStationRespVO {

    @Schema(description = "主键", example = "24047")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "站名", example = "赵六")
    @JsonProperty("hydrologicName")
    @ExcelProperty("站名")
    private String hydrologicName;

    @Schema(description = "国")
    @ExcelProperty("国")
    private String country;

    @Schema(description = "省")
    @ExcelProperty("省")
    private String province;

    @Schema(description = "市")
    @ExcelProperty("市")
    private String city;

    @Schema(description = "县")
    @ExcelProperty("县")
    private String county;

    @Schema(description = "详细地址")
    @ExcelProperty("详细地址")
    private String address;

    @Schema(description = "设立机构")
    @ExcelProperty("设立机构")
    private String establishInstitution;

    @Schema(description = "经度（度）")
    @ExcelProperty("经度（度）")
    private String longitude;

    @Schema(description = "纬度（度）")
    @ExcelProperty("纬度（度）")
    private String latitude;

    @Schema(description = "海拔高度（米）")
    @ExcelProperty("海拔高度（米）")
    private String altitude;

    @Schema(description = "站址迁移")
    @ExcelProperty("站址迁移")
    private String siteRelocation;

    @Schema(description = "开始观测年份")
    @ExcelProperty("开始观测年份")
    private String startObservationYear;

    @Schema(description = "开始观测月份")
    @ExcelProperty("开始观测月份")
    private String startObservationMonth;

    @Schema(description = "缺测时间段")
    @ExcelProperty("缺测时间段")
    private String missingTestingTimePeriod;

    @Schema(description = "观测方式")
    @ExcelProperty("观测方式")
    private String observationMethod;

    @Schema(description = "年限")
    @ExcelProperty("年限")
    private Integer dataAge;

    @Schema(description = "资料起止年份")
    @ExcelProperty("资料起止年份")
    private String dataYear;

    @Schema(description = "开始年份")
    @ExcelProperty("开始年份")
    private String dataStartYear;

    @Schema(description = "结束年份")
    @ExcelProperty("结束年份")
    private String dataEndYear;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}