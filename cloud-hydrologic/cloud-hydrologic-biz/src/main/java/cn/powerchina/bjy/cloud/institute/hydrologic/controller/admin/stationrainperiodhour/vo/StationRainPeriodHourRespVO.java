package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodhour.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 降水-各时段最大降水量(小时时段) Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationRainPeriodHourRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "1h降水量")
    @ExcelProperty("1h降水量")
    private String valueDay1;

    @Schema(description = "1h开始月日")
    @ExcelProperty("1h开始月日")
    private String monthDayStart1;

    @Schema(description = "2h降水量")
    @ExcelProperty("2h降水量")
    private String valueDay2;

    @Schema(description = "2h开始月日")
    @ExcelProperty("2h开始月日")
    private String monthDayStart2;

    @Schema(description = "3h降水量")
    @ExcelProperty("3h降水量")
    private String valueDay3;

    @Schema(description = "3h开始月日")
    @ExcelProperty("3h开始月日")
    private String monthDayStart3;

    @Schema(description = "6h降水量")
    @ExcelProperty("6h降水量")
    private String valueDay6;

    @Schema(description = "6h开始月日")
    @ExcelProperty("6h开始月日")
    private String monthDayStart6;

    @Schema(description = "12h降水量")
    @ExcelProperty("12h降水量")
    private String valueDay12;

    @Schema(description = "12h开始月日")
    @ExcelProperty("12h开始月日")
    private String monthDayStart12;

    @Schema(description = "24h降水量")
    @ExcelProperty("24h降水量")
    private String valueDay24;

    @Schema(description = "24h开始月日")
    @ExcelProperty("24h开始月日")
    private String monthDayStart24;

    @Schema(description = "48h降水量")
    @ExcelProperty("48h降水量")
    private String valueDay48;

    @Schema(description = "48h开始月日")
    @ExcelProperty("48h开始月日")
    private String monthDayStart48;

    @Schema(description = "72h降水量")
    @ExcelProperty("72h降水量")
    private String valueDay72;

    @Schema(description = "72h开始月日")
    @ExcelProperty("72h开始月日")
    private String monthDayStart72;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}