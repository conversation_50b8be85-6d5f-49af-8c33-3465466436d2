package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class EngineeringProjectParametersModel {
    @Schema(description = "参数")
    @ExcelProperty(index =0)
    private String value1;

    @Schema(description = "规划")
    @ExcelProperty(index =1)
    private String value2;

    @Schema(description = "规划备注")
    @ExcelProperty(index =2)
    private String value3;

    @Schema(description = "预可")
    @ExcelProperty(index =3)
    private String value4;

    @Schema(description = "预可备注")
    @ExcelProperty(index =4)
    private String value5;

    @Schema(description = "三专")
    @ExcelProperty(index =5)
    private String value6;

    @Schema(description = "三专备注")
    @ExcelProperty(index =6)
    private String value7;

    @Schema(description = "可研")
    @ExcelProperty(index =7)
    private String value8;

    @Schema(description = "可研备注")
    @ExcelProperty(index =8)
    private String value9;

    @Schema(description = "详图")
    @ExcelProperty(index =9)
    private String value10;

    @Schema(description = "详图备注")
    @ExcelProperty(index =10)
    private String value11;

    @Schema(description = "已建")
    @ExcelProperty(index =11)
    private String value12;

    @Schema(description = "已建备注")
    @ExcelProperty(index =12)
    private String value13;
}
