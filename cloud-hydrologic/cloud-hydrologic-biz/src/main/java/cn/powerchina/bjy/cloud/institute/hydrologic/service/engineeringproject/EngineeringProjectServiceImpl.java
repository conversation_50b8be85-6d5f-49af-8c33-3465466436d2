package cn.powerchina.bjy.cloud.institute.hydrologic.service.engineeringproject;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringproject.vo.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo.EngineeringProjectParametersModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo.EngineeringProjectParametersRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo.EngineeringProjectParametersSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.IndexInfoRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.IndexReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.station.vo.StationRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.engineeringproject.EngineeringProjectDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.engineeringprojectparameters.EngineeringProjectParametersDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filetree.FileTreeDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.roleapply.RoleApplyDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.engineeringproject.EngineeringProjectMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.engineeringprojectparameters.EngineeringProjectParametersMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.filetree.FileTreeMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.PlanningDesignConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.RoleApplyBindTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.TideDayImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.TideMonthImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.engineeringprojectparameters.EngineeringProjectParametersService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.filedocument.FileDocumentService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.filetree.FileTreeService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.roleapply.RoleApplyService;
import cn.powerchina.bjy.cloud.institute.hydrologic.task.AreaManager;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.RoleUtil;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import com.alibaba.excel.EasyExcel;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.bean.BeanUtil;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 工程项目信息-项目基本情况 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EngineeringProjectServiceImpl implements EngineeringProjectService {

    @Resource
    private EngineeringProjectMapper engineeringProjectMapper;
    @Autowired
    private EngineeringProjectParametersService engineeringProjectParametersService;
    @Resource
    private EngineeringProjectParametersMapper engineeringProjectParametersMapper;
    @Resource
    private  AreaManager areaManager;
    @Resource
    private FileTreeMapper fileTreeMapper;

    @Autowired
    private FileTreeService fileTreeService;

    @Autowired
    private FileDocumentService fileDocumentService;

    @Autowired
    private RoleApplyService roleApplyService;

    private static final AtomicLong sequenceCounter = new AtomicLong(0);
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createEngineeringProject(EngineeringProjectSaveReqVO createReqVO) {
        //查询是否已经存在
        EngineeringProjectDO engineeringProjectDO = engineeringProjectMapper.selectOne(new LambdaQueryWrapperX<EngineeringProjectDO>()
                .eq(EngineeringProjectDO::getName, createReqVO.getName()));
        if (engineeringProjectDO != null) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_EXCEL_ERROR);
        }
        // 插入
        EngineeringProjectDO engineeringProject = BeanUtils.toBean(createReqVO, EngineeringProjectDO.class);
        engineeringProjectMapper.insert(engineeringProject);
        //插入空的参数表
        //获取路径
        try {
            InputStream resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + "550111" + PlanningDesignConstants.EXCEL_SUFFIX).getInputStream();
            List<EngineeringProjectParametersModel> parametersList = EasyExcel.read(resource, EngineeringProjectParametersModel.class, null).sheet(1).headRowNumber(0).doReadSync();
            List<EngineeringProjectParametersDO> parametersDOS = parametersModelToDb(parametersList,engineeringProject.getId());
            sequenceCounter.set(0);
            for(EngineeringProjectParametersDO parameters: parametersDOS){
                parameters.setId(null);
                parameters.setProjectId(engineeringProject.getId());
                engineeringProjectParametersMapper.insert(parameters);
            }
            //更新文件树
            fileTreeService.addFileTreeType(engineeringProject.getId(), engineeringProject.getName(),1);
        } catch (IOException e) {
            log.error("新增失败", e);
            throw exception(ErrorCodeConstants.FILE_UPLOAD_ERROR);
        }// 返回
        return engineeringProject.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEngineeringProject(EngineeringProjectSaveReqVO updateReqVO) {
        // 校验存在
        validateEngineeringProjectExists(updateReqVO.getId());
        // 更新主表
        EngineeringProjectDO updateObj = BeanUtils.toBean(updateReqVO, EngineeringProjectDO.class);
        engineeringProjectMapper.updateById(updateObj);
        //更新参数表
        List<EngineeringProjectParametersDO> engineeringProjectParametersDOS = flattenParametersTree(updateReqVO.getParametersArray());
        deleteParametersById(updateReqVO.getId());
        if(engineeringProjectParametersDOS.size()>0){
            for (EngineeringProjectParametersDO engineeringProjectParametersDO : engineeringProjectParametersDOS) {
                engineeringProjectParametersDO.setId(null);
                engineeringProjectParametersDO.setProjectId(updateReqVO.getId());
                engineeringProjectParametersDO.setNum(sequenceCounter.incrementAndGet());
                engineeringProjectParametersMapper.insert(engineeringProjectParametersDO);
            }
           sequenceCounter.set(0);
       }
        //更新文件树
        fileTreeService.modifyFileTreeNameByProjectId(updateReqVO.getId(), updateReqVO.getName());
    }
    public List<EngineeringProjectParametersDO> flattenParametersTree(Map<String, Object> parametersTree) {
        List<EngineeringProjectParametersDO> flatList = new ArrayList<>();

        // 遍历每个层级（children1到children6）
        for (int i = 1; i <= 6; i++) {
            String key = "children" + i;
            if (parametersTree.containsKey(key)) {
                List<Object> children = (List<Object>) parametersTree.get(key);
                flattenNodes(children, flatList);
            }
        }

        return flatList;
    }


    private void flattenNodes(List<Object> nodes, List<EngineeringProjectParametersDO> result) {
        for (Object nodeObj : nodes) {
            Map<String, Object> node = (Map<String, Object>) nodeObj;

            // 转换当前节点
            EngineeringProjectParametersDO param = BeanUtil.toBean(node, EngineeringProjectParametersDO.class);
            result.add(param);

            // 递归处理子节点
            if (node.containsKey("children")) {
                List<Object> children = (List<Object>) node.get("children");
                flattenNodes(children, result);
            }
        }
    }
    private void createParameters(EngineeringProjectParametersSaveReqVO vo) {
        // 插入
        EngineeringProjectParametersDO parametersDO = BeanUtils.toBean(vo, EngineeringProjectParametersDO.class);
        engineeringProjectParametersMapper.insert(parametersDO);
    }

    private void deleteParametersById(Long id) {
        //根据主表id删除参数表
        engineeringProjectParametersMapper.delete(new LambdaQueryWrapperX<EngineeringProjectParametersDO>()
                .eq(EngineeringProjectParametersDO::getProjectId, id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEngineeringProject(Long id) {
        //校验是否存在文件资料
        FileTreeDO fileTreeDO = fileTreeService.selectFirstLevelFileTreeDO(id,"0");
        if (Objects.nonNull(fileTreeDO) && !CollectionUtils.isEmpty(fileDocumentService.findFileDocumentDOByTreeId(fileTreeDO.getId()))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_DELETE_EXISTS_ERROR);
        }
        // 校验存在
        validateEngineeringProjectExists(id);
        // 删除主表数据
        engineeringProjectMapper.deleteById(id);
        //根据主表id删除参数表
        deleteParametersById(id);
        //删除一级子树
        fileTreeService.deleteFileTreeByProjectId(id);
    }

    private void validateEngineeringProjectExists(Long id) {
        if (engineeringProjectMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
    }

    @Override
    public EngineeringProjectRespVO getEngineeringProject(Long id) {
        EngineeringProjectDO projectDO = engineeringProjectMapper.selectById(id);
        EngineeringProjectRespVO respVO = BeanUtils.toBean(projectDO, EngineeringProjectRespVO.class);
        Long projectId = respVO.getId();
        List<EngineeringProjectParametersDO> parametersDOList = engineeringProjectParametersMapper.selectList(new LambdaQueryWrapperX<EngineeringProjectParametersDO>()
               .eq(EngineeringProjectParametersDO::getProjectId, projectId));
        Map<String, Object> maps = buildProjectParametersTree(parametersDOList);
        respVO.setParametersArray(maps);
        return respVO;
    }
    public Map<String, Object> buildProjectParametersTree(List<EngineeringProjectParametersDO> parametersDOList) {
        Map<String, Object> map = new HashMap<>();

        // 初始化各层级列表
        List<Object> childrenList01 = new ArrayList<>();
        List<Object> childrenList02 = new ArrayList<>();
        List<Object> childrenList03 = new ArrayList<>();
        List<Object> childrenList04 = new ArrayList<>();
        List<Object> childrenList05 = new ArrayList<>();
        List<Object> childrenList06 = new ArrayList<>();

        // 定义各层级数字集合
        Set<Long> firstLevelNums = Set.of(1L, 2L, 3L, 8L, 13L);
        Set<Long> secondLevelNums = Set.of(14L, 18L, 24L, 31L);
        Set<Long> thirdLevelNums = Set.of(32L, 38L, 45L, 52L, 63L, 67L, 70L, 72L);
        Set<Long> fourthLevelNums = Set.of(73L, 86L, 99L, 104L);
        Set<Long> fifthLevelNums = Set.of(105L, 113L, 121L, 124L, 126L);
        Set<Long> sixthLevelNums = Set.of(127L, 132L, 133L, 134L, 135L, 139L, 145L);

        // 处理第一级
        for (EngineeringProjectParametersDO param : parametersDOList.stream()
                .filter(p -> firstLevelNums.contains(p.getNum()))
                .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                .collect(Collectors.toList())) {

            Map<String, Object> node = new LinkedHashMap<>();
            node.putAll(BeanUtil.beanToMap(param));

            if (param.getNum() == 3L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(4L, 5L, 6L, 7L).contains(p.getNum()))
                        .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) {
                    node.put("children", children);
                }
            } else if (param.getNum() == 8L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(9L, 10L, 11L, 12L).contains(p.getNum()))
                        .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) {
                    node.put("children", children);
                }
            }
            childrenList01.add(node);
        }

        // 处理第二级
        for (EngineeringProjectParametersDO param : parametersDOList.stream()
                .filter(p -> secondLevelNums.contains(p.getNum()))
                .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                .collect(Collectors.toList())) {

            Map<String, Object> node = new LinkedHashMap<>();
            node.putAll(BeanUtil.beanToMap(param));

            if (param.getNum() == 14L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(15L, 16L, 17L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            } else if (param.getNum() == 18L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(19L, 20L, 21L, 22L, 23L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            } else if (param.getNum() == 24L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(25L, 26L, 27L, 28L, 29L, 30L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            }
            childrenList02.add(node);
        }
        // 处理第三级
        for (EngineeringProjectParametersDO param : parametersDOList.stream()
                .filter(p -> thirdLevelNums.contains(p.getNum()))
                .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                .collect(Collectors.toList())) {

            Map<String, Object> node = new LinkedHashMap<>();
            node.putAll(BeanUtil.beanToMap(param));

            if (param.getNum() == 32L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(33L, 34L, 35L, 36L, 37L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            } else if (param.getNum() == 38L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(39L, 40L, 41L, 42L, 43L, 44L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            } else if (param.getNum() == 45L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(46L, 47L, 48L, 49L, 50L, 51L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            } else if (param.getNum() == 52L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(53L, 54L, 55L, 56L, 57L, 58L, 59L, 60L, 61L, 62L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            } else if (param.getNum() == 63L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(64L, 65L, 66L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            } else if (param.getNum() == 67L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(68L, 69L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            }else if (param.getNum() == 70L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(71L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            }
            childrenList03.add(node);
        }

        // 处理第四级
        for (EngineeringProjectParametersDO param : parametersDOList.stream()
                .filter(p -> fourthLevelNums.contains(p.getNum()))
                .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                .collect(Collectors.toList())) {

            Map<String, Object> node = new LinkedHashMap<>();
            node.putAll(BeanUtil.beanToMap(param));

            if (param.getNum() == 73L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(74L, 75L, 76L, 77L, 78L, 79L, 80L, 81L, 82L, 83L, 84L, 85L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            } else if (param.getNum() == 86L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(87L, 88L, 89L, 90L, 91L, 92L, 93L, 94L, 95L, 96L, 97L, 98L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            } else if (param.getNum() == 99L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(100L, 101L, 102L, 103L).contains(p.getNum()))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) node.put("children", children);
            }
            childrenList04.add(node);
        }
        // 处理第五级
        for (EngineeringProjectParametersDO param : parametersDOList.stream()
                .filter(p -> fifthLevelNums.contains(p.getNum()))
                .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                .collect(Collectors.toList())) {

            Map<String, Object> node = new LinkedHashMap<>();
            node.putAll(BeanUtil.beanToMap(param));

            if (param.getNum() == 105L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(106L, 107L, 108L, 109L, 110L, 111L, 112L).contains(p.getNum()))
                        .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) {
                    node.put("children", children);
                }
            }
            else if (param.getNum() == 113L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(114L, 115L, 116L, 117L, 118L, 119L, 120L).contains(p.getNum()))
                        .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) {
                    node.put("children", children);
                }
            }
            else if (param.getNum() == 121L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(122L, 123L).contains(p.getNum()))
                        .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) {
                    node.put("children", children);
                }
            }
            else if (param.getNum() == 124L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(125L).contains(p.getNum()))
                        .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) {
                    node.put("children", children);
                }
            }
            childrenList05.add(node);
        }

        // 处理第六级
        for (EngineeringProjectParametersDO param : parametersDOList.stream()
                .filter(p -> sixthLevelNums.contains(p.getNum()))
                .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                .collect(Collectors.toList())) {

            Map<String, Object> node = new LinkedHashMap<>();
            node.putAll(BeanUtil.beanToMap(param));

            if (param.getNum() == 127L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(128L, 129L, 130L, 131L).contains(p.getNum()))
                        .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) {
                    node.put("children", children);
                }
            }
            else if (param.getNum() == 135L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(136L, 137L, 138L).contains(p.getNum()))
                        .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) {
                    node.put("children", children);
                }
            }
            else if (param.getNum() == 139L) {
                List<Object> children = parametersDOList.stream()
                        .filter(p -> Set.of(140L, 141L, 142L, 143L, 144L).contains(p.getNum()))
                        .sorted(Comparator.comparingLong(EngineeringProjectParametersDO::getNum))
                        .collect(Collectors.toList());
                if (!children.isEmpty()) {
                    node.put("children", children);
                }
            }
            childrenList06.add(node);
        }

        // 构建最终结果
        map.put("children1", childrenList01);
        map.put("children2", childrenList02);
        map.put("children3", childrenList03);
        map.put("children4", childrenList04);
        map.put("children5", childrenList05);
        map.put("children6", childrenList06);

        return map;
    }
    @Override
    public PageResult<EngineeringProjectRespVO> getEngineeringProjectPage(IndexPageReqVO pageReqVO) {

        // 角色权限适用项目map(key:绑定类型; value:绑定数据id列表)
        Map<Integer, Set<Long>> bizIdMap = new HashMap<>();

        // 获取当前用户的角色
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());

        // 获取当前用户是否为超级管理员
        boolean superAdmin = false;
        for (RoleRespDTO roleRespDTO : roleList) {
            if (RoleUtil.isAdminRole(roleRespDTO.getCode())) {
                superAdmin = true;
                break;
            }
        }

        // 获取当前用户所属的角色选择的适用项目
        Set<Long> roleIdSet = roleList.stream().map(temp->temp.getId()).collect(Collectors.toSet());
        List<RoleApplyDO> roleApplyDOList = roleApplyService.listByRoleIdSet(roleIdSet);

        // 不同角色可能绑定了同一个项目，需要把不同角色的适用项目按绑定类型分组，每组的数据去重
        roleApplyDOList.stream().collect(Collectors.groupingBy(RoleApplyDO::getBindType)).forEach((bindType, tempRoleApplyDOList)->{
            Set<Long> bizIdSet = Optional.ofNullable(bizIdMap.get(bindType)).orElse(new HashSet<>());
            tempRoleApplyDOList.forEach(tempRoleApplyDO -> {
                Arrays.stream(Optional.ofNullable(tempRoleApplyDO.getBizId()).orElse("").split(",")).forEach(bizId->{
                    if (StringUtils.isNotBlank(bizId)) {
                        bizIdSet.add(Long.parseLong(bizId));
                    }
                });
            });
            bizIdMap.put(bindType, bizIdSet);
        });

        MPJLambdaWrapper<EngineeringProjectDO> lam = new MPJLambdaWrapper<EngineeringProjectDO>();
        if (pageReqVO.getName() != null && !pageReqVO.getName().isEmpty()) {
            lam.like(EngineeringProjectDO::getName, pageReqVO.getName());
        }
        if (pageReqVO.getCountry() != null && !pageReqVO.getCountry().isEmpty()) {
            lam.eq(EngineeringProjectDO::getCountry, pageReqVO.getCountry());
        }
        if (pageReqVO.getProvince() != null && !pageReqVO.getProvince().isEmpty()) {
            lam.eq(EngineeringProjectDO::getProvince, pageReqVO.getProvince());
        }
        if (pageReqVO.getCity() != null && !pageReqVO.getCity().isEmpty()) {
            lam.eq(EngineeringProjectDO::getCity, pageReqVO.getCity());
        }
        if (pageReqVO.getIsNaGui() != null && !pageReqVO.getIsNaGui().isEmpty()) {
            lam.eq(EngineeringProjectDO::getDesignStage, pageReqVO.getIsNaGui());
        }
        if (pageReqVO.getProgress() != null && !pageReqVO.getProgress().isEmpty()) {
            lam.eq(EngineeringProjectDO::getProgress, pageReqVO.getProgress());
        }
        if ((pageReqVO.getInstalledCapacityW() != null && !pageReqVO.getInstalledCapacityW().isEmpty())
                || (pageReqVO.getInstalledCapacityM() != null && !pageReqVO.getInstalledCapacityM().isEmpty())) {
            if (pageReqVO.getProgress() != null && !pageReqVO.getProgress().isEmpty()) {
                lam.leftJoin(EngineeringProjectParametersDO.class, EngineeringProjectParametersDO::getProjectId, EngineeringProjectDO::getId);
                if (pageReqVO.getInstalledCapacityM() != null && !pageReqVO.getInstalledCapacityM().isEmpty()) {
                    IndexPageReqVO finalIndexReqVO = pageReqVO;
                    lam.and(i -> {
                        i.eq(EngineeringProjectParametersDO::getValue1, "装机容量(MW)");
                        if (finalIndexReqVO.getProgress().equals("规划")) {
                            i.le(EngineeringProjectParametersDO::getValue2, finalIndexReqVO.getInstalledCapacityM());
                        } else if (finalIndexReqVO.getProgress().equals("预可")) {
                            i.le(EngineeringProjectParametersDO::getValue4, finalIndexReqVO.getInstalledCapacityM());
                        } else if (finalIndexReqVO.getProgress().equals("三专")) {
                            i.le(EngineeringProjectParametersDO::getValue6, finalIndexReqVO.getInstalledCapacityM());
                        } else if (finalIndexReqVO.getProgress().equals("可研")) {
                            i.le(EngineeringProjectParametersDO::getValue8, finalIndexReqVO.getInstalledCapacityM());
                        } else if (finalIndexReqVO.getProgress().equals("详图")) {
                            i.le(EngineeringProjectParametersDO::getValue10, finalIndexReqVO.getInstalledCapacityM());
                        } else if (finalIndexReqVO.getProgress().equals("已建")) {
                            i.le(EngineeringProjectParametersDO::getValue12, finalIndexReqVO.getInstalledCapacityM());
                        }
                    });
                }
                if (pageReqVO.getInstalledCapacityW() != null && !pageReqVO.getInstalledCapacityW().isEmpty()) {
                    IndexPageReqVO finalIndexReqVO = pageReqVO;
                    lam.and(i -> {
                        i.eq(EngineeringProjectParametersDO::getValue1, "装机容量(MW)");
                        if (finalIndexReqVO.getProgress().equals("规划")) {
                            i.ge(EngineeringProjectParametersDO::getValue2, finalIndexReqVO.getInstalledCapacityW());
                        } else if (finalIndexReqVO.getProgress().equals("预可")) {
                            i.ge(EngineeringProjectParametersDO::getValue4, finalIndexReqVO.getInstalledCapacityW());
                        } else if (finalIndexReqVO.getProgress().equals("三专")) {
                            i.ge(EngineeringProjectParametersDO::getValue6, finalIndexReqVO.getInstalledCapacityW());
                        } else if (finalIndexReqVO.getProgress().equals("可研")) {
                            i.ge(EngineeringProjectParametersDO::getValue8, finalIndexReqVO.getInstalledCapacityW());
                        } else if (finalIndexReqVO.getProgress().equals("详图")) {
                            i.ge(EngineeringProjectParametersDO::getValue10, finalIndexReqVO.getInstalledCapacityW());
                        } else if (finalIndexReqVO.getProgress().equals("已建")) {
                            i.ge(EngineeringProjectParametersDO::getValue12, finalIndexReqVO.getInstalledCapacityW());
                        }
                    });
                }
            }
        }
        List<EngineeringProjectDO> list = engineeringProjectMapper.selectList(lam);
        List<EngineeringProjectRespVO> voList= new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            List<Long> projectIdList = list.stream().map(EngineeringProjectDO::getId).toList();
            lam = new MPJLambdaWrapper<EngineeringProjectDO>();
            lam.in(EngineeringProjectDO::getId, projectIdList);
            if (pageReqVO.getContinuousHours() != null && !pageReqVO.getContinuousHours().isEmpty()) {
                IndexPageReqVO finalIndexReqVO = pageReqVO;
                if (pageReqVO.getProgress() != null && !pageReqVO.getProgress().isEmpty()) {
                    lam.leftJoin(EngineeringProjectParametersDO.class, EngineeringProjectParametersDO::getProjectId, EngineeringProjectDO::getId);
                    lam.and(i -> {
                        i.eq(EngineeringProjectParametersDO::getValue1, "连续满发小时数(h)");
                        if (finalIndexReqVO.getProgress().equals("规划")) {
                            i.eq(EngineeringProjectParametersDO::getValue2, finalIndexReqVO.getContinuousHours());
                        } else if (finalIndexReqVO.getProgress().equals("预可")) {
                            i.eq(EngineeringProjectParametersDO::getValue4, finalIndexReqVO.getContinuousHours());
                        } else if (finalIndexReqVO.getProgress().equals("三专")) {
                            i.eq(EngineeringProjectParametersDO::getValue6, finalIndexReqVO.getContinuousHours());
                        } else if (finalIndexReqVO.getProgress().equals("可研")) {
                            i.eq(EngineeringProjectParametersDO::getValue8, finalIndexReqVO.getContinuousHours());
                        } else if (finalIndexReqVO.getProgress().equals("详图")) {
                            i.eq(EngineeringProjectParametersDO::getValue10, finalIndexReqVO.getContinuousHours());
                        } else if (finalIndexReqVO.getProgress().equals("已建")) {
                            i.eq(EngineeringProjectParametersDO::getValue12, finalIndexReqVO.getContinuousHours());
                        }
                    });
                }
            }
            list = engineeringProjectMapper.selectList(lam.orderByDesc(EngineeringProjectDO::getId));

            // 根据配置的角色权限适用项目控制数据范围
            if (!superAdmin) {
                Set<Long> bizIdSet = new HashSet<>();
                bizIdSet.addAll(Optional.ofNullable(bizIdMap.get(RoleApplyBindTypeEnum.PROJECT.getType())).orElse(new HashSet<>()));
                list = list.stream().filter(temp->bizIdSet.contains(temp.getId())).collect(Collectors.toList());
            }

            int total = list.size();
            int pageSize = pageReqVO.getPageSize();
            int pageNo = pageReqVO.getPageNo();
            int fromIndex = (pageNo - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, total);
            List<EngineeringProjectDO> pageList = list.subList(fromIndex, toIndex);
             voList = pageList.stream()
                    .sorted(Comparator.comparingLong(EngineeringProjectDO::getId).reversed())
                    .map(item -> {
                        EngineeringProjectRespVO vo = BeanUtils.toBean(item, EngineeringProjectRespVO.class);
                        vo.setDesignStage(Integer.valueOf(vo.getDesignStage()) == 1 ? "是" : "否");
                        vo.setCountry(areaManager.getCountryNameByCode(item.getCountry()));
                        vo.setProvince(areaManager.getProvinceNameByCode(item.getProvince()));
                        vo.setCity(areaManager.getCityNameByCode(item.getCity()));
                        vo.setCounty(areaManager.getAreaNameByCode(item.getCounty()));
                        return vo;
                    })
                    .collect(Collectors.toList());
        }
        int total = list.size();
        return new PageResult<>(voList, (long) total);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(MultipartFile file) {
        //增加分布式锁，只能有一个导入,针对站点增加锁，站点的所有类型，只能有一个导入,页面新增时也要增加锁控制
//        String key = String.format(PlanningDesignConstants.STATION_TIDE_IMPORT_TIDE_KEY, stationId, dataType,"DAY");
//        RLock rLock = redisService.acquireDistributedLock(key);

        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }
            //获取excel内容
            List<EngineeringProjectModel> importDataList = EasyExcel.read(file.getInputStream(), EngineeringProjectModel.class, null).sheet(0).headRowNumber(1).doReadSync();
            List<EngineeringProjectParametersModel> parametersList = EasyExcel.read(file.getInputStream(), EngineeringProjectParametersModel.class, null).sheet(1).headRowNumber(0).doReadSync();
            if (CollectionUtils.isEmpty(importDataList)) {
                throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR,"项目信息不存在");
            }
            checkProjectModel(importDataList);
            checkProjectParametersModel(parametersList);
            EngineeringProjectDO projectDO =projectModelToDb(importDataList);
            //查询是否已经存在
            EngineeringProjectDO engineeringProjectDO = engineeringProjectMapper.selectOne(new LambdaQueryWrapperX<EngineeringProjectDO>()
                    .eq(EngineeringProjectDO::getName, projectDO.getName()));
            Long id = null;
            if(engineeringProjectDO!=null){
                id = engineeringProjectDO.getId();
                projectDO.setId(id);
                engineeringProjectMapper.updateById(projectDO);
            }else{
                engineeringProjectMapper.insert(projectDO);
                id = projectDO.getId();
            }
            if(parametersList.size()>1){
                //删除原有参数
                engineeringProjectParametersMapper.delete(new LambdaQueryWrapperX<EngineeringProjectParametersDO>()
                       .eq(EngineeringProjectParametersDO::getProjectId, id));
                List<EngineeringProjectParametersDO> parametersDOS = parametersModelToDb(parametersList,id);
                sequenceCounter.set(0);
                for(EngineeringProjectParametersDO parameters: parametersDOS){
                    parameters.setId(null);
                    parameters.setProjectId(id);
                    engineeringProjectParametersMapper.insert(parameters);
                }
            }
                List<FileTreeDO> treeDOList = new ArrayList<>();
                FileTreeDO treeDO = new FileTreeDO();
                treeDO.setTreeLevel(1);
                treeDO.setType("1");
                treeDO.setProjectId(projectDO.getId());
                treeDO.setTreeName(projectDO.getName());
                treeDOList.add(treeDO);
            //插入文件树
            fileTreeService.insertBatch(treeDOList,"1");
        } catch (IOException e) {
            log.error("importStationRainPeriodMinuteFeatureExcel--->error.", e);
        }
    }

    private List<EngineeringProjectParametersDO> parametersModelToDb(List<EngineeringProjectParametersModel> parametersList, Long projectId) {
        List<EngineeringProjectParametersDO> list = new ArrayList<>();

        // 设计人员
        processSection(parametersList, projectId, list, 1, parametersList.size() - 132, "备注");

        // 动能参数
        processSection(parametersList, projectId, list, 14, parametersList.size() - 114, "备注");

        // 水文
        processSection(parametersList, projectId, list, 32, parametersList.size() - 73, "备注");

        // 上、下水库
        processSection(parametersList, projectId, list, 73, parametersList.size() - 41, "备注");

        // 经济指标
        processSection(parametersList, projectId, list, 105, parametersList.size()-19, "备注");

        // 经济指标
        processSection(parametersList, projectId, list, 127, parametersList.size() , "备注");

        return list;
    }


    private void processSection(List<EngineeringProjectParametersModel> parametersList,
                                Long projectId,
                                List<EngineeringProjectParametersDO> resultList,
                                int startIndex,
                                int endIndex,
                                String remarkValue) {
        StringBuilder[] builders = new StringBuilder[6];
        for (int i = 0; i < builders.length; i++) {
            builders[i] = new StringBuilder();
        }

        for (int i = startIndex+1; i < endIndex; i++) {
            EngineeringProjectParametersModel model = parametersList.get(i);
            EngineeringProjectParametersDO parametersDO = new EngineeringProjectParametersDO();
            parametersDO.setProjectId(projectId);
            parametersDO.setValue1(model.getValue1());
            parametersDO.setValue2(model.getValue2());
            parametersDO.setValue4(model.getValue4());
            parametersDO.setValue6(model.getValue6());
            parametersDO.setValue8(model.getValue8());
            parametersDO.setValue10(model.getValue10());
            parametersDO.setValue12(model.getValue12());
            parametersDO.setNum(sequenceCounter.incrementAndGet());
            resultList.add(parametersDO);

            appendIfNotNull(builders[0], model.getValue3());
            appendIfNotNull(builders[1], model.getValue5());
            appendIfNotNull(builders[2], model.getValue7());
            appendIfNotNull(builders[3], model.getValue9());
            appendIfNotNull(builders[4], model.getValue11());
            appendIfNotNull(builders[5], model.getValue13());
        }

        EngineeringProjectParametersDO remark = new EngineeringProjectParametersDO();
        remark.setProjectId(projectId);
        remark.setValue1(remarkValue);
        remark.setValue2(builders[0].toString());
        remark.setValue4(builders[1].toString());
        remark.setValue6(builders[2].toString());
        remark.setValue8(builders[3].toString());
        remark.setValue10(builders[4].toString());
        remark.setValue12(builders[5].toString());
        remark.setNum(sequenceCounter.incrementAndGet());
        resultList.add(remark);
    }


    private void appendIfNotNull(StringBuilder builder, String value) {
        if (value != null) {
            builder.append(value).append(",");
        }
    }
    private EngineeringProjectDO projectModelToDb(List<EngineeringProjectModel> importDataList) {
        EngineeringProjectModel projectModel = importDataList.get(0);
        String countryCode =areaManager.getCountryCodeByName(projectModel.getCountry());
        String province = null;
        String city = null;
        String county = null;
        if(countryCode.equals("193")){
             province= areaManager.getProvinceCodeByName(projectModel.getProvince());
             city = areaManager.getCityCodeByName(projectModel.getProvince(), projectModel.getCity());
             county = areaManager.getAreaCodeByName(projectModel.getCounty());
        }
        EngineeringProjectDO projectDO = new EngineeringProjectDO();
        projectDO.setName(projectModel.getName());
        projectDO.setCountry(countryCode);
        if(countryCode.equals("193")){
            projectDO.setProvince(province);
            projectDO.setCity(city);
            projectDO.setCounty(county);
        }else {
            projectDO.setProvince(projectModel.getProvince());
            projectDO.setCity(projectModel.getCity());
            projectDO.setCounty(projectModel.getCounty());
        }
        projectDO.setAddress(projectModel.getAddress());
        projectDO.setLongitude(projectModel.getLongitude());
        projectDO.setLatitude(projectModel.getLatitude());
        projectDO.setDevelopWay(projectModel.getDevelopWay());
        projectDO.setProgress(projectModel.getProgress());
        projectDO.setDesignStage(projectModel.getDesignStage().equals("是") ? 1 : 0);
        projectDO.setOwnerUnit(projectModel.getOwnerUnit());
        projectDO.setExplanation(projectModel.getExplanation());
        return projectDO;
    }


    private void checkProjectParametersModel(List<EngineeringProjectParametersModel> parametersList) {
        for (EngineeringProjectParametersModel parameter : parametersList) {
            if (parameter.getValue1() != null) {
                parameter.setValue1(convertToSuperscript(parameter.getValue1()));
            }
        }
    }
            private String convertToSuperscript(String text) {
                return text.replaceAll("m2", "m²")
                        .replaceAll("m3", "m³");
//                        .replaceAll("km2", "km²");
            }
    private void checkProjectModel(List<EngineeringProjectModel> importDataList) {
        if (importDataList.size() > 1) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "一次只能存入一条项目基本情况");
        }
        EngineeringProjectModel projectModel = importDataList.get(0);
        if(StringUtils.isBlank(projectModel.getName())) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "项目名称为空");
        }
        if(StringUtils.isBlank(projectModel.getCountry())) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "观测场地点 国家填为空");
        }
        String countryCode =areaManager.getCountryCodeByName(projectModel.getCountry());
        if (StringUtils.isBlank(countryCode)) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "观测场地点 国家填写错误，" );
        }else if(countryCode.equals("193")){
            if (StringUtils.isBlank(projectModel.getProvince())) {
                throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR,  "观测场地点 省为空；");
            }
            if (StringUtils.isBlank(projectModel.getCity())) {
                throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "观测场地点 市为空；");
            }
            if (StringUtils.isBlank(projectModel.getCounty())) {
                throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "观测场地点 所属县级行政区为空；");
            }
            // 校验省市区否正确
            String province = areaManager.getProvinceCodeByName(projectModel.getProvince());
            if (StringUtils.isBlank(province)) {
                throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR,  "观测场地点 省填写错误");
            }
            String city = areaManager.getCityCodeByName(projectModel.getProvince(), projectModel.getCity());
            if (StringUtils.isBlank(city) || !areaManager.checkProvinceCityExists(province, city)) {
                throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR,  "观测场地点 市填写错误，" + projectModel.getProvince() + "下无" + projectModel.getCity());
            }
            if(StringUtils.isNotBlank(projectModel.getCounty())){
                String area = areaManager.getAreaCodeByName(projectModel.getCounty());
                if (StringUtils.isBlank(area) || !areaManager.checkCityAreaExists(city, area)) {
                    throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR,  "观测场地点 县填写错误，" + projectModel.getProvince() + projectModel.getCity() + "下无" + projectModel.getCounty());
                }
            }
        }
        String longitude = projectModel.getLongitude();
        String latitude = projectModel.getLatitude();
        if(longitude==null || latitude==null) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "经纬度不能为空");
        }
        // 确保是数字格式
        if (!longitude.matches("^[+-]?(180(\\.0*)?|([0-9]|[1-9]\\d|1[0-7]\\d)(\\.\\d+)?)$")) {  // 确保是数字格式
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR,"经度格式錯誤·");
        }
        if (!latitude.matches("^[+-]?(90(\\.0*)?|([0-9]|[1-9]\\d|1[0-7]\\d)(\\.\\d+)?)$")) {  // 确保是数字格式
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR,"纬度格式錯誤");
        }
         longitude = String.format("%.6f", Double.parseDouble(projectModel.getLongitude()));
         projectModel.setLongitude(longitude);
         latitude = String.format("%.6f", Double.parseDouble(projectModel.getLatitude()));
         projectModel.setLatitude(latitude);
        if(projectModel.getName().length() > 100) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "项目名称不能超过100字");
        }

        if(!StringUtils.isBlank(projectModel.getAddress())) {
            if (projectModel.getAddress().length() > 100) {
                throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "详细位置不能超过100字");
            }
        }
        if(StringUtils.isBlank(projectModel.getDevelopWay())) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "开发方式为空");
        }
        if(StringUtils.isBlank(projectModel.getProgress())) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "工作进展为空");
        }
        if(StringUtils.isBlank(projectModel.getDesignStage())) {
            throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "是否纳规为空");
        }
        if(!StringUtils.isBlank(projectModel.getOwnerUnit())) {
            if (projectModel.getOwnerUnit().length() > 100) {
                throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "详细位置不能超过100字");
            }
        }
        if(! StringUtils.isBlank(projectModel.getExplanation())) {
            if(projectModel.getExplanation().length() > 3000) {
                throw exception(ErrorCodeConstants.ENGINEERING_PROJECT_ERROR, "重大变更情况说明不能超过3000字");
            }
        }
    }

}