package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 水文-统计表索引数据信息新增/修改 Request VO")
@Data
public class StatisticalInfoSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "29319")
    private Long id;

    @Schema(description = "站点id", example = "6060")
    private Long stationId;

    @Schema(description = "统计表id", example = "12517")
    private Long statsId;

    @Schema(description = "开始年")
    private String startYear;

    @Schema(description = "结束年")
    private String endYear;

    @Schema(description = "年份数量", example = "16265")
    private Integer yearCount;

    @Schema(description = "年份数据 例如：2021;2022;2023")
    private String years;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "2")
    private String stationType;

}