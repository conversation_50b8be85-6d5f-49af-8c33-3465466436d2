package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherstation;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherstation.vo.WeatherStationPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherstation.vo.WeatherStationRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.weatherstation.vo.WeatherStationSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.weatherstation.WeatherStationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 气象站")
@RestController
@RequestMapping("/plan/hydrologic/weather/station")
@Validated
public class WeatherStationController {

    @Resource
    private WeatherStationService weatherStationService;

    @PostMapping("/create")
    @Operation(summary = "创建气象站")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-station:create')")
    public CommonResult<Long> createWeatherStation(@Valid @RequestBody WeatherStationSaveReqVO createReqVO) {
        return success(weatherStationService.createWeatherStation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新气象站")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-station:update')")
    public CommonResult<Boolean> updateWeatherStation(@Valid @RequestBody WeatherStationSaveReqVO updateReqVO) {
        weatherStationService.updateWeatherStation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除气象站")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-station:delete')")
    public CommonResult<Boolean> deleteWeatherStation(@RequestParam("id") Long id) {
        weatherStationService.deleteWeatherStation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得气象站")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-station:query')")
    public CommonResult<WeatherStationRespVO> getWeatherStation(@RequestParam("id") Long id) {
        WeatherStationRespVO weatherStation = weatherStationService.getWeatherStation(id);
        return success(weatherStation);
    }

    @GetMapping("/page")
    @Operation(summary = "获得气象站分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-station:query')")
    public CommonResult<PageResult<WeatherStationRespVO>> getWeatherStationPage(@Valid WeatherStationPageReqVO pageReqVO) {
        PageResult<WeatherStationRespVO> pageResult = weatherStationService.getWeatherStationPage(pageReqVO);
        return success(pageResult);
    }

//    @GetMapping("/export/excel")
//    @Operation(summary = "导出气象站 Excel")
////    @PreAuthorize("@ss.hasPermission('hydrologic:weather-station:export')")
////    @OperateLog(type = EXPORT)
//    public void exportWeatherStationExcel(@Valid WeatherStationPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
////        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
////        List<WeatherStationDO> list = weatherStationService.getWeatherStationPage(pageReqVO).getList();
////        // 导出 Excel
////        ExcelUtils.write(response, "气象站.xls", "数据", WeatherStationRespVO.class,
////                        BeanUtils.toBean(list, WeatherStationRespVO.class));
//    }

    @PostMapping("/import")
	@ExcelImportCheck
    @Operation(summary = "导入气象站 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-station:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importWeatherStationExcel(@RequestParam("file") MultipartFile file) throws IOException {
        weatherStationService.importStation(file);
        return success(true);
    }

}