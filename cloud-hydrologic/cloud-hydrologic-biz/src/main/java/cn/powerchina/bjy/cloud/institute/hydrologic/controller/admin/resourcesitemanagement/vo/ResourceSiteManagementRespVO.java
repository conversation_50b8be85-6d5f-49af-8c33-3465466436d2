package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资源站点管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ResourceSiteManagementRespVO {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @ExcelProperty("主键")
    private Long id;

    /**
     * 站点名称
     */
    @Schema(description = "站点名称")
    @ExcelProperty("站点名称")
    private String powerStationName;


    /**
     * 站点类型：抽水蓄能，常规水电，
     */
    @Schema(description = "站点类型")
    @ExcelProperty("站点类型")
    private Integer powerStationType;

    /**
     * 所属电网
     */
    @Schema(description = "所属电网")
    @ExcelProperty("所属电网")
    private String powerCode;

    /**
     * 国家名称：中国，其他
     */
    @Schema(description = "国家")
    @ExcelProperty("国家")
    private String country;

    /**
     * 省份
     */
    @Schema(description = "省")
    @ExcelProperty("省")
    private String province;

    /**
     * 市
     */
    @Schema(description = "市")
    @ExcelProperty("市")
    private String city;

    /**
     * 县
     */
    @Schema(description = "县")
    @ExcelProperty("县")
    private String county;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "县名称")
    private String countyName;

    /**
     * 详细位置
     */
    @Schema(description = "详细位置")
    @ExcelProperty("详细位置")
    private String address;

    /**
     * 工作深度：规划，预可，三专，可研，详图，已建
     */
    @Schema(description = "工作深度")
    @ExcelProperty("工作深度")
    private String designStage;

    /**
     * 上传文件名
     */
    @Schema(description = "上传文件名")
    @ExcelProperty("上传文件名")
    private String uploadFile;

    /**
     * 文件地址
     */
    @Schema(description = "文件地址")
    @ExcelProperty("文件地址")
    private String fileUrl;

    /**
     * 工作方式：纯蓄能，混合式
     */
    @Schema(description = "工作方式")
    @ExcelProperty("工作方式")
    private String developWay;


    /**
     * 是否纳规
     */
    @Schema(description = "是否纳规")
    @ExcelProperty("是否纳规")
    private String complianceRegulations;

    /**
     * 经度
     */
    @Schema(description = "经度")
    @ExcelProperty("经度")
    private String longitude;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    @ExcelProperty("纬度")
    private String latitude;

    /**
     * 总装机容量(MW)
     */
    @Schema(description = "总装机容量(MW)")
    @ExcelProperty("总装机容量(MW)")
    private String totalCapacity;

    /**
     * 连续满发小时数(h)
     */
    @Schema(description = "连续满发小时数(h)")
    @ExcelProperty("连续满发小时数(h)")
    private String fullShippingHours;

    /**
     * 额定水头
     */
    @Schema(description = "额定水头")
    @ExcelProperty("额定水头")
    private String ratedHead;

    /**
     * 距高比
     */
    @Schema(description = "距高比")
    @ExcelProperty("距高比")
    private String distanceToHeightRatio;

    /**
     * 单位千瓦静态投资(元/kW)
     */
    @Schema(description = "单位千瓦静态投资(元/kW)")
    @ExcelProperty("单位千瓦静态投资(元/kW)")
    private String staticInvestment;

    /**
     * 水源条件
     */
    @Schema(description = "水源条件")
    @ExcelProperty("水源条件")
    private String waterSourceConditions;

    /**
     * 重大敏感因素
     */
    @Schema(description = "重大敏感因素")
    @ExcelProperty("重大敏感因素")
    private String significantSensitiveFactors;

    /**
     * 设计单位
     */
    @Schema(description = "设计单位")
    @ExcelProperty("设计单位")
    private String designUnit;

    /**
     * 投资单位
     */
    @Schema(description = "投资单位")
    @ExcelProperty("投资单位")
    private String lnvestmentUnit;

    /**
     * 数据来源
     */
    @Schema(description = "数据来源")
    @ExcelProperty("数据来源")
    private String dataSources;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remarks;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}