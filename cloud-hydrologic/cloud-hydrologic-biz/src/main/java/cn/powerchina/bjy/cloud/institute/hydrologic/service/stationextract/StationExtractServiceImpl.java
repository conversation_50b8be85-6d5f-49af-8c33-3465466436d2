package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationextract;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationextract.vo.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationextract.StationExtractDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationextract.StationExtractMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.LatestEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.PlanningDesignConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnumV2;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.StationDataLogAddModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.DataProcessor;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.RedisService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationdatalog.StationDataLogService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.MyStringUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.RedisUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils.arrayValidate;


/**
 * 站点-摘录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class StationExtractServiceImpl implements StationExtractService {

    @Resource
    private RedisService redisService;
    @Resource
    private StationExtractMapper stationExtractMapper;
    @Resource
    private StationDataLogService stationDataLogService;

    @Override
    public Long createStationExtract(StationExtractSaveReqVO createReqVO) {
        // 插入
        StationExtractDO stationExtract = BeanUtils.toBean(createReqVO, StationExtractDO.class);
        stationExtractMapper.insert(stationExtract);
        // 返回
        return stationExtract.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStationExtract(StationExtractSaveReqVO updateReqVO) {
        //查询站点是否存在
        if (Objects.isNull(updateReqVO.getStationId())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        RLock lock = redisService.acquireDistributedLock(RedisUtils.getLockKey(updateReqVO.getStationId(), updateReqVO.getStationType(), updateReqVO.getDataType()));
        try {
            List<StationExtractSaveReqVO.StationExtractData> dataList = updateReqVO.getDataList();
            if (CollectionUtils.isEmpty(dataList) && CollectionUtils.isEmpty(updateReqVO.getDeleteIds())) {
                throw exception(ErrorCodeConstants.STATION_DATA_UPDATE_CONTENT_EMPTY_EXISTS_ERROR);
            }
            //校验日期格式
            if (CollectionUtil.isNotEmpty(dataList)) {
                dataList.forEach(item -> {
                    if (BooleanUtil.isFalse(DateUtils.isValidDate(item.getYear(), item.getMonth(), item.getDay()))) {
                        throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
                    }
                });
                List<Long> isd=dataList.stream().map(StationExtractSaveReqVO.StationExtractData::getId).collect(Collectors.toList());
                for(StationExtractSaveReqVO.StationExtractData item:dataList){
                    Long count=stationExtractMapper.selectCount(new LambdaQueryWrapperX<StationExtractDO>()
                            .notIn(StationExtractDO::getId, isd)
                            .eq(StationExtractDO::getStationId, updateReqVO.getStationId())
                            .eq(StationExtractDO::getYear, item.getYear())
                            .eq(StationExtractDO::getMonth, item.getMonth())
                            .eq(StationExtractDO::getStartHourMinute, item.getStartHourMinute())
                            .eq(StationExtractDO::getEndHourMinute, item.getEndHourMinute())
                    );
                    if(count>0){
                        throw exception(new ErrorCode(500,"数据重复！请重新填写！"));
                    }
                }
            }
            //数据处理器
            DataProcessor dataProcessor = (DataProcessor) SpringUtil.getBean(StationDataTypeEnumV2.fromCode(updateReqVO.getDataType()).getImportModel());
            try {
                dataProcessor.update(updateReqVO);
            } catch (ServiceException e) {
                throw e;
            } catch (Throwable e) {
                log.info("未知异常：", e);
                throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void deleteStationExtract(Long id) {
        // 校验存在
        validateStationExtractExists(id);
        // 删除
        stationExtractMapper.deleteById(id);
    }

    private void validateStationExtractExists(Long id) {
        if (stationExtractMapper.selectById(id) == null) {
//            throw exception(STATION_EXTRACT_NOT_EXISTS);
        }
    }

    @Override
    public StationExtractDO getStationExtract(Long id) {
        return stationExtractMapper.selectById(id);
    }

    @Override
    public PageResult<StationExtractDO> getStationExtractPage(StationExtractPageReqVO pageReqVO) {
        //如果查询更新记录logId不为空
        Long logId = pageReqVO.getLogId();
        if (Objects.nonNull(logId)) {
            StationDataLogDO stationDataLogDO = stationDataLogService.findById(pageReqVO.getLogId());
            if (Objects.isNull(stationDataLogDO)) {
                return PageResult.empty();
            }
            pageReqVO.setVersion(stationDataLogDO.getCurrentVersion());
        } else {
            pageReqVO.setLatest(LatestEnum.LATEST.getType());
        }
        return stationExtractMapper.selectPage(pageReqVO);
    }

    @Override
    public void importStationExtractExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) {
        RLock lock = redisService.acquireDistributedLock(RedisUtils.getLockKey(stationId, stationType, dataType));
        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }

            DataProcessor dataProcessor = (DataProcessor) SpringUtil.getBean(StationDataTypeEnumV2.fromCode(dataType).getImportModel());
            dataProcessor.importExcel(file, stationId, stationType, dataType);

        } catch (IOException e) {
            log.error("importStationRainExtractFeatureExcel--->error.", e);
        } finally {
            lock.unlock();
        }

    }

    /**
     * @param stationExtractList
     * @param importFlag
     * @desc 批量插入数据、更新历史数据
     * <AUTHOR>
     * @time 2024/8/20 09:02
     */
    @Override
    public void insertBatch(List<StationExtractDO> stationExtractList, List<Long> idList, boolean importFlag) {
        Integer preVersion;
        Integer nextVersion;

        List<StationExtractDO> insertList = new ArrayList<>();

        Map<String, List<StationExtractDO>> groupedByKey = stationExtractList.stream()
                .collect(Collectors.groupingBy(this::gainIndexKey
                ));
        Set<String> keySet = groupedByKey.keySet();

        StationExtractDO stationExtract;

        if (CollectionUtil.isNotEmpty(stationExtractList)) {
            //修改的数据
            insertList.addAll(stationExtractList);
            stationExtract = stationExtractList.get(0);
            preVersion = stationExtract.getVersion() - 1;
            nextVersion = stationExtract.getVersion();
        } else if (CollectionUtil.isNotEmpty(idList)) {
            Long id = idList.get(0);
            //历史数据
            stationExtract = stationExtractMapper.selectById(id);
            preVersion = stationExtract.getVersion();
            nextVersion = stationExtract.getVersion() + 1;
        } else {
            return;
        }
        //更新当前数据为历史版本
        Long stationId = stationExtract.getStationId();
        Integer stationType = stationExtract.getStationType();
        Integer dataType = stationExtract.getDataType();


        stationExtractMapper.updateHistoryVersion(stationId, stationType, dataType);


        //判断当前版本
        if (stationExtract.getVersion() >= 1) {
            //获取上一个版本的数据集
            List<StationExtractDO> preVersionStationExtractDOList = findStationExtractDOByVersion(stationId, stationType, dataType, preVersion);
            if (CollectionUtil.isNotEmpty(preVersionStationExtractDOList)) {

                String loginUserId = WebFrameworkUtils.getLoginUserId().toString();

                if (importFlag) {
                    List<StationExtractDO> stationExtractDOS = preVersionStationExtractDOList.stream()
                            .filter(item -> !keySet.contains(gainIndexKey(item)))
                            .peek(item -> {
                                item.setVersion(item.getVersion() + 1);
                                item.setLatest(LatestEnum.LATEST.getType());
                                item.setId(null);
                                item.setCreator(loginUserId);
                                item.setUpdater(loginUserId);
                            }).toList();
                    //插入的数据分为：本次修改的、本次未修改的
                    //该部分为：本次未修改的部分
                    insertList.addAll(stationExtractDOS);
                } else {
                    //挑选出本次没有未修改的数据，作为最新版本数据
                    List<StationExtractDO> stationExtractDOS = preVersionStationExtractDOList.stream().filter(item -> (!idList.contains(item.getId())))
                            .peek(item -> {
                                item.setVersion(nextVersion);
                                item.setLatest(LatestEnum.LATEST.getType());
                                item.setId(null);
                                item.setCreator(loginUserId);
                                item.setUpdater(loginUserId);
                            }).toList();

                    //插入的数据分为：本次修改的、本次未修改的
                    //该部分为：本次未修改的部分
                    insertList.addAll(stationExtractDOS);
                }
            }
        }
        //插入新数据
        if (CollectionUtil.isNotEmpty(insertList)) {
            stationExtractMapper.batchInsert(insertList);
        }

        //插入变更记录
        stationDataLogService.addStationDataLog(
                StationDataLogAddModel.builder()
                        .stationType(stationType)
                        .stationId(stationId)
                        .dataType(dataType)
                        .currentVersion(preVersion)
                        .build());
    }


    /**
     * @param stationExtractList
     * @param updateReqVO
     * @param importFlag
     * @desc 批量插入数据、更新历史数据
     * <AUTHOR>
     * @time 2024/8/20 09:02
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBatchRain(List<StationExtractDO> stationExtractList, StationExtractSaveReqVO updateReqVO, List<Long> idList, boolean importFlag) {

        List<StationExtractDO> insertList = new ArrayList<>();


        //更新当前数据为历史版本
        Long stationId = updateReqVO.getStationId();
        Integer stationType = updateReqVO.getStationType();
        Integer dataType = updateReqVO.getDataType();


        List<StationExtractDO> listCurrent = listCurrent(stationId, dataType);
        stationExtractMapper.updateHistoryVersion(stationId, stationType, dataType);

        Integer nextVersion = stationDataLogService.getNextVersionV2(stationId, dataType);

        //判断当前版本

        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();

        //导入：数据需要融合
        if (importFlag) {
            Map<String, List<StationExtractDO>> groupedByKey = stationExtractList.stream()
                    .collect(Collectors.groupingBy(this::gainIndexRainKey
                    ));
            Set<String> keySet = groupedByKey.keySet();


            if (CollectionUtil.isNotEmpty(listCurrent)) {
                List<StationExtractDO> list = listCurrent.stream()
                        .filter(item -> !keySet.contains(gainIndexRainKey(item)))
                        .peek(item -> {
                            item.setLatest(LatestEnum.LATEST.getType());
                            item.setId(null);
                            item.setCreator(loginUserId);
                            item.setUpdater(loginUserId);
                        }).toList();
                if (CollectionUtil.isNotEmpty(list)) {
                    insertList.addAll(list);
                }
            }
            insertList.addAll(stationExtractList);

        }
        //更新
        else {
            listCurrent = listCurrent.stream()
                    .filter(item -> !idList.contains(item.getId()))
                    .peek(item -> {
                        item.setLatest(LatestEnum.LATEST.getType());
                        item.setId(null);
                        item.setCreator(loginUserId);
                        item.setUpdater(loginUserId);
                    }).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(listCurrent)) {
                insertList.addAll(listCurrent);
            }
            insertList.addAll(stationExtractList);
        }

        //插入新数据
        if (CollectionUtil.isNotEmpty(insertList)) {

            insertList = sortAndhourMinuteCheck(insertList);

            insertList.forEach(item -> item.setVersion(nextVersion));
            stationExtractMapper.batchInsert(insertList);
        }
        //插入变更记录
        stationDataLogService.addStationDataLog(
                StationDataLogAddModel.builder()
                        .stationType(stationType)
                        .stationId(stationId)
                        .dataType(dataType)
                        .currentVersion(nextVersion - 1)
                        .build());

//        List<StationExtractDO> insertList = new ArrayList<>();
//
//        //更新当前数据为历史版本
//        Long stationId = updateReqVO.getStationId();
//        Integer stationType = updateReqVO.getStationType();
//        Integer dataType = updateReqVO.getDataType();
//
//        List<StationExtractDO> listCurrent = listCurrent(stationId, dataType);
//
//        Integer nextVersion = stationDataLogService.getNextVersionV2(stationId, dataType);
//
//        //判断当前版本
//        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();
//
//        //导入：数据需要融合
//        if (importFlag) {
//            Map<String, List<StationExtractDO>> groupedByKey = stationExtractList.stream()
//                    .collect(Collectors.groupingBy(this::gainIndexRainKey
//                    ));
//            Set<String> keySet = groupedByKey.keySet();
//
//            if (CollectionUtil.isNotEmpty(listCurrent)) {
//                List<StationExtractDO> list = listCurrent.stream()
//                        .filter(item -> !keySet.contains(gainIndexRainKey(item)))
//                        .peek(item -> {
//                            item.setLatest(LatestEnum.LATEST.getType());
//                            item.setId(null);
//                            item.setCreator(loginUserId);
//                            item.setUpdater(loginUserId);
//                        }).toList();
//                if (CollectionUtil.isNotEmpty(list)) {
//                    insertList.addAll(list);
//                }
//            }
//            insertList.addAll(stationExtractList);
//        }
//        //更新
//        else {
//            if (CollectionUtil.isNotEmpty(idList)) {
//                // 首先，记录要删除的ID
//                Set<Long> deleteIdSet = new HashSet<>(idList);
//
//                // 对当前列表进行过滤，确保要删除的记录被清除
//                listCurrent = listCurrent.stream()
//                        .filter(item -> {
//                            Long id = item.getId();
//                            return id != null && !deleteIdSet.contains(id);
//                        })
//                        .peek(item -> {
//                            item.setLatest(LatestEnum.LATEST.getType());
//                            item.setId(null);
//                            item.setCreator(loginUserId);
//                            item.setUpdater(loginUserId);
//                        }).collect(Collectors.toList());
//
//                // 增加日志记录，辅助调试
//                log.info("删除记录处理完成，待删除ID: {}, 过滤后剩余记录数: {}", deleteIdSet, listCurrent.size());
//            } else {
//                // 如果没有要删除的ID，则处理方式不变
//                listCurrent = listCurrent.stream()
//                        .peek(item -> {
//                            item.setLatest(LatestEnum.LATEST.getType());
//                            item.setId(null);
//                            item.setCreator(loginUserId);
//                            item.setUpdater(loginUserId);
//                        }).collect(Collectors.toList());
//            }
//
//            if (CollectionUtil.isNotEmpty(listCurrent)) {
//                insertList.addAll(listCurrent);
//            }
//            insertList.addAll(stationExtractList);
//        }
//        int size = updateReqVO.getDeleteIds().size();
//        stationExtractMapper.updateHistoryVersion(stationId, stationType, dataType);
//        //插入新数据
//        if (CollectionUtil.isNotEmpty(insertList)) {
//            insertList = sortAndhourMinuteCheck(insertList);
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
////            stationExtractMapper.updateHistoryVersion(stationId, stationType, dataType);
//            insertList.forEach(item -> item.setVersion(nextVersion));
//            stationExtractMapper.batchInsert(insertList);
//        }
//
//        //插入变更记录
//        stationDataLogService.addStationDataLog(
//                StationDataLogAddModel.builder()
//                        .stationType(stationType)
//                        .stationId(stationId)
//                        .dataType(dataType)
//                        .currentVersion(nextVersion - 1)
//                        .build());
    }

    private List<StationExtractDO> sortAndhourMinuteCheck(List<StationExtractDO> insertList) {

        List<StationExtractRainTimeStampVO> extractRainList = BeanUtils.toBean(insertList, StationExtractRainTimeStampVO.class);
        extractRainList = extractRainList.stream()
                .peek(item -> item.setStartTimestamp(generateStartTimestamp(item)))
                .sorted(Comparator.comparing(StationExtractRainTimeStampVO::getStartTimestamp))
                .collect(Collectors.toList());

        //数据校验
        List<String> repeatStr = new ArrayList<>();
        List<long[]> timestampArrays = new ArrayList<>();
        if (CollectionUtil.isEmpty(extractRainList)) {
            return BeanUtils.toBean(extractRainList, StationExtractDO.class);
        }
        for (int i = 0; i < insertList.size(); i++) {
            StationExtractRainTimeStampVO importModel = extractRainList.get(i);
            MyStringUtils.checkYearMonthDayStartHoursMinuteEndHoursMinute(String.valueOf(importModel.getYear()), String.valueOf(importModel.getMonth()), String.valueOf(importModel.getDay()), importModel.getStartHourMinute(), importModel.getEndHourMinute(), +3, false);
            String startHourMinute = importModel.getStartHourMinute();
            String correctedStartHourMinute = startHourMinute.contains(":") ? startHourMinute : startHourMinute + ":00";
            String endHourMinute = importModel.getEndHourMinute();
            String correctedEndHourMinute = endHourMinute.contains(":") ? endHourMinute : endHourMinute + ":00";
            if (correctedStartHourMinute.length() == 4) {
                correctedStartHourMinute = "0" + correctedStartHourMinute;
            }
            if (correctedEndHourMinute.length() == 4) {
                correctedEndHourMinute = "0" + correctedEndHourMinute;
            }
            String key = importModel.getYear() + "-" + importModel.getMonth() + "-" + importModel.getDay() + "-" + correctedStartHourMinute + "-" + correctedEndHourMinute;
            if (repeatStr.contains(key)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_EXTRACT_DAY_REPEAT_EXISTS_ERROR, importModel.getYear(), importModel.getMonth(), importModel.getDay());
            }
            importModel.setStartHourMinute(correctedStartHourMinute);
            importModel.setEndHourMinute(correctedEndHourMinute);
            extractRainList.set(i, importModel);
            int startHour = Integer.parseInt(correctedStartHourMinute.substring(0, 2));
            int startMinute = Integer.parseInt(correctedStartHourMinute.substring(3, 5));
            LocalDateTime startDateTime;
            if (startHour == 24 && startMinute == 0) {
                startDateTime = LocalDateTime.of(
                        importModel.getYear(),
                        importModel.getMonth(),
                        importModel.getDay() + 1,
                        0,
                        0
                );
            } else {
                startDateTime = LocalDateTime.of(
                        importModel.getYear(),
                        importModel.getMonth(),
                        importModel.getDay(),
                        startHour,
                        startMinute
                );
            }
//            // 特殊处理 24:00
//            int endHour = Integer.parseInt(correctedEndHourMinute.substring(0, 2));
//            int endMinute = Integer.parseInt(correctedEndHourMinute.substring(3, 5));
//            LocalDateTime endDateTime;
//            if (endHour == 24 && endMinute == 0) {
//                endDateTime = LocalDateTime.of(
//                        importModel.getYear(),
//                        importModel.getMonth(),
//                        importModel.getDay() + 1,
//                        0,
//                        0
//                );
//            } else {
//                endDateTime = LocalDateTime.of(
//                        importModel.getYear(),
//                        importModel.getMonth(),
//                        importModel.getDay(),
//                        endHour,
//                        endMinute
//                );
//            }
            // 特殊处理 24:00
            int endHour = Integer.parseInt(correctedEndHourMinute.substring(0, 2));
            int endMinute = Integer.parseInt(correctedEndHourMinute.substring(3, 5));
            LocalDateTime endDateTime;
            if (endHour == 24 && endMinute == 0) {
                // 使用 plusDays 方法正确处理跨天的情况，尤其是月底
                LocalDateTime baseDateTime = LocalDateTime.of(
                        importModel.getYear(),
                        importModel.getMonth(),
                        importModel.getDay(),
                        0,
                        0
                );
                endDateTime = baseDateTime.plusDays(1);
            } else {
                endDateTime = LocalDateTime.of(
                        importModel.getYear(),
                        importModel.getMonth(),
                        importModel.getDay(),
                        endHour,
                        endMinute
                );
            }
            if (startDateTime.isAfter(endDateTime)) {
                endDateTime = endDateTime.plusDays(1);
            }
            long startTimestamp = startDateTime.toEpochSecond(ZoneOffset.UTC);
            long endTimestamp = endDateTime.toEpochSecond(ZoneOffset.UTC);
            // 将 startTimestamp 和 endTimestamp 组成一个一维数组
            long[] timestamps = {startTimestamp, endTimestamp};

            // 添加到二维数组
            timestampArrays.add(timestamps);
            repeatStr.add(key);
        }
        long[][] allTimestamps = timestampArrays.toArray(new long[0][]);
        try {
//            arrayValidate(allTimestamps);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.DATA_FORMAT_ERROR);
        }
        return BeanUtils.toBean(extractRainList, StationExtractDO.class);
    }

    private long generateStartTimestamp(StationExtractRainTimeStampVO item) {
        MyStringUtils.checkYearMonthDayStartHoursMinuteEndHoursMinute(String.valueOf(item.getYear()), String.valueOf(item.getMonth()), String.valueOf(item.getDay()), item.getStartHourMinute(), item.getEndHourMinute(), +3, false);
        String startHourMinute = item.getStartHourMinute();
        String correctedStartHourMinute = startHourMinute.contains(":") ? startHourMinute : startHourMinute + ":00";
        if (correctedStartHourMinute.length() == 4) {
            correctedStartHourMinute = "0" + correctedStartHourMinute;
        }
        int year = item.getYear();
        int month = item.getMonth();
        int day = item.getDay();
        int hour = Integer.parseInt(correctedStartHourMinute.substring(0, 2));
        int minute = Integer.parseInt(correctedStartHourMinute.substring(3, 5));

        LocalDateTime startDateTime = LocalDateTime.of(year, month, day, hour, minute);
        return startDateTime.toEpochSecond(ZoneOffset.UTC);
    }

    @NotNull
    private String gainIndexKey(StationExtractDO item) {
        return item.getStationId() + "" + item.getStationType() + "" + item.getDataType() + "" + item.getYear() + "" + item.getMonth() + "" + item.getDay() + "" + item.getHoursMinute() + "" + item.getStartHourMinute() + "" + item.getEndHourMinute();
    }

    @NotNull
    private String gainIndexRainKey(StationExtractDO item) {
        return item.getStationId() + "" + item.getStationType() + "" + item.getDataType() + "" + item.getYear() + "" + item.getMonth() + "" + item.getDay() + "" + item.getStartHourMinute() + "" + item.getEndHourMinute();
    }

    @Override
    public List<StationExtractDO> findStationExtractDOByVersion(Long stationId, Integer stationType, Integer dataType, int version) {
        Map<String, Object> map = new HashMap<>(4);
        map.put("station_id", stationId);
        map.put("station_type", stationType);
        map.put("data_type", dataType);
        map.put("version", version);
        return stationExtractMapper.selectByMap(map);
    }

    @Override
    public void exportStationExtractExcel(StationExtractPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        StationDataTypeEnumV2 dataTypeEnum = StationDataTypeEnumV2.fromCode(pageReqVO.getDataType());
        if (Objects.isNull(dataTypeEnum)) {
            throw new ServiceException(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase());
        }
        String fileName = URLEncoder.encode(dataTypeEnum.getDescription() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        List<StationExtractDO> list = this.getStationExtractPage(pageReqVO).getList();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

//        List<StationExtractRespVO> stationExtractRespVOS = BeanUtils.toBean(list, StationExtractRespVO.class);
//        ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + dataTypeEnum.getCode() + PlanningDesignConstants.EXCEL_SUFFIX);
//
//        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
//                // 利用模板的输出流
//                .withTemplate(resource.getInputStream())
//                .build();
//        WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
//        excelWriter.fill(stationExtractRespVOS, writeSheet);
//        excelWriter.finish();
        // 根据数据类型选择不同的VO类进行转换
        if (isWaterReservoirType(dataTypeEnum.getCode())) {
            // 水库水文使用新的VO类
            List<StationWaterExtractRespVO> waterExtractRespVOS = BeanUtils.toBean(list, StationWaterExtractRespVO.class);
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + dataTypeEnum.getCode() + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream())
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            excelWriter.fill(waterExtractRespVOS, writeSheet);
            excelWriter.finish();
        } else {
            // 洪水水文使用原有的VO类
            List<StationExtractRespVO> stationExtractRespVOS = BeanUtils.toBean(list, StationExtractRespVO.class);
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + dataTypeEnum.getCode() + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream())
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0).build();
            excelWriter.fill(stationExtractRespVOS, writeSheet);
            excelWriter.finish();
        }
    }
    /**
     * 判断是否为水库水文类型
     */
    private boolean isWaterReservoirType(Integer dataType) {
        // 这里需要根据实际的水库水文数据类型进行判断
        return dataType.equals(StationDataTypeEnumV2.HYDROLOGIC_WATER_HYDROLOGICAL_ELEMENTS_EXCERPT.getCode()); // 假设这是水库水文的类型码
    }

    @Override
    public Integer findNextStationExtractVersion(Long stationId, Integer stationType, Integer dataType) {
        Integer version = stationExtractMapper.selectMaxVersion(stationId, stationType, dataType);
        return Objects.isNull(version) ? 1 : (version + 1);
    }

    @Override
    public List<StationExtractDO> listByStationIds(List<Long> stationIds) {
        return stationExtractMapper.listByStationIds(stationIds);
    }

    @Override
    public void insertList(List<StationExtractDO> stationExtractDOS) {
        stationExtractMapper.insertList(stationExtractDOS);
    }

    @Override
    public StationExtractDO selectOne(Long stationId, Integer dataType, Integer year, Integer month, Integer day, String hoursMinute) {
        QueryWrapper<StationExtractDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("station_id", stationId);
        queryWrapper.eq("data_type", dataType);
        queryWrapper.eq("year", year);
        queryWrapper.eq("month", month);
        queryWrapper.eq("day", day);
        queryWrapper.eq("hours_minute", hoursMinute);
        queryWrapper.eq("latest", 1);
        return stationExtractMapper.selectOne(queryWrapper, false);
    }

    @Override
    public StationExtractDO selectOneRain(Long stationId, Integer dataType, Integer year, Integer month, Integer day, String startHourMinute, String endHourMinute) {
        QueryWrapper<StationExtractDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("station_id", stationId);
        queryWrapper.eq("data_type", dataType);
        queryWrapper.eq("year", year);
        queryWrapper.eq("month", month);
        queryWrapper.eq("day", day);
        queryWrapper.eq("start_hour_minute", startHourMinute);
        queryWrapper.eq("end_hour_minute", endHourMinute);
        queryWrapper.eq("latest", 1);
        return stationExtractMapper.selectOne(queryWrapper, false);
    }

    @Override
    public List<StationExtractDO> selectListTemporaryData(Long stationId) {
        return stationExtractMapper.selectListTemporaryData(stationId);
    }

    @Override
    public Integer getLatestVersion(Long stationId, Integer dataType) {
        return stationExtractMapper.getLatestVersion(stationId, dataType);
    }

    @Override
    public void batchInsert(List<StationExtractDO> list, int batchSize) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        int totalSize = list.size();
        for (int start = 0; start < totalSize; start += batchSize) {
            int end = Math.min(start + batchSize, totalSize);
            List<StationExtractDO> batch = list.subList(start, end);
            stationExtractMapper.batchInsert(batch);
        }
    }

    @Override
    public void updateBatchById(List<StationExtractDO> updateList, int batchSize) {
        int totalSize = updateList.size();
        for (int start = 0; start < totalSize; start += batchSize) {
            int end = Math.min(start + batchSize, totalSize);
            List<StationExtractDO> batch = updateList.subList(start, end);
            stationExtractMapper.updateBatchById(batch);
        }
    }

    @Override
    public List<StationExtractDO> listCurrent(Long stationId, Integer dataType) {
        LambdaQueryWrapper<StationExtractDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StationExtractDO::getStationId, stationId);
        queryWrapper.eq(StationExtractDO::getDataType, dataType);
        queryWrapper.eq(StationExtractDO::getLatest, LatestEnum.LATEST.getType());
        return stationExtractMapper.selectList(queryWrapper);
    }

}