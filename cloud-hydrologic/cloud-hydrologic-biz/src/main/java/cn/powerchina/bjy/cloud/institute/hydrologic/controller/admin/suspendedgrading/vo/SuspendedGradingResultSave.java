package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedgrading.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class SuspendedGradingResultSave {

    @Schema(description = "站点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16047")
    @NotNull(message = "站点ID不能为空")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "站点类型不能为空")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;

    @Schema(description = "年份(4位数字)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "年份(4位数字)不能为空")
    private Integer year;

    @Schema(description = "是否删除 true是 ")
    private Boolean isDelete;

    private List<SuspendedGradingResultSaveReqVO> dataList;

}
