package cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sixparamhnqx6yscs2;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 六要素观测成果表(南通惠能HNqx6ys-cs2)
 *
 * <AUTHOR>
 */
@TableName("hydrologic_six_param_HNqx6yscs2")
@KeySequence("hydrologic_six_param_HNqx6yscs2_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = false)
public class SixParamHNqx6yscs2DO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 站点id
     */
    private Long stationId;

    /**
     * 设备地址
     */
    private String deviceAddress;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 风力(级)
     */
    private String windForce;

    /**
     * 风速(m/s)
     */
    private String windSpeed;

    /**
     * 风向(方向)
     */
    private String windDirection;

    /**
     * 空气温度(℃)
     */
    private String airTemperature;

    /**
     * 空气湿度(%)
     */
    private String airHumidity;

    /**
     * 大气压(Kpa)
     */
    private String atmosphericPressure;

    /**
     * 累计雨量(mm)
     */
    private String cumulativeRainfall;

    /**
     * 瞬时雨量(mm)
     */
    private String instantaneousRainfall;

    /**
     * 当前雨量(mm)
     */
    private String currentRainfall;

    /**
     * 日雨量(mm)
     */
    private String dailyRainfall;

    /**
     * 记录时间
     */
    private LocalDateTime recordTime;
}