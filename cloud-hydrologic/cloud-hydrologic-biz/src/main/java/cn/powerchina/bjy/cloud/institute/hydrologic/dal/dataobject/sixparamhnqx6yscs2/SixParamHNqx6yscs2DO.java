package cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.resourcesitemanagement;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 项目管理 DO
 *
 * <AUTHOR>
 */
@TableName("hydrologic_resource_site_management")
@KeySequence("hydrologic_resource_site_management_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = false)
public class ResourceSiteManagementDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 站址名称
     */
    private String powerStationName;

    /**
     * 站点类型：抽水蓄能，常规水电，混合式抽蓄
     */
    private Integer powerStationType;

    /**
     * 所属电网 -(所在图纸编号)
     */
    private String powerCode;

    /**
     * 国家名称：中国，其他
     */
    private String country;

    /**
     * 省
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String province;

    /**
     * 市
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String city;

    /**
     * 县
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String county;

    /**
     * 详细地址
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    @ExcelProperty("address")
    private String address;

    /**
     * 是否纳规
     */
    private String complianceRegulations;

    /**
     * 工作深度：规划，预可，三专，可研，详图，已建
     */
    private String designStage;

    /**
     * 上传文件名
     */
    private String uploadFile;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 开发方式：纯蓄能，混合式
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String developWay; // ，1-纯蓄能，2-混合式


    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 总装机容量(MW)
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String totalCapacity;

    /**
     * 连续满发小时数(h)
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String fullShippingHours;

    /**
     * 额定水头
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ratedHead;

    /**
     * 距高比
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String distanceToHeightRatio;

    /**
     * 单位千瓦静态投资(元/kW)
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String staticInvestment;

    /**
     * 水源条件
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String waterSourceConditions;

    /**
     * 重大敏感因素
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String significantSensitiveFactors;

    /**
     * 设计单位
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String designUnit;

    /**
     * 投资单位
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String lnvestmentUnit;

    /**
     * 数据来源
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String dataSources;

    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remarks;


}