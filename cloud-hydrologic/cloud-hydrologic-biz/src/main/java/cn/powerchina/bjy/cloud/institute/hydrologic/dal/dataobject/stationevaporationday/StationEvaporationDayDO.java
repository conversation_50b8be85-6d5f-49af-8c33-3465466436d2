package cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationevaporationday;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 蒸发-逐日水面蒸发量 DO
 *
 * <AUTHOR>
 */
@TableName("hydrologic_station_evaporation_day")
@KeySequence("hydrologic_station_evaporation_day_seq")
// 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StationEvaporationDayDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 水文/雨量站id
     */
    private Long stationId;
    /**
     * 站点类型，1：雨量站，2：水文站
     */
    private Integer stationType;
    /**
     * 年
     */
    private Integer year;
    /**
     * 月
     */
    private Integer month;
    /**
     * 日
     */
    private Integer day;

    /**
     * 记录日期
     */
    private Date currentDay;
    /**
     * 蒸发量(mm)
     */
    private String value;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本（根据原型待定）
     */
    private Integer version;
    /**
     * 最新版本（1：最新，0：历史版本，默认为1）
     */
    private Integer latest;

}