package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainexcerpt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 降水-摘录新增/修改 Request VO")
@Data
public class StationRainExcerptSaveReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationRainExcerptData> dataList;

    @Schema(description = "管理后台 - 降水-摘录修改 Request VO")
    @Data
    public static class StationRainExcerptData {
        
        @Schema(description = "年")
        private String year;

        @Schema(description = "月")
        private String month;

        @Schema(description = "日")
        private String day;

        @Schema(description = "降水量")
        private String value;

        @Schema(description = "起	时:分")
        private String startHourMinute;

        @Schema(description = "止	时:分")
        private String endHourMinute;
    }

}