package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainyear.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 降水-年降水量 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationRainYearRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "年降水量(mm)")
    @ExcelProperty("年降水量(mm)")
    private String value;

    @Schema(description = "年降水日数")
    @ExcelProperty("年降水日数")
    private Integer valueDays;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}