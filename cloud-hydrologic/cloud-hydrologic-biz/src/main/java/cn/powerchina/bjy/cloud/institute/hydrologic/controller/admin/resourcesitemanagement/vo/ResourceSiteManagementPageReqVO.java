package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

@Schema(description = "管理后台 - 资源站点管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ResourceSiteManagementPageReqVO extends PageParam {

    /**
     * 站点名称
     */
    @Schema(description = "站点名称")
    private String powerStationName;


    /**
     * 站点类型：抽水蓄能，常规水电，混合式抽蓄
     */
    @Schema(description = "站点类型")
    private Integer powerStationType;

    /**
     * 所属电网-所在图纸编号
     */
    @Schema(description = "所属电网")
    private String powerCode;

    /**
     * 国家名称：中国，其他
     */
    @Schema(description = "国家名称")
    private String country;

    /**
     * 省份
     */
    @Schema(description = "省")
    private String province;

    /**
     * 市
     */
    @Schema(description = "市")
    private String city;

    /**
     * 县
     */
    @Schema(description = "县")
    private String county;

    /**
     * 详细位置
     */
    @Schema(description = "详细位置")
    @ExcelProperty("详细位置")
    private String address;

    /**
     * 工作深度：规划，预可，三专，可研，详图，已建
     */
    @Schema(description = "工作深度")
    private String designStage;

    /**
     * 上传文件名
     */
    @Schema(description = "上传文件名")
    private String uploadFile;

    /**
     * 文件地址
     */
    @Schema(description = "文件地址")
    private String fileUrl;

    /**
     * 是否纳规
     */
    @Schema(description = "是否纳规")
    @ExcelProperty("是否纳规")
    private String complianceRegulations;

    /**
     * 工作方式：纯蓄能，混合式
     */
    @Schema(description = "工作方式")
    private String developWay;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private String latitude;

    /**
     * 总装机容量(MW)
     */
    @Schema(description = "总装机容量(MW)")
    private String totalCapacity;

    /**
     * 总装机容量(MW) - 最小值
     */
    @Schema(description = "总装机容量(MW) - 最小值")
    @Pattern(regexp = "^$|-?\\d+(\\.\\d+)?$", message = "装机容量必须为有效数字")
    private String lowerLimit;

    /**
     * 总装机容量(MW) - 最小值（数值类型）
     */
    private Double lowerLimitValue;

    /**
     * 总装机容量(MW) - 最大值
     */
    @Schema(description = "总装机容量(MW) - 最大值")
    @Pattern(regexp = "^$|-?\\d+(\\.\\d+)?$", message = "装机容量必须为有效数字")
    private String upperLimit;

    /**
     * 总装机容量(MW) - 最大值(数值类型)
     */
    private Double upperLimitValue;

    /**
     * 连续满发小时数(h)
     */
    @Schema(description = "连续满发小时数(h)")
    private String fullShippingHours;

    /**
     * 额定水头
     */
    @Schema(description = "额定水头")
    private String ratedHead;

    /**
     * 距高比
     */
    @Schema(description = "距高比")
    private String distanceToHeightRatio;

    /**
     * 单位千瓦静态投资(元/kW)
     */
    @Schema(description = "单位千瓦静态投资(元/kW)")
    private String staticInvestment;

    /**
     * 水源条件
     */
    @Schema(description = "水源条件")
    private String waterSourceConditions;

    /**
     * 重大敏感因素
     */
    @Schema(description = "重大敏感因素")
    private String significantSensitiveFactors;

    /**
     * 设计单位
     */
    @Schema(description = "设计单位")
    private String designUnit;

    /**
     * 投资单位
     */
    @Schema(description = "投资单位")
    private String lnvestmentUnit;

    /**
     * 数据来源
     */
    @Schema(description = "数据来源")
    private String dataSources;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remarks;

    /**
     * 角色权限-适用项目配置的项目集合
     */
    private Set<Long> bizIdSet;


    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;
}