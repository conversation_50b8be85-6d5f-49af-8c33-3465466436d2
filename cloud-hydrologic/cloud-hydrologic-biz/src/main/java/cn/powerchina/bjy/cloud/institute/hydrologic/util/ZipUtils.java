package cn.powerchina.bjy.cloud.institute.hydrologic.util;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @desc 文件打包工具类
 * @time 2024/8/29 19:13
 */
public class ZipUtils {

    /**
     * @param sourceFolderPath 目标文件夹
     * @param zipFilePath      压缩之后的文件名
     * @param fileName         解压的文件夹名称
     * @desc 压缩文件夹
     * <AUTHOR>
     * @time 2024/9/2 15:24
     */
    public static void zipFolder(String sourceFolderPath, String zipFilePath, String fileName) throws IOException {
        File folderToZip = new File(sourceFolderPath);
        try (FileOutputStream fos = new FileOutputStream(zipFilePath);
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            zipFile(folderToZip, fileName, zos);
        }
    }

    private static void zipFile(File fileToZip, String fileName, ZipOutputStream zos) throws IOException {
        if (fileToZip.isHidden()) {
            return;
        }
        if (fileToZip.isDirectory()) {
            for (File childFile : fileToZip.listFiles()) {
                zipFile(childFile, fileName + File.separator + childFile.getName(), zos);
            }
            return;
        }
        try (FileInputStream fis = new FileInputStream(fileToZip)) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zos.putNextEntry(zipEntry);
            byte[] bytesBuffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(bytesBuffer)) != -1) {
                zos.write(bytesBuffer, 0, bytesRead);
            }
            zos.closeEntry();
        }
    }

    /**
     * 解压 ZIP 文件到指定目录
     *
     * @param zipFilePath  ZIP 文件路径
     * @param destDirectory 解压目标目录
     * @throws IOException 如果发生 I/O 错误
     */
    public static void unzip(String zipFilePath, String destDirectory) throws IOException {
        File destDir = new File(destDirectory);
        if (!destDir.exists()) {
            destDir.mkdirs(); // 创建目录结构
        }

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry zipEntry;
            while ((zipEntry = zis.getNextEntry()) != null) {
                File newFile = newFile(destDir, zipEntry);
                if (zipEntry.isDirectory()) {
                    if (!newFile.isDirectory() && !newFile.mkdirs()) {
                        throw new IOException("Failed to create directory " + newFile);
                    }
                } else {
                    // Ensure parent directories exist
                    File parent = newFile.getParentFile();
                    if (!parent.isDirectory() && !parent.mkdirs()) {
                        throw new IOException("Failed to create directory " + parent);
                    }
                    // Write file content
                    try (OutputStream fos = new FileOutputStream(newFile)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
                zis.closeEntry();
            }
        }
    }

    /**
     * 生成解压后的文件对象
     *
     * @param destinationDir 目标目录
     * @param zipEntry ZIP 文件条目
     * @return 解压后的文件对象
     * @throws IOException 如果生成文件路径出错
     */
    private static File newFile(File destinationDir, ZipEntry zipEntry) throws IOException {
        File destFile = new File(destinationDir, zipEntry.getName());
        String destDirPath = destinationDir.getCanonicalPath();
        String destFilePath = destFile.getCanonicalPath();

        if (!destFilePath.startsWith(destDirPath + File.separator)) {
            throw new IOException("Entry is outside of the target dir: " + zipEntry.getName());
        }

        return destFile;
    }

    /**
     * 文件加密
     */
    public static void encryptFile(String inputFile, String outputFile, String key) throws Exception {
        // 创建密钥
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);

        try (FileInputStream fis = new FileInputStream(inputFile);
             FileOutputStream fos = new FileOutputStream(outputFile);
             CipherOutputStream cos = new CipherOutputStream(fos, cipher)) {

            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) > 0) {
                cos.write(buffer, 0, len);
            }
        }
    }

    public static void decryptFile(String inputFile, String outputFile, String key) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);

        try (FileInputStream fis = new FileInputStream(inputFile);
             FileOutputStream fos = new FileOutputStream(outputFile);
             CipherInputStream cis = new CipherInputStream(fis, cipher)) {

            byte[] buffer = new byte[1024];
            int len;
            while ((len = cis.read(buffer)) > 0) {
                fos.write(buffer, 0, len);
            }
        }
    }
}
