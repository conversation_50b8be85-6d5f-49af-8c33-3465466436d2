package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationmonth.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 蒸发-月水面蒸发量特征值 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationEvaporationMonthRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private String month;

    @Schema(description = "月平均水面蒸发量(mm)")
    @ExcelProperty("月平均水面蒸发量(mm)")
    private String averageValue;

    @Schema(description = "月平均水面蒸发量备注")
    @ExcelProperty("月平均水面蒸发量备注")
    private String averageValueRemark;

    @Schema(description = "最大日水面蒸发量(mm)")
    @ExcelProperty("最大日水面蒸发量(mm)")
    private String maxValue;

    @Schema(description = "最大日水面蒸发量备注")
    @ExcelProperty("最大日水面蒸发量备注")
    private String maxValueRemark;

    @Schema(description = "最小日水面蒸发量(mm)")
    @ExcelProperty("最小日水面蒸发量(mm)")
    private String minValue;

    @Schema(description = "最小日水面蒸发量备注")
    @ExcelProperty("最小日水面蒸发量备注")
    private String minValueRemark;

}