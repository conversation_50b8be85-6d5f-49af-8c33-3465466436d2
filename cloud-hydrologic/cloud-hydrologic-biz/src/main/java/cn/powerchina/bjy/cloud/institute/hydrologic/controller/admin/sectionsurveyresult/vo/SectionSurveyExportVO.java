package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class SectionSurveyExportVO {
    @Schema(description = "站点id", required = true)
    private Long stationId;

    @Schema(description = "数据类型", required = true)
    private Integer dataType;

    @Schema(description = "站点类型", required = true)
    private Integer stationType;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "基本信息表附注")
    private String remark;

    private Long logId;
    // 基本信息
    private String year;  // 施测年份
    private String surveyDate;  // 施测日期
    private String sectionName;  // 断面名称及位置
    private String waterLevel;  // 测时水位

    // 垂线数据列表
    private List<VerticalLineData> dataList;

    @Data
    public static class VerticalLineData {
        private String vertical_no;  // 垂线号
        private String start_distance;  // 起点距
        private String riverbed_elevation;  // 河底高程
        private String remark;  // 附注
    }
}
