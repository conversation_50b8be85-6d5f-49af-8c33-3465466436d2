package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 站点数据更新记录新增/修改 Request VO")
@Data
public class StationDataLogSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29368")
    private Long id;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "1")
    private Integer stationType;

    @Schema(description = "站点id", example = "22177")
    private Long stationId;

    @Schema(description = "10100:雨量站-降水-逐日降水量年鉴，10101:雨量站-降水-逐日降水量序列，10102:雨量站-降水-月降水特征值，10103:雨量站-降水-年降水特征值，	10104:雨量站-降水-降水量摘录表，10105:雨量站-降水-各时段最大降水-按日，10106:雨量站-降水-各时段最大降水-按小时，10107:雨量站-降水-各时段最大降水-按分钟，	20201：水文站-冰情-冰情统计，20202：水文站-冰情-冰情摘录，20203：水文站-冰情-逐日水温序列，20204：水文站-冰情-逐日水温年鉴，20205：水文站-冰情-月水温特征，	20206：水文站-冰情-年水温特征", example = "2")
    private Integer dataType;

    @Schema(description = "当前版本")
    private Integer currentVersion;

    @Schema(description = "日序列版本")
    private Integer dayVersion;

    @Schema(description = "月序列版本")
    private Integer monthVersion;

    @Schema(description = "年序列版本")
    private Integer yearVersion;

    @Schema(description = "各时段日时段版本")
    private Integer periodDayVersion;

    @Schema(description = "各时段小时时段版本")
    private Integer periodHourVersion;

    @Schema(description = "各时段分钟时段版本")
    private Integer periodMinuteVersion;

    @Schema(description = "创建者姓名", example = "赵六")
    private String creatorName;

    @Schema(description = "更新者")
    private String updator;

}