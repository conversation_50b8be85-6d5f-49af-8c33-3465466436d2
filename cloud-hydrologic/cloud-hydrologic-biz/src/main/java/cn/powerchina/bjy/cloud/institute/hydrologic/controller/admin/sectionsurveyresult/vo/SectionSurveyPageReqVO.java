package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Schema(description = "管理后台 - 实测大断面成果分页查询 Request VO")
public class SectionSurveyPageReqVO extends PageParam {

    @Schema(description = "水文站id")
    private Long stationId;

    @Schema(description = "施测年份")
    private Integer year;

    @Schema(description = "施测日期")
    private LocalDate surveyDate;

    @Schema(description = "断面名称")
    private String sectionName;

    @Schema(description = "测时水位")
    private BigDecimal waterLevel;

    @Schema(description = "检索记录判断", example = "1")
    private Long indexed;
    /**
     * 数据类型
     */
    private Integer dataType;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;
}
