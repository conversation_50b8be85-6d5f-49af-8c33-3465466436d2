package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainyear.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.model.RainFallDataRainYearModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 年鉴修改
 */
@Schema(description = "管理后台 - 降水-年鉴数据-保存 Request VO")
@Data
@ToString(callSuper = true)
public class StationRainYearBookUpdateReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "数据")
    private List<RainFallDataRainYearModel> dataMode;
}
