package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveydetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 实测大断面成果垂线明细数据新增/修改 Request VO")
@Data
public class SectionSurveyDetailSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5217")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8778")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "站点类型不能为空")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;

    @Schema(description = "施测年份", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "施测年份不能为空")
    private Integer year;

    @Schema(description = "汛期类型：1-汛前，2-汛后", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer periodType;

    @Schema(description = "垂线号，不大于4个字", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "垂线号，不大于4个字不能为空")
    private String verticalNo;

    @Schema(description = "起点距，5位整数+1位小数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "起点距，5位整数+1位小数不能为空")
    private BigDecimal startDistance;

    @Schema(description = "河底高程，4位整数+2位小数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "河底高程，4位整数+2位小数不能为空")
    private BigDecimal riverbedElevation;

    @Schema(description = "备注/附注", example = "你猜")
    private String remark;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "是否最新（1最新0历史）")
    private Integer latest;

}