package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation.vo.HydrologicStationPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation.vo.HydrologicStationRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation.vo.HydrologicStationSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.hydrologicstation.HydrologicStationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 水文站")
@RestController
@RequestMapping("/plan/hydrologic/hydrologic/station/")
@Validated
public class HydrologicStationController {

    @Resource
    private HydrologicStationService stationService;

    @PostMapping("/create")
    @Operation(summary = "创建水文站")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station:create')")
    public CommonResult<Long> createStation(@Valid @RequestBody HydrologicStationSaveReqVO createReqVO) {
        return success(stationService.createStation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水文站")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station:update')")
    public CommonResult<Boolean> updateStation(@Valid @RequestBody HydrologicStationSaveReqVO updateReqVO) {
        stationService.updateStation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水文站")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station:delete')")
    public CommonResult<Boolean> deleteStation(@RequestParam("id") Long id) {
        stationService.deleteStation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水文站")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station:query')")
    public CommonResult<HydrologicStationRespVO> getStation(@RequestParam("id") Long id) {
        HydrologicStationRespVO station = stationService.getStation(id);
        return success(station);
    }

    @GetMapping("/page")
    @Operation(summary = "获得水文站分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station:query')")
    public CommonResult<PageResult<HydrologicStationRespVO>> getStationPage(@Valid HydrologicStationPageReqVO pageReqVO) {
        PageResult<HydrologicStationRespVO> pageResult = stationService.getStationPage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/import")
	@ExcelImportCheck
    @Operation(summary = "导入气象站 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importStationExcel(@RequestParam("file") MultipartFile file) throws IOException {
        stationService.importStation(file);
        return success(true);
    }

    @GetMapping("/export/excel")
    @Operation(summary = "导出水文站 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station:export')")
    // @OperateLog(type = EXPORT)
    public void exportStationExcel(@Valid HydrologicStationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<HydrologicStationDO> list = stationService.getStationPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "水文站.xls", "数据", HydrologicStationRespVO.class,
//                        BeanUtils.toBean(list, HydrologicStationRespVO.class));
    }

}