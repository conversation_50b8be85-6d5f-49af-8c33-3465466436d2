package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationyear.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 蒸发-年水面蒸发量特征值 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationEvaporationYearRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "平均水面蒸发量(mm)")
    @ExcelProperty("平均水面蒸发量(mm)")
    private String averageValue;

    @Schema(description = "最大日水面蒸发量(mm)")
    @ExcelProperty("最大日水面蒸发量(mm)")
    private String maxValue;

    @Schema(description = "最大日水面蒸发量日期")
    @ExcelProperty("最大日水面蒸发量日期")
    private String maxValueDate;

    @Schema(description = "最小日水面蒸发量(mm)")
    @ExcelProperty("最小日水面蒸发量(mm)")
    private String minValue;

    @Schema(description = "最小日水面蒸发量日期")
    @ExcelProperty("最小日水面蒸发量日期")
    private String minValueDate;

    @Schema(description = "初冰日期")
    @ExcelProperty("初冰日期")
    private String firstIceDate;

    @Schema(description = "终冰日期")
    @ExcelProperty("终冰日期")
    private String endIceDate;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}