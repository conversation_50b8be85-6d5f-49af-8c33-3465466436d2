package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 水文站-实测悬移质输沙率成果新增/修改 Request VO")
@Data
public class DischargeResultSaveReqVO {

    @Schema(description = "站点id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11254")
    @NotNull(message = "站点id不能为空")
    private Long stationId;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<Item> dataList;

    @Schema(description = "管理后台 - 降水-日降水量数据 Request VO")
    @Data
    public static class Item {
        @Schema(description = "主键", example = "16312")
        private Long id;

        @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5255")
        @NotNull(message = "水文站id不能为空")
        private Long stationId;

        @Schema(description = "年")
        private String year;

        @Schema(description = "月")
        private String month;

        @Schema(description = "日")
        private String day;

        @Schema(description = "起始时分")
        private String startHoursMinute;

        @Schema(description = "起始时分")
        private String endHoursMinute;

        @Schema(description = "流量(m3/s)")
        private String flow;

        @Schema(description = "断面输沙率(kg/s)")
        private String sectionTransportRate;

        @Schema(description = "含沙量断面平均(kg/m3)")
        private String sectionAverageValue;

        @Schema(description = "含沙量单样(kg/m3)")
        private String sectionSampleValue;

        @Schema(description = "测验方法-断面平均含沙量")
        private String testingMethodsAverageValue;

        @Schema(description = "测验方法-单样含沙量")
        private String testingMethodsSampleValue;

        @Schema(description = "备注", example = "随便")
        private String remark;

        @Schema(description = "版本（根据原型待定）")
        private Integer version;

        @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
        private Integer latest;
    }

}