package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodhour.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 降水-各时段最大降水量(小时时段)修改 Request VO")
@Data
public class StationRainPeriodHourSaveReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationRainPeriodHourData> dataList;

    @Schema(description = "要删除的数据id")
    private List<Long> deleteIds;


    @Schema(description = "管理后台 - 降水-各时段最大降水量(小时时段)数据")
    @Data
    public static class StationRainPeriodHourData {
        @Schema(description = "主键")
        private Long id;

        @Schema(description = "年")
        private String year;

        @Schema(description = "1h降水量")
        private String valueDay1;

        @Schema(description = "1h开始月日")
        private String monthDayStart1;

        @Schema(description = "2h降水量")
        private String valueDay2;

        @Schema(description = "2h开始月日")
        private String monthDayStart2;

        @Schema(description = "3h降水量")
        private String valueDay3;

        @Schema(description = "3h开始月日")
        private String monthDayStart3;

        @Schema(description = "6h降水量")
        private String valueDay6;

        @Schema(description = "6h开始月日")
        private String monthDayStart6;

        @Schema(description = "12h降水量")
        private String valueDay12;

        @Schema(description = "12h开始月日")
        private String monthDayStart12;

        @Schema(description = "24h降水量")
        private String valueDay24;

        @Schema(description = "24h开始月日")
        private String monthDayStart24;

        @Schema(description = "48h降水量")
        private String valueDay48;

        @Schema(description = "48h开始月日")
        private String monthDayStart48;

        @Schema(description = "72h降水量")
        private String valueDay72;

        @Schema(description = "72h开始月日")
        private String monthDayStart72;

        @Schema(description = "备注", example = "随便")
        private String remark;
    }

}