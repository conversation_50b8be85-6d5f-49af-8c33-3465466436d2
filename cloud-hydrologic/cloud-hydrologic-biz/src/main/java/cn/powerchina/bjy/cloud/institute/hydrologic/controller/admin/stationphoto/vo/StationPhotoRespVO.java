package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationphoto.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 站点照片管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationPhotoRespVO {

    @Schema(description = "主键")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private Integer year;

    @Schema(description = "照片路径")
    @ExcelProperty("照片路径")
    private String imagePath;

    @Schema(description = "照片名称")
    @ExcelProperty("照片名称")
    private String imageName;

    @Schema(description = "上传人")
    @ExcelProperty("上传人")
    private String creatorName;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}