package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "管理后台 - 实测大断面成果 Response VO")
public class SectionSurveyRespVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "水文站id")
    private Long stationId;

    @Schema(description = "施测年份")
    private Integer year;

    @Schema(description = "施测日期")
    @NotNull(message = "施测日期不能为空")
    @JsonFormat(pattern = "M月d日")
    private LocalDate surveyDate;

    @Schema(description = "断面名称及位置")
    private String sectionName;

    @Schema(description = "测时水位(m)")
    private BigDecimal waterLevel;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新者")
    private String updater;

//    @Schema(description = "垂线明细数据列表")
//    private List<SectionSurveyDetailVO> detailList;

    // 垂线明细字段-直接添加到对象中
    @Schema(description = "垂线号")
    private String verticalNo;

    @Schema(description = "起点距(m)")
    private BigDecimal startDistance;

    @Schema(description = "河底高程(m)")
    private BigDecimal riverBedElevation;
}