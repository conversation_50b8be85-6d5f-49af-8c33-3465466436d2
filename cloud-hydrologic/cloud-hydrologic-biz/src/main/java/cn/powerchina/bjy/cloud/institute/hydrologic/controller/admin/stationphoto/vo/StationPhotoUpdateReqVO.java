package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationphoto.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 站点照片管理新增/修改 Request VO")
@Data
public class StationPhotoUpdateReqVO {

    @Schema(description = "主键")
    @NotNull(message = "请选择一条记录")
    private Long id;

    @Schema(description = "年")
    @NotNull(message = "请输入年")
    private Integer year;
}