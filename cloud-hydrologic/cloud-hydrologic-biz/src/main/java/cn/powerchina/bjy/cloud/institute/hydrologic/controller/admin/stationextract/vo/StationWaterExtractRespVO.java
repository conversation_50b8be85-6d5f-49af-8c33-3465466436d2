package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationextract.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 站点-摘录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationWaterExtractRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8726")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", example = "30784")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "2")
    @ExcelProperty("站点类型，1：雨量站，2：水文站， 3：气象站")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "年")
    @ExcelProperty("年")
    private Integer year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private Integer month;

    @Schema(description = "日")
    @ExcelProperty("日")
    private Integer day;

    @Schema(description = "时分")
    @ExcelProperty("时分")
    private String hoursMinute;

    @Schema(description = "冰情")
    @ExcelProperty("冰情")
    private String iceSituation;

    @Schema(description = "冰厚(m)")
    @ExcelProperty("冰厚(m)")
    private String iceThickness;

    @Schema(description = "冰上雪深(m)")
    @ExcelProperty("冰上雪深(m)")
    private String deepSnowOnIce;

    @Schema(description = "岸上气温(C°)")
    @ExcelProperty("岸上气温(C°)")
    private String shoreTemperature;

    @Schema(description = "坝上水位(m)")
    @ExcelProperty("坝上水位(m)")
    private String waterLevel;

    @Schema(description = "水文站-水库水文要素摘录表-流量(m3/s)")
    @ExcelProperty("水文站-水库水文要素摘录表-蓄水量(108m³)")
    private String flow;

    @Schema(description = "水文站-水库水文要素摘录表-出库流量(m³/s)")
    @ExcelProperty("水文站-水库水文要素摘录表-出库流量(m³/s)")
    private String sandValue;

    @Schema(description = "降水量")
    @ExcelProperty("降水量")
    private String value;

    @Schema(description = "起时:分")
    @ExcelProperty("起时:分")
    private String startHourMinute;

    @Schema(description = "止时:分")
    @ExcelProperty("止时:分")
    private String endHourMinute;

    @Schema(description = "版本")
    @ExcelProperty("版本")
    private Integer version;

    @Schema(description = "最新版本（1：最新；0：历史）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新；0：历史）")
    private Integer latest;

    @Schema(description = "记录日期")
    @ExcelProperty("记录日期")
    private LocalDate currentDay;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}