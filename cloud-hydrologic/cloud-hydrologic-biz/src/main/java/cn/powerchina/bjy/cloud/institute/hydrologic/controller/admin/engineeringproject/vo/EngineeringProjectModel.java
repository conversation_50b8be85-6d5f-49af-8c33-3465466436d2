package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringproject.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class EngineeringProjectModel {
    @Schema(description = "项目名称", example = "王五")
    @ExcelProperty(index = 0)
    private String name;

    @Schema(description = "所属国家")
    @ExcelProperty(index = 1)
    private String country;

    @Schema(description = "所属省级行政区")
    @ExcelProperty(index = 2)
    private String province;

    @Schema(description = "所属市级行政区")
    @ExcelProperty(index = 3)
    private String city;

    @Schema(description = "所属县级行政区")
    @ExcelProperty(index = 4)
    private String county;

    @Schema(description = "详细位置")
    @ExcelProperty(index =5)
    private String address;

    @Schema(description = "经度")
    @ExcelProperty(index = 6)
    private String longitude;

    @Schema(description = "纬度")
    @ExcelProperty(index = 7)
    private String latitude;

    @Schema(description = "开发方式")
    @ExcelProperty(index = 8)
    private String developWay;

    @Schema(description = "工作进展")
    @ExcelProperty(index = 9)
    private String progress;

    @Schema(description = "是否纳规")
    @ExcelProperty(index = 10)
    private String designStage;

    @Schema(description = "业主单位")
    @ExcelProperty(index = 11)
    private String ownerUnit;

    @Schema(description = "重大变更情况说明")
    @ExcelProperty(index = 12)
    private String explanation;
}
