package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.StationInfoRespIndexVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 站点树列表
 * @Author: yhx
 * @CreateDate: 2024/7/9
 */
@Schema(description = "管理后台 - 站点列表 Response VO")
@Data
public class ResourceStationRespVO {

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站")
    private Integer stationType;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "站点id")
    private String stationId;

    @Schema(description = "站点名称")
    private String stationName;

    @Schema(description = "站点信息")
    private List<StationInfoRespIndexVO> children;


    @Schema(description = "管理后台 - 站点信息 Response VO")
    @Data
    public static class StationInfo {

        @Schema(description = "id")
        private Long id;

        @Schema(description = "站点id")
        private Long stationId;

        @Schema(description = "站点名称")
        private String stationName;
        @Schema(description = "站点信息")
        private List<StationInfo> children;
    }
}
