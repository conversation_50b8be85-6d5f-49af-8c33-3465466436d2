package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentyear.vo.SandContentYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentyear.vo.SandContentYearRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentyear.vo.SandContentYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sandcontentyear.SandContentYearService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台-含沙量-年含沙量特征值")
@RestController
@RequestMapping("/plan/hydrologic/sand/content/year")
@Validated
public class SandContentYearController {

    @Resource
    private SandContentYearService sandContentYearService;

    @PutMapping("/update")
    @Operation(summary = "更新-年含沙量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:sand-content-year:update')")
    public CommonResult<Boolean> updateSandContentYear(@Valid @RequestBody SandContentYearSaveReqVO updateReqVO) {
        sandContentYearService.updateSandContentYear(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得-年含沙量特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:sand-content-year:query')")
    public CommonResult<PageResult<SandContentYearRespVO>> getSandContentYearPage(@Valid SandContentYearPageReqVO pageReqVO) {
        PageResult<SandContentYearRespVO> pageResult = sandContentYearService.getSandContentYearPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-年含沙量特征值分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_SAND_YEAR)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<SandContentYearRespVO>> getSandContentYearDataPage(@Valid SandContentYearPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        PageResult<SandContentYearRespVO> pageResult = sandContentYearService.getSandContentYearPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出年含沙量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportSandContentYearExcel(@Valid SandContentYearPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        sandContentYearService.exportExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出年含沙量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportSandContentYearDataExcel(@Valid SandContentYearPageReqVO pageReqVO,
                                               HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        sandContentYearService.exportExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入年含沙量特征 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:flow-info:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        sandContentYearService.importData(file, stationId);
        return success(true);
    }
}