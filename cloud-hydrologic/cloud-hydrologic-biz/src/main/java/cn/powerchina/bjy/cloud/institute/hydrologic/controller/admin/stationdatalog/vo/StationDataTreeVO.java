package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 年鉴拥有的年份-树列表
 * @Author: yhx
 * @CreateDate: 2024/7/9
 */
@Schema(description = "管理后台 - 站点列表 Response VO")
@Data
public class StationDataTreeVO {

    @Schema(description = "年份")
    private String value;

    @Schema(description = "年份")
    private String label;

    @Schema(description = "站点信息")
    private List<StationInfo> children;


    @Schema(description = "管理后台 - 站点信息 Response VO")
    @Data
    public static class StationInfo {

        @Schema(description = "月份")
        private String value;

        @Schema(description = "月份")
        private String label;
    }
}
