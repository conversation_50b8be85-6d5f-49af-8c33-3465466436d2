package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.datamigration.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 数据迁移-备份 请求参数
 * @time 2024/8/28 16:26
 */
@Data
public class DataSelectionTreeVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 111974956300851771L;
    @Schema(description = "操作类型：0：备份 1：还原")
    private String type;
    @Schema(description = "nodes")
    private List<Node> nodes;

    @Data
    public static class Node implements Serializable {
        @Serial
        private static final long serialVersionUID = 6221110603572889552L;
        @Schema(description = "id")
        private Long id;
        @Schema(description = "类别")
        private String type;
        @Schema(description = "名称")
        private String name;
        @Schema(description = "是否选中 false：未选中 true：已选中")
        private boolean selected;
        @Schema(description = "级别")
        private Integer level;
        @Schema(description = "是否虚拟节点")
        private boolean virtualNode;
        @Schema(description = "parentId")
        private long parentId;
        @Schema(description = "子节点")
        List<Node> children;
    }
}
