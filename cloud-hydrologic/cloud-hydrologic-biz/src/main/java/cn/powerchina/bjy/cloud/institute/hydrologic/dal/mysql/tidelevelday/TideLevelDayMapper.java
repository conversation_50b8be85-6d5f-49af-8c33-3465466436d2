package cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.tidelevelday;

import cn.hutool.db.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodminute.StationRainPeriodMinuteDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.tidelevelday.TideLevelDayDO;
import java.util.*;

import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.HydrologicStationChecker;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 水文站—潮位日统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TideLevelDayMapper extends BaseMapperX<TideLevelDayDO>, HydrologicStationChecker<TideLevelDayDO> {

    @Update("update hydrologic_tide_level_day set version = '0' where station_id = #{stationId}  and data_type=#{dataType} and year=#{year} and month=#{month}")
    void updateVersion(@Param("stationId")Long stationId, @Param("dataType")Integer dataType, @Param("year")String year, @Param("month")String month);

    @Update("update hydrologic_tide_level_day set version = '0' where station_id = #{stationId}  and data_type=#{dataType} and year=#{year} and month=#{month}")
    void updateDayVersion(@Param("stationId")Long stationId, @Param("dataType")Integer dataType, @Param("year")String year, @Param("month")String month);

    @Select("select CONCAT(year,month) as year from hydrologic_tide_level_day where deleted = 0 and station_id = #{stationId} and data_type = #{dataType} group by year,month order by year asc")
    List<Integer> listYear(@Param("stationId") Long stationId, @Param("dataType") Integer dataType);
}