package cn.powerchina.bjy.cloud.institute.hydrologic.service;

import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.QueryWrapperX;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.station.StationDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationday.StationDayDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.LatestEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ShowTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.YearForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationdatalog.StationDataLogService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.MyStringUtils;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.*;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR;

/**
 * Excel导入基础类
 * 注：MODEL如果使用了{@code @Builder}，需同时使用{@code @NoArgsConstructor和@AllArgsConstructor}
 *
 * <AUTHOR>
 **/
@Slf4j
public abstract class BaseExcelProcessor<DO extends BaseDO, MODEL> {

    /**
     * 存储站点id，注意释放
     */
    protected static final ThreadLocal<Long> EXCEL = new ThreadLocal<>();

    protected Class<MODEL> modelClass;
    protected Class<DO> doClass;
    @Resource
    protected RedisTemplate<String, Object> redisTemplate;
    @Resource
    protected StationDataLogService stationDataLogService;

    /**
     * 导入序列格式的简单数据
     * 逻辑：解析行数据，将历史数据和新数据拷贝入库，记录日志
     *
     * @param file        Excel
     * @param lockKey     分布式锁key
     * @param headNum     头
     * @param stationType
     * @throws IOException
     */
    public void importData(MultipartFile file, String lockKey, int headNum, Integer stationType) throws IOException {

        // 增加分布式锁，只能有一个导入
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, 1, 60, TimeUnit.SECONDS))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EXISTS_ERROR);
        }
        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }
            List<MODEL> importData = EasyExcel.read(file.getInputStream(), modelClass == null ? getModelClass() : modelClass, null).sheet().headRowNumber(headNum).doReadSync();

            processSimpleModel(importData, ShowTypeEnum.LIST,stationType);

        } catch (IOException e) {
            log.error("importFile--->error.", e);
        } finally {
            redisTemplate.delete(lockKey);
            EXCEL.remove();
        }
    }

    /**
     * 只有处理数据逻辑，无锁，需自己处理，同时需要注意ThreadLocal的释放
     *
     * @param importData  Excel数据
     * @param stationType
     */
    protected void processSimpleModel(List<MODEL> importData, ShowTypeEnum source, Integer stationType) {
        if (CollectionUtils.isEmpty(importData)) {
            return;
        }
        // 校验数据
//        List<DO> data = validateStationExistsImport(importData, null, null);
        List<DO> data = validateStationExistsImport(importData);
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        // 涉及关联站点等其他处理的逻辑
        dataProcess(data, source);
        // 数据入库
        try {
            if(Objects.nonNull(doClass) && doClass==StationDO.class){
                data.forEach(item->{
                    ((StationDO)item).setStationType(stationType);
                });
            }
        } catch (Exception e) {
            log.error("站点数据导入异常：",e);
        }
        getMapper().insertBatch(data);
    }

    /**
     * 通用解析年鉴数据流程
     *
     * @param file 年鉴数据Excel
     * @param lockKey 分布式锁key
     * @param headNum 年份所在行
     * @param startRow 日序列数据开始的行
     * @param defaultType 是否使用通用逻辑解析日数据，0为使用，1为自定义
     */
    protected void importYearData(MultipartFile file, String lockKey, int headNum, int startRow, int defaultType) {
        // 增加分布式锁，只能有一个导入
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, 1, 10, TimeUnit.SECONDS))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EXISTS_ERROR);
        }
        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }
            List<YearForm> importData = EasyExcel.read(file.getInputStream(), YearForm.class, null).sheet().headRowNumber(headNum).doReadSync();

            if (CollectionUtils.isEmpty(importData)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
            }
            processYearModel(importData, headNum, startRow, defaultType);

        } catch (IOException e) {
            log.error("importFile--->error.", e);
        } finally {
            redisTemplate.delete(lockKey);
            EXCEL.remove();
        }
    }

    /**
     * 只有处理数据逻辑，无锁，需自己处理，同时需要注意ThreadLocal的释放
     *
     * @param importData  Excel数据
     * @param headNum     头
     * @param startRow    开始行（含，从0开始）
     * @param defaultType 是否使用默认方式处理日数据
     */
    protected void processYearModel(List<YearForm> importData, int headNum, int startRow, int defaultType) {
        if (CollectionUtils.isEmpty(importData)) {
            return;
        }
        // 获取年
        String year = importData.get(headNum).getColumnSecondName();
        if (StringUtils.isBlank(year)) {
            year = importData.get(headNum).getValue1();
        }
        if (StringUtils.isBlank(year)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, headNum+1, "年份填写错误；");
        }
        if (year.contains("年")) {
            year = year.replace("年", "");
        }
        String yearRemark = importData.get(importData.size() - 1).getValue1();
        // 转换为日格式，日格式固定
        List<YearForm> dayDatas = new ArrayList<>(31);
        for (int i = startRow; i < startRow + 31 && i < importData.size() ; i++) {
            dayDatas.add(importData.get(i));
        }
        List<DO> doList = null;
        if (0 == defaultType) {
            doList = fillDayItemDefault(dayDatas, EXCEL.get(), year);
        } else {
            doList = fillDayItem(dayDatas, EXCEL.get(), year);
        }

        if (CollectionUtils.isEmpty(doList)) {
            return;
        }
        // 涉及关联站点等其他处理的逻辑
        dataProcess(doList, ShowTypeEnum.YEAR);
        // 入库
        getMapper().insertBatch(doList);
        // 转换为年、月格式，需具体处理
        processOtherData(year, yearRemark, importData);
    }

    protected void processYearModel2(List<YearForm> importData, int headNum, int startRow, int defaultType) {
        if (CollectionUtils.isEmpty(importData)) {
            return;
        }
        // 获取年
        String year = importData.get(headNum).getColumnSecondName();
        String remark1=importData.get(headNum).getValue3();
        String remark2=importData.get(headNum).getValue8();
        if (StringUtils.isBlank(year)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, headNum+1, "年份填写错误；");
        }
        if (StringUtils.isBlank(remark1)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, headNum+1, "蒸发器位置特征填写错误；");
        }
        if (StringUtils.isBlank(remark2)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, headNum+1, "蒸发器型式填写错误；");
        }
        if (year.contains("年")) {
            year = year.replace("年", "");
        }
        String yearRemark = importData.get(importData.size() - 1).getValue1()+"##"+importData.get(headNum).getValue3()+"##"+importData.get(headNum).getValue8();
        // 转换为日格式，日格式固定
        List<YearForm> dayDatas = new ArrayList<>(31);
        for (int i = startRow; i < startRow + 31 && i < importData.size() ; i++) {
            dayDatas.add(importData.get(i));
        }
        List<DO> doList = null;
        if (0 == defaultType) {
            doList = fillDayItemDefault(dayDatas, EXCEL.get(), year);
        } else {
            doList = fillDayItem(dayDatas, EXCEL.get(), year);
        }
//        StationDayDO day= (StationDayDO) doList.get(0);
//        day.setMaxValue(remark1);
//        day.setMinValue(remark2);

        if (CollectionUtils.isEmpty(doList)) {
            return;
        }
        // 涉及关联站点等其他处理的逻辑
        dataProcess(doList, ShowTypeEnum.YEAR);
        // 入库
        getMapper().insertBatch(doList);
        // 转换为年、月格式，需具体处理
        processOtherData(year, yearRemark, importData);
    }


    protected void processYearModel1(List<YearForm> importData, int headNum, int startRow, int defaultType) {
        if (CollectionUtils.isEmpty(importData)) {
            return;
        }
        // 获取年
        String year = importData.get(headNum).getColumnSecondName();
        if (StringUtils.isBlank(year)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EMPTY_ERROR, headNum+1, "年份填写错误；");
        }
        if (year.contains("年")) {
            year = year.replace("年", "");
        }
        String yearRemark = importData.get(importData.size() - 1).getValue1()+"##"+importData.get(headNum).getValue3()+"##"+importData.get(headNum).getValue8();
        // 转换为日格式，日格式固定
        List<YearForm> dayDatas = new ArrayList<>(31);
        for (int i = startRow; i < startRow + 31 && i < importData.size() ; i++) {
            dayDatas.add(importData.get(i));
        }
        List<DO> doList = null;
        if (0 == defaultType) {
            doList = fillDayItemDefault(dayDatas, EXCEL.get(), year);
        } else {
            doList = fillDayItem(dayDatas, EXCEL.get(), year);
        }

        if (CollectionUtils.isEmpty(doList)) {
            return;
        }
        // 涉及关联站点等其他处理的逻辑
        dataProcess(doList, ShowTypeEnum.YEAR);
        // 入库
        getMapper().insertBatch(doList);
        // 转换为年、月格式，需具体处理
        processOtherData(year, yearRemark, importData);
    }

    protected void valid(String year, String month, String day, Integer lineNumber, boolean importFlag){
        MyStringUtils.checkYearMonthDay(year,month,day,lineNumber,importFlag);
    }

    /**
     * 处理日格式的数据
     * 业务实现，比如有些数据校验
     *
     * @param excelData 导入数据
     * @param stationId 站点id
     * @param year 年份
     * @return 日数据
     */
    protected abstract List<DO> fillDayItem(List<YearForm> excelData, Long stationId, String year);

    /**
     * 处理其他数据，如年、月
     *
     * @param year 年
     * @param yearRemark 附注
     * @param excelData Excel解析后的数据
     */
    protected abstract void processOtherData(String year, String yearRemark, List<YearForm> excelData);

    /**
     * 获取子类mapper
     *
     * @return BaseMapperX
     */
    protected abstract BaseMapperX<DO> getMapper();

    /**
     * 校验
     *
     * @param importDataList Excel数据
     * @param stationType
     * @param dataType
     * @return 入库数据
     */
    protected abstract List<DO> validateStationExistsImport(List<MODEL> importDataList, Integer stationType, Integer dataType);

    protected abstract List<DO> validateStationExistsImport(List<MODEL> importDataList);


    protected abstract void dataProcess(List<DO> dos, ShowTypeEnum source);

    protected Class<MODEL> getModelClass() {
        Type currClass = getClass().getGenericSuperclass();
        ParameterizedType parameterizedType = (ParameterizedType) currClass;
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        modelClass = (Class<MODEL>) actualTypeArguments[1];
        return modelClass;
    }

    protected Class<DO> getDOClass() {
        Type currClass = getClass().getGenericSuperclass();
        ParameterizedType parameterizedType = (ParameterizedType) currClass;
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        doClass = (Class<DO>) actualTypeArguments[0];
        return doClass;
    }

    protected List<DO> getBeforeData(SFunction<DO, Long> stationId, SFunction<DO, Integer> latest, SFunction<DO, Boolean> deleted) {
        QueryWrapperX<DO> queryWrapperX = new QueryWrapperX<>();
        queryWrapperX.lambda().eq(stationId, EXCEL.get())
                .eq(latest, LatestEnum.LATEST.getType())
                .eq(deleted, Boolean.FALSE);
        return getMapper().selectList(queryWrapperX);
    }

    protected UpdateWrapper<DO> getUpdateWrapper(SFunction<DO, Long> stationIdFunction,
                                                 SFunction<DO, Integer> latestFunction,
                                                 SFunction<DO, Integer> versionFunction,
                                                 SFunction<DO, Boolean> deletedFunction,
                                                 int version) {
        UpdateWrapper<DO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(stationIdFunction, EXCEL.get())
                .eq(latestFunction, LatestEnum.LATEST.getType())
                .eq(versionFunction, version)
                .eq(deletedFunction, Boolean.FALSE);

        return updateWrapper;
    }

    protected DO validateDOExists(Long id, ErrorCode errorCode) {
        DO data = getMapper().selectById(id);
        if (data == null) {
            throw exception(errorCode);
        }
        return data;
    }

    protected void validateDosExists(Set<Long> ids, ErrorCode errorCode) {
        if (CollectionUtils.isEmpty(ids)) {
            throw exception(errorCode);
        }
        ids.forEach(item -> validateDOExists(item, errorCode));
    }

//    protected List<DO> findDOByVersionAndYear(Long stationId, String year, Integer version) {
//        QueryWrapper<DO> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("station_id", stationId);
//        queryWrapper.eq("year", year);
//        queryWrapper.eq("version", version);
//        queryWrapper.orderByAsc("month");
//        return getMapper().selectList(queryWrapper);
//    }

    protected List<DO> fillDayItemDefault(List<YearForm> excelData, Long stationId, String year) {
        List<CommonDayItem> commonDayItems = new ArrayList<>(31 * 12);
        for (int i = 0; i < excelData.size(); i++) {
            YearForm importModel = excelData.get(i);
            addCommonDayDO(commonDayItems, stationId, year, 1 + "", (i + 1) + "", importModel.getValue1());
            addCommonDayDO(commonDayItems, stationId, year, 2 + "", (i + 1) + "", importModel.getValue2());
            addCommonDayDO(commonDayItems, stationId, year, 3 + "", (i + 1) + "", importModel.getValue3());
            addCommonDayDO(commonDayItems, stationId, year, 4 + "", (i + 1) + "", importModel.getValue4());
            addCommonDayDO(commonDayItems, stationId, year, 5 + "", (i + 1) + "", importModel.getValue5());
            addCommonDayDO(commonDayItems, stationId, year, 6 + "", (i + 1) + "", importModel.getValue6());
            addCommonDayDO(commonDayItems, stationId, year, 7 + "", (i + 1) + "", importModel.getValue7());
            addCommonDayDO(commonDayItems, stationId, year, 8 + "", (i + 1) + "", importModel.getValue8());
            addCommonDayDO(commonDayItems, stationId, year, 9 + "", (i + 1) + "", importModel.getValue9());
            addCommonDayDO(commonDayItems, stationId, year, 10 + "", (i + 1) + "", importModel.getValue10());
            addCommonDayDO(commonDayItems, stationId, year, 11 + "", (i + 1) + "", importModel.getValue11());
            addCommonDayDO(commonDayItems, stationId, year, 12 + "", (i + 1) + "", importModel.getValue12());
        }
        List<DO> result = new ArrayList<>(31 * 12);
        for (CommonDayItem item : commonDayItems) {
            DO d = BeanUtils.toBean(item, doClass == null ? getDOClass() : doClass);
            result.add(d);
        }
        return result;
    }

    private void addCommonDayDO(List<CommonDayItem> dayList, Long stationId, String year, String month, String day, String value) {
        CommonDayItem item = new CommonDayItem();
        item.setStationId(stationId);
        item.setYear(year);
        item.setMonth(month);
        item.setDay(day);
        item.setValue(value);
        //不符合年月日格式的数据排除掉
        if(MyStringUtils.checkYearMonthDay(Integer.parseInt(year),Integer.parseInt(month),Integer.parseInt(day))){
            dayList.add(item);
        }
    }

    private class CommonDayItem {
        private Long stationId;
        private String year;
        private String month;
        private String day;
        private String value;

        public Long getStationId() {
            return stationId;
        }

        public void setStationId(Long stationId) {
            this.stationId = stationId;
        }

        public String getYear() {
            return year;
        }

        public void setYear(String year) {
            this.year = year;
        }

        public String getMonth() {
            return month;
        }

        public void setMonth(String month) {
            this.month = month;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    protected void validateYear(Integer year, int lineNum) {
        if (null == year) {
            throw exception(EXCEL_IMPORT_EMPTY_ERROR, lineNum, "缺少年");
        }
        if (year <= 0) {
            throw exception(EXCEL_IMPORT_EMPTY_ERROR, lineNum, year + "年格式错误");
        }
    }

    protected void validateYear(Integer year) {
        if (null == year) {
            throw exception(EXCEL_UPDATE_DATA_YEAR_EMPTY_ERROR);
        }

        if (year <= 0) {
            throw exception(EXCEL_UPDATE_DATA_YEAR_FORMAT_ERROR, year);
        }
    }

    protected void validateMonth(Integer month) {
        if (null == month) {
            throw exception(EXCEL_UPDATE_DATA_MONTH_SINGLE_EMPTY_ERROR);
        }

        if (month <= 0 || month > 12) {
            throw exception(EXCEL_UPDATE_DATA_MONTH_SINGLE_FORMAT_ERROR, month);
        }
    }

    protected void validateMonth(Integer month, int lineNum) {
        if (null == month) {
            throw exception(EXCEL_IMPORT_EMPTY_ERROR, lineNum, "缺少月");
        }

        if (month <= 0 || month > 12) {
            throw exception(EXCEL_IMPORT_EMPTY_ERROR, lineNum, month + "月格式错误");
        }
    }

    private void validateDay(Integer day) {
        if (null == day) {
            throw exception(EXCEL_UPDATE_DATA_DAY_SINGLE_EMPTY_ERROR);
        }
        if (day <= 0 || day > 31) {
            throw exception(EXCEL_UPDATE_DATA_DAY_SIGLE_FORMAT_ERROR, day);
        }
    }

    private void validateDay(Integer day, int lineNum) {
        if (null == day) {
            throw exception(EXCEL_IMPORT_EMPTY_ERROR, lineNum, "缺少日");
        }
        if (day <= 0 || day > 31) {
            throw exception(EXCEL_IMPORT_EMPTY_ERROR, lineNum, "日格式错误");
        }
    }

    protected void validateYearMonth(Integer year, Integer month, int lineNum) {
        validateYear(year, lineNum);
        validateMonth(month, lineNum);
        try {
            String uniKey = year + "-" + month + "-";
            DateUtils.parseDate(uniKey, "yyyy-MM");
        } catch (Exception e) {
            throw exception(EXCEL_IMPORT_EMPTY_ERROR, lineNum, "年月格式错误");
        }
    }

    protected void validateYearMonth(Integer year, Integer month) {
        validateYear(year);
        validateMonth(month);
        try {
            String uniKey = year + "-" + month + "-";
            DateUtils.parseDate(uniKey, "yyyy-MM");
        } catch (Exception e) {
            throw exception(EXCEL_UPDATE_DATA_FORMAT_ERROR, year, month);
        }
    }

    protected void validateMonthDay(Integer month, Integer day) {
        validateMonth(month);
        validateDay(day);
        try {
            String uniKey = "1970-" + month + "-" + day;
            DateUtils.parseDate(uniKey, "yyyy-MM-dd");
        } catch (Exception e) {
            throw exception(EXCEL_UPDATE_DATA_MONTH_DAY_FORMAT_ERROR, month, day);
        }
    }

    protected void validateMonthDay(Integer month, Integer day, int lineNum) {
        validateMonth(month, lineNum);
        validateDay(day, lineNum);
        try {
            String uniKey = "1970-" + month + "-" + day;
            DateUtils.parseDate(uniKey, "yyyy-MM-dd");
        } catch (Exception e) {
            throw exception(EXCEL_IMPORT_EMPTY_ERROR, lineNum, "月日格式错误");
        }
    }

    protected void validateFull(Integer year, Integer month, Integer day, int lineNum) {
        validateYear(year, lineNum);
        validateMonth(month, lineNum);
        validateDay(day, lineNum);
        try {
            String uniKey = year + "-" + month + "-" + day;
            DateUtils.parseDate(uniKey, "yyyy-MM-dd");
        } catch (Exception e) {
            throw exception(EXCEL_IMPORT_EMPTY_ERROR, lineNum, "年月日格式错误");
        }
    }

    protected void validateFull(Integer year, Integer month, Integer day) {
        validateYear(year);
        validateMonth(month);
        validateDay(day);
        try {
            String uniKey = year + "-" + month + "-" + day;
            DateUtils.parseDate(uniKey, "yyyy-MM-dd");
        } catch (Exception e) {
            throw exception(EXCEL_UPDATE_DATA_DAY_FORMAT_ERROR, year, month, day);
        }
    }
}
