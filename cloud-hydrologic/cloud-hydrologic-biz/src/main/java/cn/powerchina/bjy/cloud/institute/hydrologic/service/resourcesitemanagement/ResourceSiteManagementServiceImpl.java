package cn.powerchina.bjy.cloud.institute.hydrologic.service.resourcesitemanagement;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.StationInfoRespIndexVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.vo.ResourceSiteManagementPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.vo.ResourceSiteManagementRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.vo.ResourceSiteManagementSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filetree.FileTreeDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.resourcesitemanagement.ResourceSiteManagementDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.roleapply.RoleApplyDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.resourcesitemanagement.ResourceSiteManagementMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.PlanningDesignConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ResourceSiteTypeMapRoleApplyBindTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.ResourceSiteManagementImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.filedocument.FileDocumentService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.filetree.FileTreeService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.roleapply.RoleApplyService;
import cn.powerchina.bjy.cloud.institute.hydrologic.task.AreaManager;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.RoleUtil;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants.*;

/**
 * 资源站点管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ResourceSiteManagementServiceImpl implements ResourceSiteManagementService {

    // 使用 AtomicLong 生成递增ID
    private static final AtomicLong STATION_ID_GENERATOR = new AtomicLong();

    // 不同类型站点的起始ID
    private static final long PUMP_STORAGE_START_ID = 1000000;  // 抽水蓄能站起始ID
    private static final long CONVENTIONAL_START_ID = 2000000;  // 常规水电站起始ID

    @Resource
    private ResourceSiteManagementMapper resourceSiteManagementMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private FileTreeService fileTreeService;

    @Autowired
    private FileDocumentService fileDocumentService;

    @Autowired
    private AreaManager areaManager;

    @Autowired
    private RoleApplyService roleApplyService;


    /** 参数校验 */
    private static final Map<String,String> map=new HashMap<>(30);

    static {
        map.put("powerStationName","站址名称");
        map.put("country","国家");
        map.put("provinceCityCounty","省市县");
        map.put("address","详细地址");
        map.put("designStage","工作深度");
        map.put("developWay","开发方式");
        map.put("complianceRegulations","是否纳规");
        map.put("longitude","经度");
        map.put("latitude","纬度");
        map.put("totalCapacity","总装机容量(MW)");
        map.put("fullShippingHours","连续满发小时数(h)");
        map.put("ratedHead","额定水头");
        map.put("distanceToHeightRatio","距高比");
        map.put("staticInvestment","单位千瓦静态投资(元/kW)");
        map.put("waterSourceConditions","水源条件");
        map.put("significantSensitiveFactors","重大敏感因素");
        map.put("designUnit","设计单位");
        map.put("lnvestmentUnit","投资单位");
        map.put("dataSources","数据来源");
        map.put("remarks","备注");
    }


    @Override
    public Long createResourceSiteManagement(ResourceSiteManagementSaveReqVO createReqVO) {
        ResourceSiteManagementDO resourceSiteManagementDO = findEntityByPowerStationName(createReqVO.getPowerStationName());
        if (Objects.nonNull(resourceSiteManagementDO)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_NAME_SAVE_EXISTS_ERROR);
        }
        // 插入
        ResourceSiteManagementDO resourceSiteManagement = BeanUtils.toBean(createReqVO, ResourceSiteManagementDO.class);
        resourceSiteManagement.setPowerStationType(4);
        resourceSiteManagementMapper.insert(resourceSiteManagement);
        //插入文件树
        fileTreeService.addFileTreeType(resourceSiteManagement.getId(), resourceSiteManagement.getPowerStationName(),0);
        // 返回
        return resourceSiteManagement.getId();
    }

    @Override
    public void updateResourceSiteManagement(ResourceSiteManagementSaveReqVO updateReqVO) {
        // 校验存在
        validateProjectExists(updateReqVO.getId());
        ResourceSiteManagementDO resourceSiteManagementDO = findEntityByPowerStationName(updateReqVO.getPowerStationName());
        if (Objects.nonNull(resourceSiteManagementDO) && !Objects.equals(resourceSiteManagementDO.getId(), updateReqVO.getId())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_NAME_SAVE_EXISTS_ERROR);
        }
        // 更新
        ResourceSiteManagementDO resourceSiteManagement = BeanUtils.toBean(updateReqVO, ResourceSiteManagementDO.class);
        resourceSiteManagementMapper.updateById(resourceSiteManagement);
        //更新文件树
        fileTreeService.modifyFileTreeNameByProjectId(resourceSiteManagement.getId(), resourceSiteManagement.getPowerStationName());
    }

    @Override
    public void deleteResourceSiteManagement(Long id) {
//        // 校验存在
//        validateProjectExists(id);
        //校验是否存在文件资料
        FileTreeDO fileTreeDO = fileTreeService.selectFirstLevelFileTreeDO(id,"0");
        if (Objects.nonNull(fileTreeDO) && !CollectionUtils.isEmpty(fileDocumentService.findFileDocumentDOByTreeId(fileTreeDO.getId()))) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_DELETE_EXISTS_ERROR);
        }
        // 删除
        resourceSiteManagementMapper.deleteById(id);
        //删除一级子树
        fileTreeService.deleteFileTreeByProjectId(id);
    }

    private void validateProjectExists(Long id) {
//        if (resourceSiteManagementMapper.selectById(id) == null) {
//            throw exception(PROJECT_NOT_EXISTS);
//        }
        // 使用QueryWrapper添加deleted=0条件
        QueryWrapper<ResourceSiteManagementDO> queryWrapper = new QueryWrapper<ResourceSiteManagementDO>()
                .eq("id", id)
                .eq("deleted", 0);
        if (resourceSiteManagementMapper.selectOne(queryWrapper) == null) {
            throw exception(PROJECT_NOT_EXISTS);
        }
    }

    @Override
    public ResourceSiteManagementRespVO getResourceSiteManagement(Long id) {
        ResourceSiteManagementDO resourceSiteManagementDO = resourceSiteManagementMapper.selectById(id);
        ResourceSiteManagementRespVO resourceSiteManagementRespVO = BeanUtils.toBean(resourceSiteManagementDO, ResourceSiteManagementRespVO.class);
        resourceSiteManagementRespVO.setProvinceName(areaManager.getProvinceNameByCode(resourceSiteManagementDO.getProvince()));
        if (StringUtils.isBlank(resourceSiteManagementDO.getCountry())) {
            throw exception(ErrorCodeConstants.PARAM_ERROR, "国");
        }
        if (StringUtils.isBlank(resourceSiteManagementDO.getProvince())) {
            resourceSiteManagementRespVO.setProvinceName(null);
        }
        resourceSiteManagementRespVO.setCityName(areaManager.getCityNameByCode(resourceSiteManagementDO.getCity()));
        if (StringUtils.isBlank(resourceSiteManagementDO.getCity())) {
            resourceSiteManagementRespVO.setCityName(null);
        }
        if (StringUtils.isNotBlank(resourceSiteManagementDO.getCounty())) {
            resourceSiteManagementRespVO.setCountyName(areaManager.getAreaNameByCode(resourceSiteManagementDO.getCounty()));
            if (StringUtils.isBlank(resourceSiteManagementDO.getCounty())) {
                resourceSiteManagementRespVO.setCountyName(null);
            }
        }
        if (StringUtils.isBlank(resourceSiteManagementDO.getCountry())) {
                resourceSiteManagementRespVO.setCountry(null);
            }
        return resourceSiteManagementRespVO;
    }

    @Override
    public PageResult<ResourceSiteManagementRespVO> getResourceSiteManagementPage(ResourceSiteManagementPageReqVO pageReqVO) {
//        PageResult<ResourceSiteManagementDO> doPageResult = resourceSiteManagementMapper.selectPage(pageReqVO);
//        if (null == doPageResult || org.apache.commons.collections4.CollectionUtils.isEmpty(doPageResult.getList())) {
//            return PageResult.empty();
//        }
//        PageResult<ResourceSiteManagementRespVO> result = PageResult.empty();
//        List<ResourceSiteManagementRespVO> data = new ArrayList<>(doPageResult.getList().size());
//        result.setTotal(doPageResult.getTotal());
//        doPageResult.getList().forEach(item -> {
//            ResourceSiteManagementRespVO vo = BeanUtils.toBean(item, ResourceSiteManagementRespVO.class);
//            vo.setProvince(areaManager.getProvinceNameByCode(item.getProvince()));
//            vo.setCity(areaManager.getCityNameByCode(item.getCity()));
//            vo.setCounty(areaManager.getAreaNameByCode(item.getCounty()));
//            vo.setCountry(areaManager.getCountryNameByCode(item.getCountry()));
//            data.add(vo);
//        });
//        result.setList(data);
//        return result;

        // 角色权限适用项目map(key:绑定类型; value:绑定数据id列表)
        Map<Integer, Set<Long>> bizIdMap = new HashMap<>();

        // 获取当前用户的角色
        List<RoleRespDTO> roleList = roleApplyService.findRoleByUserId(WebFrameworkUtils.getLoginUserId());

        // 获取当前用户是否为超级管理员
        boolean superAdmin = false;
        for (RoleRespDTO roleRespDTO : roleList) {
            if (RoleUtil.isAdminRole(roleRespDTO.getCode())) {
                superAdmin = true;
                break;
            }
        }

        // 获取当前用户所属的角色选择的适用项目
        Set<Long> roleIdSet = roleList.stream().map(temp->temp.getId()).collect(Collectors.toSet());
        List<RoleApplyDO> roleApplyDOList = roleApplyService.listByRoleIdSet(roleIdSet);

        // 不同角色可能绑定了同一个项目，需要把不同角色的适用项目按绑定类型分组，每组的数据去重
        roleApplyDOList.stream().collect(Collectors.groupingBy(RoleApplyDO::getBindType)).forEach((bindType, tempRoleApplyDOList)->{
            Set<Long> bizIdSet = Optional.ofNullable(bizIdMap.get(bindType)).orElse(new HashSet<>());
            tempRoleApplyDOList.forEach(tempRoleApplyDO -> {
                Arrays.stream(Optional.ofNullable(tempRoleApplyDO.getBizId()).orElse("").split(",")).forEach(bizId->{
                    if (StringUtils.isNotBlank(bizId)) {
                        bizIdSet.add(Long.parseLong(bizId));
                    }
                });
            });
            bizIdMap.put(bindType, bizIdSet);
        });

        Integer bindType = ResourceSiteTypeMapRoleApplyBindTypeEnum.getBindType(pageReqVO.getPowerStationType());
        Set<Long> bizIdSet = Optional.ofNullable(bizIdMap.get(bindType)).orElse(new HashSet<>());

        String tempUpperLimit = pageReqVO.getUpperLimit();
        String tempLowerLimit = pageReqVO.getLowerLimit();
        if (StringUtils.isNotBlank(tempUpperLimit)) {
            pageReqVO.setUpperLimitValue(Double.valueOf(tempUpperLimit));
        }
        if (StringUtils.isNotBlank(tempLowerLimit)) {
            pageReqVO.setLowerLimitValue(Double.valueOf(tempLowerLimit));
        }

        int pageNo = pageReqVO.getPageNo()<1?1:pageReqVO.getPageNo();
        int pageSize = pageReqVO.getPageSize();


        List<ResourceSiteManagementDO> list = new ArrayList<>();
        Long total = 0L;

        if (superAdmin) {
            list = resourceSiteManagementMapper.selectResourceSitePage(pageReqVO,
                    (pageNo - 1) * pageSize, pageSize);
            total = resourceSiteManagementMapper.selectResourceSiteTotal(pageReqVO);
        } else {
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(bizIdSet)) {
                // 如果不是超级管理员并且bizIdSet又不是空的，需要将bizIdSet做为过滤条件
                pageReqVO.setBizIdSet(bizIdSet);
                list = resourceSiteManagementMapper.selectResourceSitePage(pageReqVO,
                        (pageNo - 1) * pageSize, pageSize);
                total = resourceSiteManagementMapper.selectResourceSiteTotal(pageReqVO);
            }
        }

        if (CollectionUtils.isEmpty(list)) {
            return PageResult.empty();
        }

        // 3. 转换返回结果
        PageResult<ResourceSiteManagementRespVO> result = new PageResult<>();
        List<ResourceSiteManagementRespVO> data = new ArrayList<>(list.size());
        result.setTotal(total);

        // 4. 处理数据转换
        list.forEach(item -> {
            ResourceSiteManagementRespVO vo = BeanUtils.toBean(item, ResourceSiteManagementRespVO.class);
            vo.setProvince(areaManager.getProvinceNameByCode(item.getProvince()));
            vo.setCity(areaManager.getCityNameByCode(item.getCity()));
            vo.setCounty(areaManager.getAreaNameByCode(item.getCounty()));
            vo.setCountry(areaManager.getCountryNameByCode(item.getCountry()));
            data.add(vo);
        });

        result.setList(data);
        return result;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importResourceSiteManagement(MultipartFile file) {
        //增加分布式锁，只能有一个导入
        if (!redisTemplate.opsForValue().setIfAbsent(PlanningDesignConstants.RESOURCES_SITE_MANAGEMENT_MANAGE_IMPORT_KEY, 1, 60, TimeUnit.SECONDS)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_EXISTS_ERROR);
        }
        try {
            if (Objects.isNull(file)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_NO_FILE_ERROR);
            }
            List<ResourceSiteManagementImportModel> importDataList = EasyExcel.read(file.getInputStream(), ResourceSiteManagementImportModel.class, null).sheet().headRowNumber(1).doReadSync();
            if (CollectionUtils.isEmpty(importDataList)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
            }
            //校验数据
            List<ResourceSiteManagementDO> resourceSiteManagementDOList = validateProjectManageExistsImport(importDataList);
            List<FileTreeDO> treeDOList = new ArrayList<>();
            resourceSiteManagementDOList.forEach(item -> {
                item.setPowerStationType(4);
                resourceSiteManagementMapper.insert(item);
                FileTreeDO treeDO = new FileTreeDO();
                treeDO.setTreeLevel(1);
                treeDO.setProjectId(item.getId());
                treeDO.setTreeName(item.getPowerStationName());
                treeDO.setType("0");
                treeDOList.add(treeDO);
            });
            //插入文件树
            fileTreeService.insertBatch(treeDOList,"0");
            //resourceSiteManagementMapper.insertBatch(resourceSiteManagementDOList);

        } catch (IOException e) {
            log.error("importRainfallStation--->error.", e);
        } finally {
            redisTemplate.delete(PlanningDesignConstants.RESOURCES_SITE_MANAGEMENT_MANAGE_IMPORT_KEY);
        }
    }

    @Override
    public ResourceSiteManagementDO findEntityByPowerCode(String powerStationCode) {
        return resourceSiteManagementMapper.selectOne("power_code", powerStationCode);
    }

    //站名编码
    @Override
    public ResourceSiteManagementDO findEntityByPowerStationName(String powerStationName) {
        QueryWrapper<ResourceSiteManagementDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("power_station_name", powerStationName)
                .eq("power_station_type", 4)
        .eq("deleted", 0);
        return resourceSiteManagementMapper.selectOne(queryWrapper,false);
    }

    @Override
    public List<ResourceSiteManagementDO> findAllResourceSiteManagement() {
        return resourceSiteManagementMapper.selectAllResourceSiteManagement();
    }

    @Override
    public List<ResourceSiteManagementDO> listEntityByIds(List<Long> projectIdList) {
        return resourceSiteManagementMapper.selectBatchIds(projectIdList);
    }

    @Override
    public List<ResourceSiteManagementDO> selectListByStationNames(List<String> stationNames) {
        return resourceSiteManagementMapper.selectListByStationNames(stationNames);
    }

    @Override
    public void batchInsert(List<ResourceSiteManagementDO> resourceSiteManagementDOList) {
        resourceSiteManagementMapper.insertList(resourceSiteManagementDOList);
    }

    @Override
    public void deleteTemporaryData() {
        resourceSiteManagementMapper.deleteTemporaryData();
    }

    @Override
    public List<ResourceSiteManagementDO> selectListTemporaryData() {
        return resourceSiteManagementMapper.selectListTemporaryData();
    }

    @Override
    public ResourceSiteManagementDO selectOneByName(String powerStationName) {
        QueryWrapper<ResourceSiteManagementDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("power_station_name",powerStationName);
        return resourceSiteManagementMapper.selectOne(queryWrapper,false);
    }

    @Override
    public void updateResourceSiteManagement(ResourceSiteManagementDO projectDO) {
        // 校验存在
        validateProjectExists(projectDO.getId());
        ResourceSiteManagementDO stationDO = findEntityByPowerStationName(projectDO.getPowerStationName());
        if (Objects.nonNull(stationDO) && !Objects.equals(stationDO.getId(), projectDO.getId())) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_NAME_SAVE_EXISTS_ERROR);
        }
        // 更新
        resourceSiteManagementMapper.updateById(projectDO);
    }

    @Override
    public int insert(ResourceSiteManagementDO projectDO) {
        return resourceSiteManagementMapper.insert(projectDO);
    }

    @Override
    public void updateCreator() {
        resourceSiteManagementMapper.updateCreator();
    }



    /**
     * 校验导入的项目编码是否存在
     *
     * @param importDataList
     */
    private List<ResourceSiteManagementDO> validateProjectManageExistsImport(List<ResourceSiteManagementImportModel> importDataList) {
        if (CollectionUtils.isEmpty(importDataList)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
        }

        /** 省市县间需用"/"隔开，如:河南省/郑州市/金水区，其中省为必填详细位置为区后的详细信息，内容为选填且最多录入30字;
         * 东经的格式为:113.13213221，导入最高输入小数点后9位，超出部分不寻入;
         * 北纬的格式为:34.13213221，最高输入小数点后9位，超出部分不
         * 表头标绿的部分为必填项。
         *
         * 且需保证省市县文字正确，否则会导入失败
         */

        List<ResourceSiteManagementDO> resourceSiteManagementDOList = new ArrayList<>();  // 入库数据集合
        List<String> powerStationNameList = new ArrayList<>();
        for (int i = 0; i < importDataList.size(); i++) {
            ResourceSiteManagementImportModel importModel = importDataList.get(i);

            //站址名称
            if (StringUtils.isBlank(importModel.getPowerStationName())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE +1);
            }
            //校验站名重复
            if (powerStationNameList.contains(importModel.getPowerStationName())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_REPEAT_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE +1);
            }
            //校验站名存在
            if (Objects.nonNull(findEntityByPowerStationName(importModel.getPowerStationName()))) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_EXISTS_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE +1);
            }
            //校验电站地点
            if (StringUtils.isBlank(importModel.getProvince())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_PROVINCE_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE );
            }
            if (StringUtils.isBlank(importModel.getCity())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_CITY_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE );
            }
            if (StringUtils.isBlank(importModel.getCounty())) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_COUNTY_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE );
            }
            if (StringUtils.isBlank(importModel.getLongitude())){
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_LONGITUDE_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE );
            }
            if (StringUtils.isBlank(importModel.getLatitude())){
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_LATITUDE_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE );
            }
            if (StringUtils.isBlank(importModel.getDevelopWay())){
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_DEVELOP_WAY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE );
            }
            if (StringUtils.isBlank(importModel.getComplianceRegulations())){
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_COMPLIANCE_REGULATIONS_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE );
            }
            if (StringUtils.isBlank(importModel.getDesignStage())){
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_DESIGN_SYSTEM_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE );
            }
            if (StringUtils.isBlank(importModel.getTotalCapacity())){
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_TOTAL_CAPACITY_EMPTY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE );
            }


            //String[] provinceCityCounty = importModel.getProvinceCityCounty().split("/");
            //处理省市县
            String countryName = importModel.getCountry();
            String provinceName = importModel.getProvince();
            String cityName = importModel.getCity();
            String countyName = StringUtils.isNotBlank(importModel.getCounty()) ? importModel.getCounty() : null;

            String country = areaManager.getCountryCodeByName(countryName);
            String province = areaManager.getProvinceCodeByFullName(provinceName);
            String city = areaManager.getCityCodeByName(provinceName,cityName);
            String county = areaManager.getAreaCodeByName(countyName);

            if (StringUtils.isBlank(country)){
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_COUNTRY_MATCH_ERROR,i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1,countryName);
            }

            if (StringUtils.isNotBlank(provinceName) && StringUtils.isBlank(province)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_PROVINCE_MATCH_ERROR,i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1, provinceName);
            }

            if (StringUtils.isNotBlank(cityName) && StringUtils.isBlank(city)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_CITY_MATCH_ERROR,i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1, provinceName, cityName);
            }

            if (StringUtils.isNotBlank(countyName) && StringUtils.isBlank(county)) {
                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_COUNTY_MATCH_ERROR,i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1, provinceName, cityName, countyName);
            }



            // 3. 经纬度校验
            validateLatitudeAndLongitude(importModel.getLatitude(), importModel.getLongitude());
            // 5. 重要参数校验
            validateImportantParams(importModel);
//            if (StringUtils.isBlank(province)){
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_PROVINCE_MATCH_ERROR,i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1,provinceName);
//            }
//            if (StringUtils.isNotBlank(cityName) && (StringUtils.isBlank(city) || !areaManager.checkProvinceCityExists(province, city))) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_CITY_MATCH_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1, provinceName, cityName);
//            }
//            if (StringUtils.isNotBlank(countyName) && (StringUtils.isBlank(county) || !areaManager.checkCityAreaExists(city, county))) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_COUNTY_MATCH_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1, provinceName, cityName, countyName);
//            }
//            // 详细地址的长度
//            if (StringUtils.isNotBlank(importModel.getAddress()) && importModel.getAddress().length() > 30) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_ACCESS_MATCH_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
//            }
//            //检验工作深度
//            if (StringUtils.isBlank(importModel.getDesignStage())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_DESIGN_SYSTEM_EMPTY_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
//            }
//
//            if (importModel.getDesignStage().length()>10) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_DESIGN_LENGTH_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
//            }
//            //检验是否纳规
//            if (StringUtils.isBlank(importModel.getComplianceRegulations())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_COMPLIANCE_REGULATIONS_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
//            }
//            //检验工作方式
//            if (StringUtils.isBlank(importModel.getDevelopWay())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_DEVELOP_WAY_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
//            }
//            if (importModel.getDevelopWay().length() > 10) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_DEVELOP_WAY_LENGTH_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
//            }
//            //校验东经
//            if (StringUtils.isBlank(importModel.getLongitude())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_LONGITUDE_EMPTY_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
//            }
//            importModel.setLongitude(importModel.getLongitude().replace("°", ".").replace("'", ""));
//            //校验北纬
//            if (StringUtils.isBlank(importModel.getLatitude())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_LATITUDE_EMPTY_ERROR, i + PlanningDesignConstants.STATION_RAINFALL_IMPORT_HEAD_LINE + 1);
//            }
//            importModel.setLatitude(importModel.getLatitude().replace("°", ".").replace("'", ""));
            // 经度校验：范围(-180,180)，保留6位小数

//            //总装机容量
//            if (StringUtils.isBlank(importModel.getTotalCapacity())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_TOTAL_CAPACITY_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
//            }
//
//            //额定水头
//            if (StringUtils.isBlank(importModel.getRatedHead())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_PROJECT_RATED_HEAD_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
//            }
//            //连续满发小时数
//            if (StringUtils.isBlank(importModel.getFullShippingHours())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_FULL_SHIPPING_HOURS_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
//            }
//            //距高比
//            if (StringUtils.isBlank(importModel.getDistanceToHeightRatio())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_DISTANCE_TO_HEIGHT_RATIO_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
//            }
//            //单位千瓦静态投资(元/kW)
//            if (StringUtils.isBlank(importModel.getStaticInvestment())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_STATIC_INVESTMENT_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
//            }
//            //水源条件
//            if (StringUtils.isBlank(importModel.getWaterSourceConditions())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_WATER_SOURCE_CONDITIONS_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
//            }
//            //设计单位
//            if (StringUtils.isBlank(importModel.getDesignUnit())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_DESIGN_UNIT_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
//            }
//            //投资单位
//            if (StringUtils.isBlank(importModel.getLnvestmentUnit())) {
//                throw exception(ErrorCodeConstants.EXCEL_IMPORT_RESOURCE_SITE_LNVESTMENT_UNIT_ERROR, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1);
//            }
//            //字符长度校验
//            int limit=30;
//            Object[] objects = cn.powerchina.bjy.cloud.institute.hydrologic.util.StringUtils.areAllPropertiesWithinLimit(importModel, limit, map);
//            if(BooleanUtil.isFalse((Boolean) objects[0])){
//                throw exception(ErrorCodeConstants.LENGTH_EXCEEDS_LIMIT, i + PlanningDesignConstants.PROJECT_MANAGE_IMPORT_HEAD_LINE + 1,objects[1],limit);
//            }

            ResourceSiteManagementDO resourceSiteManagementDO = new ResourceSiteManagementDO();
            org.springframework.beans.BeanUtils.copyProperties(importDataList.get(i), resourceSiteManagementDO);
            resourceSiteManagementDO.setProvince(province);
            resourceSiteManagementDO.setCity(city);
            resourceSiteManagementDO.setCountry(country);
            resourceSiteManagementDO.setCounty(county);
            powerStationNameList.add(importDataList.get(i).getPowerStationName());
            resourceSiteManagementDOList.add(resourceSiteManagementDO);
        }
        return resourceSiteManagementDOList;
    }

    private void validateImportantParams(ResourceSiteManagementImportModel importModel) {

        // 装机容量：数值型，整数
        String totalCapacity = importModel.getTotalCapacity();
        if (StringUtils.isBlank(totalCapacity)) {
            return;
        }
        try {
            BigDecimal capacity = new BigDecimal(totalCapacity.trim());
            if (!isInteger(capacity)) {
                throw exception(RESOURCE_SITE_INSTALLED_CAPACITY_INVALID);
            }
        } catch (NumberFormatException e) {
            throw exception(RESOURCE_SITE_INSTALLED_CAPACITY_FORMAT_INVALID);
        }


        // 连续满发小时数：数值型，保留2位小数
        String fullShippingHours = importModel.getFullShippingHours();
        if (StringUtils.isBlank(fullShippingHours)) {
            return;
        }
        try {
            BigDecimal hours = new BigDecimal(fullShippingHours.trim());
            if (getDecimalPlaces(hours) > 2) {
                throw exception(RESOURCE_SITE_CONTINUOUS_FULL_HOURS_DECIMAL_INVALID);
            }
        } catch (NumberFormatException e) {
            throw exception(RESOURCE_SITE_CONTINUOUS_FULL_HOURS_FORMAT_INVALID);
        }

        // 额定水头：数值型，保留1位小数
        String ratedHead = importModel.getRatedHead();
        if (StringUtils.isBlank(ratedHead)) {
            return;
        }
        try {
            BigDecimal head = new BigDecimal(ratedHead.trim());
            if (getDecimalPlaces(head) > 1) {
                throw exception(RESOURCE_SITE_RATED_HEAD_DECIMAL_INVALID);
            }
        } catch (NumberFormatException e) {
            throw exception(RESOURCE_SITE_RATED_HEAD_FORMAT_INVALID);
        }


        // 距离比：数值型，保留1位小数
        String distanceToHeightRatio = importModel.getDistanceToHeightRatio();
        if (StringUtils.isBlank(distanceToHeightRatio)) {
            return;
        }
        try {
            BigDecimal ratio = new BigDecimal(distanceToHeightRatio.trim());
            if (getDecimalPlaces(ratio) > 2) {
                throw exception(RESOURCE_SITE_DISTANCE_RATIO_DECIMAL_INVALID);
            }
        } catch (NumberFormatException e) {
            throw exception(RESOURCE_SITE_DISTANCE_RATIO_FORMAT_INVALID);
        }


        // 单位千瓦静态投资：数值型，保留2位小数
        String staticInvestment = importModel.getStaticInvestment();
        if (StringUtils.isBlank(staticInvestment)) {
            return;
        }
        try {
            BigDecimal investment = new BigDecimal(staticInvestment.trim());
            if (getDecimalPlaces(investment) > 2) {
                throw exception(RESOURCE_SITE_UNIT_KW_STATIC_INVESTMENT_DECIMAL_INVALID);
            }
        } catch (NumberFormatException e) {
            throw exception(RESOURCE_SITE_UNIT_KW_STATIC_INVESTMENT_FORMAT_INVALID);
        }
    }

    private void validateLatitudeAndLongitude(String latitudeStr, String longitudeStr) {
        // 经度校验：范围(-180,180)，保留6位小数
        if (StringUtils.isBlank(longitudeStr)) {
            return;
        }
        BigDecimal longitude;
        try {
            longitude = new BigDecimal(longitudeStr.trim());
        } catch (NumberFormatException e) {
            throw exception(RESOURCE_SITE_LONGITUDE_FORMAT_INVALID);
        }

        if (longitude.compareTo(new BigDecimal("-180")) <= 0
                || longitude.compareTo(new BigDecimal("180")) >= 0) {
            throw exception(RESOURCE_SITE_LONGITUDE_INVALID);
        }
        if (getDecimalPlaces(longitude) > 6) {
            throw exception(RESOURCE_SITE_LONGITUDE_DECIMAL_INVALID);
        }

        // 纬度校验：范围(-90,90)，保留6位小数
        if (StringUtils.isBlank(latitudeStr)) {
            return;
        }

        BigDecimal latitude;
        try {
            latitude = new BigDecimal(latitudeStr.trim());
        } catch (NumberFormatException e) {
            throw exception(RESOURCE_SITE_LATITUDE_FORMAT_INVALID);
        }

        if (latitude.compareTo(new BigDecimal("-90")) <= 0
                || latitude.compareTo(new BigDecimal("90")) >= 0) {
            throw exception(RESOURCE_SITE_LATITUDE_INVALID);
        }
        if (getDecimalPlaces(latitude) > 6) {
            throw exception(RESOURCE_SITE_LATITUDE_DECIMAL_INVALID);
        }
    }

    /**
     * 获取小数位数
     */
    private int getDecimalPlaces(BigDecimal number) {
        String str = number.stripTrailingZeros().toPlainString();
        int index = str.indexOf(".");
        return index < 0 ? 0 : str.length() - index - 1;
    }

    /**
     * 判断是否为整数
     */
    private boolean isInteger(BigDecimal number) {
        return number.stripTrailingZeros().scale() <= 0;
    }


    @Override
    public List<StationInfoRespIndexVO> buildResourceSiteTree(boolean isSuperAdmin, Long loginUserId) {
        // 1. 查询站点数据
        List<ResourceSiteManagementDO> sites = resourceSiteManagementMapper.selectAllResourceSiteManagement();
        if (CollUtil.isEmpty(sites)) {
            // 如果查询结果为空，应该返回空列表而不是null
            return new ArrayList<>();
        }

        // 2. 按站点类型分组
        Map<Integer, List<ResourceSiteManagementDO>> siteTypeMap = sites.stream()
                .filter(site -> site.getPowerStationType() != null)
                .collect(Collectors.groupingBy(ResourceSiteManagementDO::getPowerStationType));

        // 打印日志，查看实际的类型值
        log.info("数据库中的站点类型: {}", siteTypeMap.keySet());
        log.info("枚举中的站点类型 - 抽水蓄能: {}, 常规水电: {}",
                StationEnum.PUMPED.getType(),
                StationEnum.CONVENTIONAL.getType());

        // 3. 构建树形结构
        List<StationInfoRespIndexVO> result = new ArrayList<>();

        // 处理抽水蓄能站点
        if (siteTypeMap.containsKey(StationEnum.PUMPED.getType())) {
            StationInfoRespIndexVO pumpStorage = new StationInfoRespIndexVO();
            pumpStorage.setStationType(1);
            pumpStorage.setId(String.valueOf(generateStationId("抽水蓄能"))); // 转换为 String
            pumpStorage.setStationName(sites.get(0).getPowerStationName());
            pumpStorage.setChildren(buildCountryLevel(siteTypeMap.get(1)));
            result.add(pumpStorage);
        }

        // 处理常规水电站点
        if (siteTypeMap.containsKey(StationEnum.CONVENTIONAL.getType())) {
            StationInfoRespIndexVO conventional = new StationInfoRespIndexVO();
            conventional.setStationType(2);
            conventional.setId(String.valueOf(generateStationId("常规水电")));
            conventional.setStationName(sites.get(0).getPowerStationName());
            conventional.setChildren(buildCountryLevel(siteTypeMap.get("常规水电")));
            result.add(conventional);
        }

        return result;
    }

    // 构建国家层级
    private List<StationInfoRespIndexVO.StationInfo> buildCountryLevel(List<ResourceSiteManagementDO> sites) {
        Map<String, List<ResourceSiteManagementDO>> countryMap = sites.stream()
                .collect(Collectors.groupingBy(ResourceSiteManagementDO::getCountry));

        return countryMap.entrySet().stream().map(entry -> {
            StationInfoRespIndexVO.StationInfo countryNode = new StationInfoRespIndexVO.StationInfo();
            countryNode.setId(generateLocationId());
            countryNode.setStationName(entry.getKey());
            countryNode.setChildren(buildProvinceLevel(entry.getValue()));
            return countryNode;
        }).collect(Collectors.toList());
    }

    // 构建省份层级
    private List<StationInfoRespIndexVO.StationInfo> buildProvinceLevel(List<ResourceSiteManagementDO> sites) {
        Map<String, List<ResourceSiteManagementDO>> provinceMap = sites.stream()
                .collect(Collectors.groupingBy(ResourceSiteManagementDO::getProvince));

        return provinceMap.entrySet().stream().map(entry -> {
            StationInfoRespIndexVO.StationInfo provinceNode = new StationInfoRespIndexVO.StationInfo();

            provinceNode.setId(generateLocationId());
            provinceNode.setStationName(entry.getKey());
            provinceNode.setChildren(buildCityLevel(entry.getValue()));
            return provinceNode;
        }).collect(Collectors.toList());
    }

    // 构建城市层级
    private List<StationInfoRespIndexVO.StationInfo> buildCityLevel(List<ResourceSiteManagementDO> sites) {
        Map<String, List<ResourceSiteManagementDO>> cityMap = sites.stream()
                .collect(Collectors.groupingBy(ResourceSiteManagementDO::getCity));

        return cityMap.entrySet().stream().map(entry -> {
            StationInfoRespIndexVO.StationInfo cityNode = new StationInfoRespIndexVO.StationInfo();
            cityNode.setId(generateLocationId());
            cityNode.setStationName(entry.getKey());
            cityNode.setChildren(buildStationLevel(entry.getValue()));
            return cityNode;
        }).collect(Collectors.toList());
    }

    // 构建站点层级
    private List<StationInfoRespIndexVO.StationInfo> buildStationLevel(List<ResourceSiteManagementDO> sites) {
        return sites.stream().map(site -> {
            StationInfoRespIndexVO.StationInfo stationNode = new StationInfoRespIndexVO.StationInfo();
            stationNode.setId(String.valueOf(generateStationId(site.getPowerStationName())));
            stationNode.setStationName(site.getPowerStationName());
            stationNode.setStationId(String.valueOf(site.getId()));
            return stationNode;
        }).collect(Collectors.toList());
    }


    // 生成站点ID的方法
    private Long generateStationId(String stationType) {
        if ("抽水蓄能".equals(stationType)) {
            return STATION_ID_GENERATOR.getAndIncrement() + PUMP_STORAGE_START_ID;
        } else if ("常规水电".equals(stationType)) {
            return STATION_ID_GENERATOR.getAndIncrement() + CONVENTIONAL_START_ID;
        }
        return STATION_ID_GENERATOR.getAndIncrement();
    }

    // 使用雪花算法生成ID
    private String generateLocationId() {
        return IdUtil.getSnowflakeNextIdStr(); // 直接获取字符串形式的ID
    }
}