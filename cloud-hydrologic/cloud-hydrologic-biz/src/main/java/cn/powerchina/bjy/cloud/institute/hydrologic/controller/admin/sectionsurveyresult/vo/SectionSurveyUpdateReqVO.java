package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.model.YearForm;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SectionSurveyUpdateReqVO {
    /**
     * 是否删除(true-删除 false-不删除)
     */
    @Schema(description = "是否删除")
    private Boolean isDelete;

    /**
     * 水文站id
     */
    @JsonProperty("stationId") // 添加JsonProperty注解来映射前端字段
    private Long stationId;

    /**
     * 站点类型
     */
    @JsonProperty("stationType")
    private Integer stationType;

    /**
     * 数据类型
     */
    @JsonProperty("dataType")
    private Integer dataType;

    /**
     * 施测日期
     */
    private String surveyDate;

    /**
     * 断面名称
     */
    private String sectionName;

    /**
     * 测时水位
     */
    private BigDecimal waterLevel;

    @JsonProperty("year")
    private Integer year;


    /**
     * 数据列表
     */
    private List<YearForm> dataList;

}