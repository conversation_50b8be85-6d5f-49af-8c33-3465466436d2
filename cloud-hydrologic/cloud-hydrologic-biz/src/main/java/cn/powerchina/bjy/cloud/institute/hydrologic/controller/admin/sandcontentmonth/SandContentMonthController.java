package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentmonth;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentmonth.vo.SandContentMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentmonth.vo.SandContentMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentmonth.vo.SandContentMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sandcontentmonth.SandContentMonthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台-含沙量-月含沙量特征值")
@RestController
@RequestMapping("/plan/hydrologic/sand/content/month")
@Validated
public class SandContentMonthController {

    @Resource
    private SandContentMonthService sandContentMonthService;

    @PutMapping("/update")
    @Operation(summary = "更新水文站-输沙率-月含沙量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:sand-content-month:update')")
    public CommonResult<Boolean> updateSandContentMonth(@Valid @RequestBody SandContentMonthSaveReqVO updateReqVO) {
        sandContentMonthService.updateSandContentMonth(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得-月含沙量特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:sand-content-month:query')")
    public CommonResult<PageResult<SandContentMonthRespVO>> getSandContentMonthPage(@Valid SandContentMonthPageReqVO pageReqVO) {
        PageResult<SandContentMonthRespVO> pageResult = sandContentMonthService.getSandContentMonthPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-月含沙量特征值分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_SAND_MONTH)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<SandContentMonthRespVO>> getSandContentMonthDataPage(@Valid SandContentMonthPageReqVO pageReqVO) {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return success(PageResult.empty());
        }
        pageReqVO.setLogId(null);
        PageResult<SandContentMonthRespVO> pageResult = sandContentMonthService.getSandContentMonthPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出月含沙量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportSandContentMonthExcel(@Valid SandContentMonthPageReqVO pageReqVO,
                                            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        sandContentMonthService.exportExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出月含沙量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportSandContentMonthDataExcel(@Valid SandContentMonthPageReqVO pageReqVO,
                                                HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        sandContentMonthService.exportExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入月含沙量特征 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:flow-info:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        sandContentMonthService.importData(file, stationId);
        return success(true);
    }

}