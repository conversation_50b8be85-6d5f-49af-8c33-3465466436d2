package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeresult.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水文站-实测悬移质输沙率成果 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DischargeResultRespVO {

    @Schema(description = "主键",example = "16312")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id",example = "5255")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private String month;

    @Schema(description = "日")
    @ExcelProperty("日")
    private String day;

    @Schema(description = "起始时分")
    @ExcelProperty("起始时分")
    private String startHoursMinute;

    @Schema(description = "起始时分")
    @ExcelProperty("起始时分")
    private String endHoursMinute;

    @Schema(description = "流量(m3/s)")
    @ExcelProperty("流量(m3/s)")
    private String flow;

    @Schema(description = "断面输沙率(kg/s)")
    @ExcelProperty("断面输沙率(kg/s)")
    private String sectionTransportRate;

    @Schema(description = "含沙量断面平均(kg/m3)")
    @ExcelProperty("含沙量断面平均(kg/m3)")
    private String sectionAverageValue;

    @Schema(description = "含沙量单样(kg/m3)")
    @ExcelProperty("含沙量单样(kg/m3)")
    private String sectionSampleValue;

    @Schema(description = "测验方法-断面平均含沙量")
    @ExcelProperty("测验方法-断面平均含沙量")
    private String testingMethodsAverageValue;

    @Schema(description = "测验方法-单样含沙量")
    @ExcelProperty("测验方法-单样含沙量")
    private String testingMethodsSampleValue;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}