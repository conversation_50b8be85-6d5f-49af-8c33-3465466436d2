package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Schema(description = "管理后台 - 降水-各时段最大降水量(分钟时段)新增/修改 Request VO")
@Data
public class TidelevelYearUpdateReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "是否删除 true是 ")
    private Boolean isDelete;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<TidelevelYearUpdateData> dataList;

    @Schema(description = "要删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "管理后台 - 降水-各时段最大降水量(小时时段)数据")
    @Data
    public static class TidelevelYearUpdateData {

        @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "7528")
        private Long id;

        @Schema(description = "年")
        private Integer year;

        @Schema(description = "一月")
        private String value1;

        @Schema(description = "二月")
        private String value2;

        @Schema(description = "三月")
        private String value3;

        @Schema(description = "四月")
        private String value4;

        @Schema(description = "五月")
        private String value5;

        @Schema(description = "六月")
        private String value6;

        @Schema(description = "七月")
        private String value7;

        @Schema(description = "八月")
        private String value8;

        @Schema(description = "九月")
        private String value9;

        @Schema(description = "十月")
        private String value10;

        @Schema(description = "十一月")
        private String value11;

        @Schema(description = "十二月")
        private String value12;

        @Schema(description = "全年")
        private String value13;

        @Schema(description = "全年")
        private String value14;

        @Schema(description = "全年")
        private String value15;

        @Schema(description = "全年")
        private String value16;

        @Schema(description = "全年")
        private String value17;

        @Schema(description = "全年")
        private String value18;
        /**
         * 备注
         */
        @Schema(description = "备注")
        private String remark;
        /**
         * 记录时间
         */
        @Schema(description = "记录时间")
        private LocalDate currentDay;
        /**
         * 版本
         */
        @Schema(description = "版本")
        private Integer version;
        /**
         * 水文站id
         */
        @Schema(description = "水文站id")
        private Long stationId;
        /**
         * 站点类型，1：雨量站，2：水文站
         */
        @Schema(description = "站点类型，1：雨量站，2：水文站")
        private Integer stationType;
        /**
         * 数据类型
         */
        @Schema(description = "数据类型")
        private Integer dataType;
    }

}