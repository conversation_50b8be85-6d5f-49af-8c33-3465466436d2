package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.granininfoyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.granininfoyear.vo.GrainInfoYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.granininfoyear.vo.GrainInfoYearRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.granininfoyear.vo.GrainInfoYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.graininfoyear.GrainInfoYearService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 水文-年平均悬移质颗粒级配")
@RestController
@RequestMapping("/plan/hydrologic/grain/info/year")
@Validated
public class GrainInfoYearController {

    @Resource
    private GrainInfoYearService grainInfoYearService;

    @PutMapping("/update")
    @Operation(summary = "更新年平均悬移质颗粒级配")
//    @PreAuthorize("@ss.hasPermission('hydrologic:grain-info:update')")
    public CommonResult<Boolean> updateGrainInfo(@Valid @RequestBody GrainInfoYearSaveReqVO updateReqVO) {
        grainInfoYearService.updateGrainInfo(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得年平均悬移质颗粒级配分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:grain-info:query')")
    public CommonResult<PageResult<GrainInfoYearRespVO>> getGrainInfoPage(@Valid GrainInfoYearPageReqVO pageReqVO) {
        PageResult<GrainInfoYearRespVO> pageResult = grainInfoYearService.getGrainInfoPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-年平均悬移质颗粒级配分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_GRAIN_INFO_YEAR)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<GrainInfoYearRespVO>> getGrainInfoDataPage(@Valid GrainInfoYearPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        PageResult<GrainInfoYearRespVO> pageResult = grainInfoYearService.getGrainInfoPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（记录）导出年平均悬移质颗粒级配 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportGrainInfoExcel(@Valid GrainInfoYearPageReqVO pageReqVO,
                                     HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        grainInfoYearService.exportGrainInfoExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出年平均悬移质颗粒级配 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportGrainInfoDataExcel(@Valid GrainInfoYearPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        grainInfoYearService.exportGrainInfoExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入年平均悬移质颗粒级配 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:grain-info:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId) throws IOException {
        grainInfoYearService.importData(file, stationId);
        return success(true);
    }

}