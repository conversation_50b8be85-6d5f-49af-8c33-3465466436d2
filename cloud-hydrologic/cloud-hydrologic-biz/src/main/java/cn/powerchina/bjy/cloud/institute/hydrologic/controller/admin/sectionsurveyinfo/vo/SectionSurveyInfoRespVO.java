package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyinfo.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 实测大断面成果年度基本信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SectionSurveyInfoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "35")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30457")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("站点类型")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "版本号")
    @ExcelProperty("版本号")
    private Integer version;

    @Schema(description = "施测年份", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("施测年份")
    private Integer year;

    @Schema(description = "是否最新（1最新0历史）")
    @ExcelProperty("是否最新（1最新0历史）")
    private Integer latest;

    @Schema(description = "施测日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("施测日期")
    @JsonFormat(pattern = "M月d日")
    private LocalDate surveyDate;

    @Schema(description = "断面名称及位置，最多50字", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("断面名称及位置，最多50字")
    private String sectionName;

    @Schema(description = "测时水位，保留2位小数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("测时水位，保留2位小数")
    private BigDecimal waterLevel;

    @Schema(description = "汛期类型：1-汛前，2-汛后", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("汛期类型：1-汛前，2-汛后")
    private Integer periodType;

    @Schema(description = "附注", example = "你猜")
    @ExcelProperty("附注")
    private String remark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}