package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodminute.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 降水-各时段最大降水量(分钟时段) Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationRainPeriodMinuteRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "10min降水量")
    @ExcelProperty("10min降水量")
    private String valueDay10;

    @Schema(description = "10min开始月日")
    @ExcelProperty("10min开始月日")
    private String monthDayStart10;

    @Schema(description = "20min降水量")
    @ExcelProperty("20min降水量")
    private String valueDay20;

    @Schema(description = "20min开始月日")
    @ExcelProperty("20min开始月日")
    private String monthDayStart20;

    @Schema(description = "30min降水量")
    @ExcelProperty("30min降水量")
    private String valueDay30;

    @Schema(description = "30min开始月日")
    @ExcelProperty("30min开始月日")
    private String monthDayStart30;

    @Schema(description = "45min降水量")
    @ExcelProperty("45min降水量")
    private String valueDay45;

    @Schema(description = "45min开始月日")
    @ExcelProperty("45min开始月日")
    private String monthDayStart45;

    @Schema(description = "60min降水量")
    @ExcelProperty("60min降水量")
    private String valueDay60;

    @Schema(description = "60min开始月日")
    @ExcelProperty("60min开始月日")
    private String monthDayStart60;

    @Schema(description = "90min降水量")
    @ExcelProperty("90min降水量")
    private String valueDay90;

    @Schema(description = "90min开始月日")
    @ExcelProperty("90min开始月日")
    private String monthDayStart90;

    @Schema(description = "120min降水量")
    @ExcelProperty("120min降水量")
    private String valueDay120;

    @Schema(description = "120min开始月日")
    @ExcelProperty("120min开始月日")
    private String monthDayStart120;

    @Schema(description = "180min降水量")
    @ExcelProperty("180min降水量")
    private String valueDay180;

    @Schema(description = "180min开始月日")
    @ExcelProperty("180min开始月日")
    private String monthDayStart180;

    @Schema(description = "240min降水量")
    @ExcelProperty("240min降水量")
    private String valueDay240;

    @Schema(description = "240min开始月日")
    @ExcelProperty("240min开始月日")
    private String monthDayStart240;

    @Schema(description = "360min降水量")
    @ExcelProperty("360min降水量")
    private String valueDay360;

    @Schema(description = "360min开始月日")
    @ExcelProperty("360min开始月日")
    private String monthDayStart360;

    @Schema(description = "540min降水量")
    @ExcelProperty("540min降水量")
    private String valueDay540;

    @Schema(description = "540min开始月日")
    @ExcelProperty("540min开始月日")
    private String monthDayStart540;

    @Schema(description = "720min降水量")
    @ExcelProperty("720min降水量")
    private String valueDay720;

    @Schema(description = "720min开始月日")
    @ExcelProperty("720min开始月日")
    private String monthDayStart720;

    @Schema(description = "1440min降水量")
    @ExcelProperty("1440min降水量")
    private String valueDay1440;

    @Schema(description = "1440min开始月日")
    @ExcelProperty("1440min开始月日")
    private String monthDayStart1440;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}