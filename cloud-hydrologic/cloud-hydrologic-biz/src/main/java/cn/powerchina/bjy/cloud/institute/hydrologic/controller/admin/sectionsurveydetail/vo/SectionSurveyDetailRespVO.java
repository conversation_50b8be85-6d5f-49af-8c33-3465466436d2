package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveydetail.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 实测大断面成果垂线明细数据 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SectionSurveyDetailRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5217")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8778")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("站点类型")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "施测年份", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("施测年份")
    private Integer year;

    @Schema(description = "汛期类型：1-汛前，2-汛后", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("汛期类型：1-汛前，2-汛后")
    private Integer periodType;

    @Schema(description = "垂线号，不大于4个字", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("垂线号，不大于4个字")
    private String verticalNo;

    @Schema(description = "起点距，5位整数+1位小数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("起点距，5位整数+1位小数")
    private BigDecimal startDistance;

    @Schema(description = "河底高程，4位整数+2位小数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("河底高程，4位整数+2位小数")
    private BigDecimal riverbedElevation;

    @Schema(description = "备注/附注", example = "你猜")
    @ExcelProperty("备注/附注")
    private String remark;

    @Schema(description = "版本号")
    @ExcelProperty("版本号")
    private Integer version;

    @Schema(description = "是否最新（1最新0历史）")
    @ExcelProperty("是否最新（1最新0历史）")
    private Integer latest;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}