package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelguarantee.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 水文站-水位-各保证率水位新增/修改 Request VO")
@Data
public class WaterLevelGuaranteeSaveReqVO {

    @Schema(description = "主键", example = "754")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32149")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "年")
    private String year;

    @Schema(description = "最高")
    private String maxValue;

    @Schema(description = "第15天")
    private String day15;

    @Schema(description = "第30天")
    private String day30;

    @Schema(description = "第90天")
    private String day90;

    @Schema(description = "第180天")
    private String day180;

    @Schema(description = "第270天")
    private String day270;

    @Schema(description = "最低")
    private String minValue;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer latest;

}