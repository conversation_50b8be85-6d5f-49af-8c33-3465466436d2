package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationdatalog.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 站点数据更新记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationDataLogRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "创建者姓名")
    @ExcelProperty("创建者姓名")
    private String creatorName;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}