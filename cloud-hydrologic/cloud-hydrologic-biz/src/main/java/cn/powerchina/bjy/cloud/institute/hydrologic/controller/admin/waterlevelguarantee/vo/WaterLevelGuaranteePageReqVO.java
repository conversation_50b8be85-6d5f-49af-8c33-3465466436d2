package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelguarantee.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 水文站-水位-各保证率水位分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WaterLevelGuaranteePageReqVO extends PageParam {

    @Schema(description = "水文站id", example = "32149")
    private Long stationId;

    @Schema(description = "年")
    private String year;

    @Schema(description = "最高")
    private String maxValue;

    @Schema(description = "第15天")
    private String day15;

    @Schema(description = "第30天")
    private String day30;

    @Schema(description = "第90天")
    private String day90;

    @Schema(description = "第180天")
    private String day180;

    @Schema(description = "第270天")
    private String day270;

    @Schema(description = "最低")
    private String minValue;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /** 是否检索：0：否 、 1：是 */
    private Integer indexed;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}