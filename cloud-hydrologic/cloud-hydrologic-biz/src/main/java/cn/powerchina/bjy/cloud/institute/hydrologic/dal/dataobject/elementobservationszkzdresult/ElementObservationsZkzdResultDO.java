package cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.elementobservationszkzdresult;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;

@TableName("hydrologic_element_observations_zkzd_result")
@KeySequence("hydrologic_element_observations_zkzd_result_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ElementObservationsZkzdResultDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 气象站id
     */
    private Long stationId;
    /**
     * 站点类型，1：雨量站，2：水文站， 3：气象站
     */
    private Integer stationType;
    /**
     * 数据类型
     */
    private Integer dataType;
    /**
     * 时间
     */
    private String time;
    /**
     * 年
     */
    private String year;
    /**
     * 年
     */
    private String month;
    /**
     * 年
     */
    private String day;

    /**
     * 日雨量（mm）
     */
    private String dailyRainfall;
    /**
     * 风向编码
     */
    private String windCode;
    /**
     * 风向
     */
    private String wind;
    /**
     * 温度(℃)
     */
    private String temperature;
    /**
     * 湿度(%)
     */
    private String humidity;
    /**
     * 大气压(Kpa)
     */
    private String atmospheric;
    /**
     * 风速(m/s)
     */
    private String velocity;
    /**
     * 分钟雨量（mm)
     */
    private String minuteRainfall;
    /**
     * 电压(V）
     */
    private String voltage;
    /**
     * 版本（根据原型待定）
     */
    private Integer version;
    /**
     * 最新版本（1：最新，0：历史版本，默认为1）
     */
    private Integer latest;
    /**
     * 记录时间
     */
    private LocalDate currentDay;
}
