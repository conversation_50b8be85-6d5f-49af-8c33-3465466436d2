package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentmonth.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 水文站-输沙率-月含沙量特征值分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SandContentMonthPageReqVO extends PageParam {

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11254")
    @NotNull(message = "站点id不能为空")
    private Long stationId;

    @Schema(description = "年")
    private String year;

    @Schema(description = "月")
    private String month;

    @Schema(description = "月平均值(kg/m3)")
    private String averageValue;

    @Schema(description = "月平均值备注", example = "随便")
    private String averageValueRemark;

    @Schema(description = "月最大含沙量(kg/m3)")
    private String maxValue;

    @Schema(description = "月最大含沙量出现日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] maxValueDate;

    @Schema(description = "月最大含沙量备注", example = "随便")
    private String maxValueRemark;

    @Schema(description = "月最小含沙量(kg/m3)")
    private String minValue;

    @Schema(description = "月最小含沙量出现日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] minValueDate;

    @Schema(description = "月最小含沙量备注", example = "你猜")
    private String minValueRemark;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "检索时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] currentDay;

    @Schema(description = "记录id")
    private Long logId;

    public LocalDateTime[] getCurrentDay() {
        return createTime;
    }

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}