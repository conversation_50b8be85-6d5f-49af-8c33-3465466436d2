package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 站点-成果 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationResultRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13632")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18320")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "2")
    @ExcelProperty("站点类型，1：雨量站，2：水文站， 3：气象站")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "年")
    @ExcelProperty("年")
    private Integer year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private Integer month;

    @Schema(description = "日")
    @ExcelProperty("日")
    private Integer day;

    @Schema(description = "时分")
    @ExcelProperty("时分")
    private String hoursMinute;

    @Schema(description = "0.005粒径级（mm）")
    @ExcelProperty("0.005粒径级（mm）")
    private String grain005;

    @Schema(description = "0.007粒径级（mm）")
    @ExcelProperty("0.007粒径级（mm）")
    private String grain007;

    @Schema(description = "0.01粒径级（mm）")
    @ExcelProperty("0.01粒径级（mm）")
    private String grain01;

    @Schema(description = "0.025粒径级（mm）")
    @ExcelProperty("0.025粒径级（mm）")
    private String grain025;

    @Schema(description = "0.05粒径级（mm）")
    @ExcelProperty("0.05粒径级（mm）")
    private String grain05;

    @Schema(description = "0.1粒径级（mm）")
    @ExcelProperty("0.1粒径级（mm）")
    private String grain1;

    @Schema(description = "0.25粒径级（mm）")
    @ExcelProperty("0.25粒径级（mm）")
    private String grain25;

    @Schema(description = "0.5粒径级（mm）")
    @ExcelProperty("0.5粒径级（mm）")
    private String grain5;

    @Schema(description = "1.0粒径级（mm）")
    @ExcelProperty("1.0粒径级（mm）")
    private String grain10;

    @Schema(description = "2.0粒径级（mm）")
    @ExcelProperty("2.0粒径级（mm）")
    private String grain20;

    @Schema(description = "3.0粒径级（mm）")
    @ExcelProperty("3.0粒径级（mm）")
    private String grain30;

    @Schema(description = "中数粒径(mm)", example = "12117")
    @ExcelProperty("中数粒径(mm)")
    private String grainMid;

    @Schema(description = "最大粒径(mm)")
    @ExcelProperty("最大粒径(mm)")
    private String grainMax;

    @Schema(description = "单样含沙量（kg/m3)")
    @ExcelProperty("单样含沙量（kg/m3)")
    private String sampleSandContent;

    @Schema(description = "施测水温(℃)")
    @ExcelProperty("施测水温(℃)")
    private String waterTemperature;

    @Schema(description = "取样方法")
    @ExcelProperty("取样方法")
    private String samplingMethod;

    @Schema(description = "分析方法")
    @ExcelProperty("分析方法")
    private String analysisMethod;

    @Schema(description = "水文-实测悬移质颗粒级配成果表-平均粒径(mm)")
    @ExcelProperty("水文-实测悬移质颗粒级配成果表-平均粒径(mm)")
    private String grainAverage;

    @Schema(description = "水文-实测悬移质颗粒级配成果表-平均沉速（m/s)")
    @ExcelProperty("水文-实测悬移质颗粒级配成果表-平均沉速（m/s)")
    private String averageSinkSpeed;

    @Schema(description = "水文站-实测流量成果表-起始时分")
    @ExcelProperty("水文站-实测流量成果表-起始时分")
    private String startHoursMinute;

    @Schema(description = "水文站-实测流量成果表-起始时分")
    @ExcelProperty("水文站-实测流量成果表-起始时分")
    private String endHoursMinute;

    @Schema(description = "水文站-实测流量成果表-断面位置")
    @ExcelProperty("水文站-实测流量成果表-断面位置")
    private String sectionPosition;

    @Schema(description = "水文站-实测流量成果表-测验方法")
    @ExcelProperty("水文站-实测流量成果表-测验方法")
    private String testingMethods;

    @Schema(description = "水文站-实测流量成果表-基本水尺水位(m)")
    @ExcelProperty("水文站-实测流量成果表-基本水尺水位(m)")
    private String basicWaterLevel;

    @Schema(description = "水文站-实测流量成果表-流量(m3/s)")
    @ExcelProperty("水文站-实测流量成果表-流量(m3/s)")
    private String flow;

    @Schema(description = "水文站-实测流量成果表-断面面积（m2)")
    @ExcelProperty("水文站-实测流量成果表-断面面积（m2)")
    private String sectionalArea;

    @Schema(description = "水文站-实测流量成果表-平均流速")
    @ExcelProperty("水文站-实测流量成果表-平均流速")
    private String averageVelocityFlow;

    @Schema(description = "水文站-实测流量成果表-最大流速")
    @ExcelProperty("水文站-实测流量成果表-最大流速")
    private String maxVelocityFlow;

    @Schema(description = "水文站-实测流量成果表-水面宽")
    @ExcelProperty("水文站-实测流量成果表-水面宽")
    private String waterSurfaceWidth;

    @Schema(description = "水文站-实测流量成果表-最大水深")
    @ExcelProperty("水文站-实测流量成果表-最大水深")
    private String maxWaterDepth;

    @Schema(description = "水文站-实测流量成果表-平均水深")
    @ExcelProperty("水文站-实测流量成果表-平均水深")
    private String agerageWaterDepth;

    @Schema(description = "水文站-实测流量成果表-水面比降")
    @ExcelProperty("水文站-实测流量成果表-水面比降")
    private String waterSurfaceGradient;

    @Schema(description = "水文站-实测流量成果表-糙率")
    @ExcelProperty("水文站-实测流量成果表-糙率")
    private String roughness;

    @Schema(description = "水文站-实测悬移质输沙率成果表-断面输沙率(kg/s)")
    @ExcelProperty("水文站-实测悬移质输沙率成果表-断面输沙率(kg/s)")
    private String sectionTransportRate;

    @Schema(description = "水文站-实测悬移质输沙率成果表-含沙量断面平均(kg/m3)")
    @ExcelProperty("水文站-实测悬移质输沙率成果表-含沙量断面平均(kg/m3)")
    private String sectionAverageValue;

    @Schema(description = "水文站-实测悬移质输沙率成果表-含沙量单样(kg/m3)")
    @ExcelProperty("水文站-实测悬移质输沙率成果表-含沙量单样(kg/m3)")
    private String sectionSampleValue;

    @Schema(description = "水文站-实测悬移质输沙率成果表-测验方法-断面平均含沙量")
    @ExcelProperty("水文站-实测悬移质输沙率成果表-测验方法-断面平均含沙量")
    private String testingMethodsAverageValue;

    @Schema(description = "水文站-实测悬移质输沙率成果表-测验方法-单样含沙量")
    @ExcelProperty("水文站-实测悬移质输沙率成果表-测验方法-单样含沙量")
    private String testingMethodsSampleValue;

    @Schema(description = "附注", example = "随便")
    @ExcelProperty("附注")
    private String remark;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "记录时间")
    @ExcelProperty("记录时间")
    private LocalDate currentDay;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}