package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelmonth.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水文站-水位-月水位特征值 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WaterLevelMonthRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8170")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5234")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private String month;

    @Schema(description = "月平均值(m)")
    @ExcelProperty("月平均值(m)")
    private String averageValue;

    @Schema(description = "月平均值备注", example = "你猜")
    @ExcelProperty("月平均值备注")
    private String averageValueRemark;

    @Schema(description = "月最高水位(m)")
    @ExcelProperty("月最高水位(m)")
    private String maxValue;

    @Schema(description = "月最高水位出现日期")
    @ExcelProperty("月最高水位出现日期")
    private String maxValueDate;

    @Schema(description = "月最高水位备注", example = "你猜")
    @ExcelProperty("月最高水位备注")
    private String maxValueRemark;

    @Schema(description = "月最低水位(m)")
    @ExcelProperty("月最低水位(m)")
    private String minValue;

    @Schema(description = "月最低水位出现日期")
    @ExcelProperty("月最低水位出现日期")
    private String minValueDate;

    @Schema(description = "月最低水位备注", example = "你说的对")
    @ExcelProperty("月最低水位备注")
    private String minValueRemark;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}