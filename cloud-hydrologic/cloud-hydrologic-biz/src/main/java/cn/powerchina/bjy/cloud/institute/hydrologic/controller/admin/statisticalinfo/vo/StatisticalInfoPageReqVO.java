package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 水文-统计表索引数据信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StatisticalInfoPageReqVO extends PageParam {

    @Schema(description = "站点id", example = "6060")
    private Long stationId;

    @Schema(description = "统计表id", example = "12517")
    private Long statsId;

    @Schema(description = "开始年")
    private String startYear;

    @Schema(description = "结束年")
    private String endYear;

    @Schema(description = "年份数量", example = "16265")
    private Integer yearCount;

    @Schema(description = "年份数据 例如：2021;2022;2023")
    private String years;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "2")
    private String stationType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}