package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Schema(description = "管理后台 - 实测大断面成果年度基本信息新增/修改 Request VO")
@Data
public class SectionSurveyInfoSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "35")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30457")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "站点类型不能为空")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "施测年份", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer year;

    @Schema(description = "是否最新（1最新0历史）")
    private Integer latest;

    @Schema(description = "施测日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate surveyDate;

    @Schema(description = "断面名称及位置，最多50字", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String sectionName;

    @Schema(description = "测时水位，保留2位小数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal waterLevel;

    @Schema(description = "汛期类型：1-汛前，2-汛后", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer periodType;

    @Schema(description = "附注", example = "你猜")
    private String remark;

}