package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelguarantee;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelguarantee.vo.WaterLevelGuaranteePageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelguarantee.vo.WaterLevelGuaranteeRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelguarantee.vo.WaterLevelGuaranteeSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.waterlevelguarantee.WaterLevelGuaranteeDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.waterlevelguarantee.WaterLevelGuaranteeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 水文站-水位-各保证率水位")
@RestController
@RequestMapping("/plan/hydrologic/water/level/guarantee")
@Validated
public class WaterLevelGuaranteeController {

    @Resource
    private WaterLevelGuaranteeService waterLevelGuaranteeService;

    @PostMapping("/create")
    @Operation(summary = "创建水文站-水位-各保证率水位")
   // @PreAuthorize("@ss.hasPermission('hydrologic:water-level-guarantee:create')")
    public CommonResult<Long> createWaterLevelGuarantee(@Valid @RequestBody WaterLevelGuaranteeSaveReqVO createReqVO) {
        return success(waterLevelGuaranteeService.createWaterLevelGuarantee(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水文站-水位-各保证率水位")
   // @PreAuthorize("@ss.hasPermission('hydrologic:water-level-guarantee:update')")
    public CommonResult<Boolean> updateWaterLevelGuarantee(@Valid @RequestBody WaterLevelGuaranteeSaveReqVO updateReqVO) {
        waterLevelGuaranteeService.updateWaterLevelGuarantee(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水文站-水位-各保证率水位")
    @Parameter(name = "id", description = "编号", required = true)
   // @PreAuthorize("@ss.hasPermission('hydrologic:water-level-guarantee:delete')")
    public CommonResult<Boolean> deleteWaterLevelGuarantee(@RequestParam("id") Long id) {
        waterLevelGuaranteeService.deleteWaterLevelGuarantee(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水文站-水位-各保证率水位")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
   // @PreAuthorize("@ss.hasPermission('hydrologic:water-level-guarantee:query')")
    public CommonResult<WaterLevelGuaranteeRespVO> getWaterLevelGuarantee(@RequestParam("id") Long id) {
        WaterLevelGuaranteeDO waterLevelGuarantee = waterLevelGuaranteeService.getWaterLevelGuarantee(id);
        return success(BeanUtils.toBean(waterLevelGuarantee, WaterLevelGuaranteeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水文站-水位-各保证率水位分页")
   // @PreAuthorize("@ss.hasPermission('hydrologic:water-level-guarantee:query')")
    public CommonResult<PageResult<WaterLevelGuaranteeRespVO>> getWaterLevelGuaranteePage(@Valid WaterLevelGuaranteePageReqVO pageReqVO) {
        PageResult<WaterLevelGuaranteeDO> pageResult = waterLevelGuaranteeService.getWaterLevelGuaranteePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WaterLevelGuaranteeRespVO.class));
    }

}