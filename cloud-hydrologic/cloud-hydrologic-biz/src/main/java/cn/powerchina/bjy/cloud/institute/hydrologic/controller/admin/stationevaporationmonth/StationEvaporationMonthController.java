package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationmonth;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationmonth.vo.StationEvaporationMonthDataPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationmonth.vo.StationEvaporationMonthPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationmonth.vo.StationEvaporationMonthRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationmonth.vo.StationEvaporationMonthSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationevaporationmonth.StationEvaporationMonthDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationevaporationmonth.StationEvaporationMonthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 蒸发-月水面蒸发量特征值")
@RestController
@RequestMapping("/plan/hydrologic/station/evaporation/month")
@Validated
public class StationEvaporationMonthController {

    @Resource
    private StationEvaporationMonthService stationEvaporationMonthService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-获得蒸发-月水面蒸发量特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:query-rain')")
    public CommonResult<PageResult<StationEvaporationMonthRespVO>> getStationEvaporationMonthPage(@Valid StationEvaporationMonthPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        PageResult<StationEvaporationMonthDO> pageResult = stationEvaporationMonthService.getStationEvaporationMonthPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationEvaporationMonthRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入蒸发-月水面蒸发量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importStationRainMonthFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                    @RequestParam("file") MultipartFile file) {
        stationEvaporationMonthService.importStationEvaporateMonthFeatureExcel(stationId, StationEnum.RAIN.getType(), file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新蒸发-月水面蒸发量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:update-rain')")
    public CommonResult<Boolean> updateStationRainEvaporationMonth(@Valid @RequestBody StationEvaporationMonthSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.RAIN.getType());
        stationEvaporationMonthService.updateStationEvaporationMonth(updateReqVO);
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-获得蒸发-月水面蒸发量特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:query-hydrologic')")
    public CommonResult<PageResult<StationEvaporationMonthRespVO>> getStationHydrologicEvaporationMonthPage(@Valid StationEvaporationMonthPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        PageResult<StationEvaporationMonthDO> pageResult = stationEvaporationMonthService.getStationEvaporationMonthPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationEvaporationMonthRespVO.class));
    }

    @PostMapping("/import/hydrologic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入蒸发-月水面蒸发量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    public CommonResult<Boolean> importStationHydrologicMonthFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                          @RequestParam("file") MultipartFile file) {
        stationEvaporationMonthService.importStationEvaporateMonthFeatureExcel(stationId, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新蒸发-月水面蒸发量特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:update-hydrologic')")
    public CommonResult<Boolean> updateStationHydrologicEvaporationMonth(@Valid @RequestBody StationEvaporationMonthSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        stationEvaporationMonthService.updateStationEvaporationMonth(updateReqVO);
        return success(true);
    }

    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出蒸发-月水面蒸发量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:export-rain')")
    public void exportRainStationEvaporationDayExcel(@Valid StationEvaporationMonthPageReqVO pageReqVO,
                                                     HttpServletResponse response) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setCreateTime(null);
        stationEvaporationMonthService.exportStationEvaporationMonthExcel(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出蒸发-月水面蒸发量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:export-hydrologic')")
    public void exportHydrologicStationEvaporationDayExcel(@Valid StationEvaporationMonthPageReqVO pageReqVO,
                                                           HttpServletResponse response) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setCreateTime(null);
        stationEvaporationMonthService.exportStationEvaporationMonthExcel(response, pageReqVO);
    }

    @GetMapping("/data/page/rain")
    @Operation(summary = "雨量站-数据检索-获得蒸发-月水面蒸发量特征值")
    @HydrologicOperation(stationType = StationEnum.RAIN, dataType = StationDataTypeEnum.STATION_EVAPORATE_MONTH)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:query-data-rain')")
    public CommonResult<PageResult<StationEvaporationMonthRespVO>> getRainStationEvaporationMonthDataPage(@Valid StationEvaporationMonthDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationEvaporationMonthPageReqVO pageReqVO1 = new StationEvaporationMonthPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationEvaporationMonthDO> pageResult = stationEvaporationMonthService.getStationEvaporationMonthPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationEvaporationMonthRespVO.class));
    }

    @GetMapping("/data/page/hydrologic")
    @Operation(summary = "水文站--数据检索-获得蒸发-月水面蒸发量特征值")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_EVAPORATE_MONTH)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:query-data-hydrologic')")
    public CommonResult<PageResult<StationEvaporationMonthRespVO>> getHydrologicStationEvaporationMonthDataPage(@Valid StationEvaporationMonthDataPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return success(PageResult.empty());
        }
        StationEvaporationMonthPageReqVO pageReqVO1 = new StationEvaporationMonthPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setLogId(null);
        PageResult<StationEvaporationMonthDO> pageResult = stationEvaporationMonthService.getStationEvaporationMonthPage(pageReqVO1);
        return success(BeanUtils.toBean(pageResult, StationEvaporationMonthRespVO.class));
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出蒸发-月水面蒸发量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:export-data-rain')")
    public void exportRainStationEvaporationMonthDataExcel(@Valid StationEvaporationMonthDataPageReqVO pageReqVO,
                                                           HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationEvaporationMonthPageReqVO pageReqVO1 = new StationEvaporationMonthPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO1.setStationType(StationEnum.RAIN.getType());
        pageReqVO1.setLogId(null);
        stationEvaporationMonthService.exportStationEvaporationMonthExcel(response, pageReqVO1);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出蒸发-月水面蒸发量特征值 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-evaporation-month:export-data-hydrologic')")
    public void exportHydrologicStationEvaporationMonthDataExcel(@Valid StationEvaporationMonthDataPageReqVO pageReqVO,
                                                                 HttpServletResponse response) {
        if (Objects.isNull(pageReqVO.getCreateTime()) || pageReqVO.getCreateTime().length == 0) {
            return;
        }
        StationEvaporationMonthPageReqVO pageReqVO1 = new StationEvaporationMonthPageReqVO();
        org.springframework.beans.BeanUtils.copyProperties(pageReqVO, pageReqVO1);
        pageReqVO1.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO1.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO1.setLogId(null);
        stationEvaporationMonthService.exportStationEvaporationMonthExcel(response, pageReqVO1);
    }


}