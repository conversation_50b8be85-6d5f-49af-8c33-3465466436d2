package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelmonth.vo;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 水文站—潮位月统计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TideLevelMonthPageReqVO extends PageParam {

    @Schema(description = "检索记录判断", example = "1")
    private Long indexed;

    @Schema(description = "水文站id", example = "1278")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站", example = "1")
    private Integer stationType;

    @Schema(description = "数据类型", example = "2")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "月")
    private Integer month;

    @Schema(description = "项目")
    private String value1;

    @Schema(description = "潮位最高（大)")
    private String value2;

    @Schema(description = "日")
    private String value3;

    @Schema(description = "时分")
    private String value4;

    @Schema(description = "月(潮位最高农历)")
    private String value5;

    @Schema(description = "日(潮位最高农历)")
    private String value6;

    @Schema(description = "潮位最高低（小)")
    private String value7;

    @Schema(description = "日")
    private String value8;

    @Schema(description = "时分")
    private String value9;

    @Schema(description = "月(潮位最低农历)")
    private String value10;

    @Schema(description = "日(潮位最低农历)")
    private String value11;

    @Schema(description = "月总数")
    private String value12;

    @Schema(description = "次数")
    private String value13;

    @Schema(description = "平均")
    private String value14;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "记录时间")
    private LocalDate currentDay;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}