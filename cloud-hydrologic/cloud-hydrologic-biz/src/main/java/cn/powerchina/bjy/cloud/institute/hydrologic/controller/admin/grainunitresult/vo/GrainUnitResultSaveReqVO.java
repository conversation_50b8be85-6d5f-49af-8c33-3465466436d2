package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.grainunitresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 水文-实测悬移质单位水样颗粒级配成果新增/修改 Request VO")
@Data
public class GrainUnitResultSaveReqVO {

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12091")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "站点类型不能为空")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<Item> dataList;

    @Schema(description = "是否删除 true是 ")
    private Boolean isDelete;

    @Schema(description = "本次更新删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "管理后台 - 降水-日降水量数据 Request VO")
    @Data
    public static class Item {
        @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19802")
        private Long id;

        @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28372")
        @NotNull(message = "水文站id不能为空")
        private Long stationId;

        @Schema(description = "年")
        private Integer year;

        @Schema(description = "月")
        private Integer month;

        @Schema(description = "日")
        private String day;

        @Schema(description = "时分")
        private String hoursMinute;

        /**
         * 施测号数
         */
        private String tesNum;

        @Schema(description = "0.005粒径级（mm）")
        private String grain005;

        @Schema(description = "0.007粒径级（mm）")
        private String grain007;

        @Schema(description = "0.01粒径级（mm）")
        private String grain01;

        @Schema(description = "0.025粒径级（mm）")
        private String grain025;

        @Schema(description = "0.05粒径级（mm）")
        private String grain05;

        @Schema(description = "0.1粒径级（mm）")
        private String grain1;

        @Schema(description = "0.25粒径级（mm）")
        private String grain25;

        @Schema(description = "0.5粒径级（mm）")
        private String grain5;

        @Schema(description = "1.0粒径级（mm）")
        private String grain10;

        @Schema(description = "2.0粒径级（mm）")
        private String grain20;

        @Schema(description = "3.0粒径级（mm）")
        private String grain30;

//        @Schema(description = "中数粒径(mm)", example = "7740")
//        private String grainMid;

        @Schema(description = "最大粒径(mm)")
        private String grainMax;

        @Schema(description = "单样含沙量（kg/m3)")
        private String sampleSandContent;

        @Schema(description = "施测水温(℃)")
        private String waterTemperature;

        @Schema(description = "取样方法")
        private String samplingMethod;

        @Schema(description = "分析方法")
        private String analysisMethod;

        @Schema(description = "附注", example = "随便")
        private String remark;

        @Schema(description = "版本（根据原型待定）")
        private Integer version;

        @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
        private Integer latest;
    }

}