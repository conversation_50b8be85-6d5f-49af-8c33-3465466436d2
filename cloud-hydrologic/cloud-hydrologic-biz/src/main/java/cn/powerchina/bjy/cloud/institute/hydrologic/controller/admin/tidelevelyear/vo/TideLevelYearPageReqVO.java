package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelyear.vo;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 水文站—潮位年统计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TideLevelYearPageReqVO extends PageParam {

    @Schema(description = "检索记录判断", example = "1")
    private Long indexed;

    @Schema(description = "水文站id", example = "2151")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站", example = "2")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "一月")
    private String value1;

    @Schema(description = "二月")
    private String value2;

    @Schema(description = "三月")
    private String value3;

    @Schema(description = "四月")
    private String value4;

    @Schema(description = "五月")
    private String value5;

    @Schema(description = "六月")
    private String value6;

    @Schema(description = "七月")
    private String value7;

    @Schema(description = "八月")
    private String value8;

    @Schema(description = "九月")
    private String value9;

    @Schema(description = "十月")
    private String value10;

    @Schema(description = "十一月")
    private String value11;

    @Schema(description = "十二月")
    private String value12;

    @Schema(description = "全年")
    private String value13;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "记录时间")
    private LocalDate currentDay;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}