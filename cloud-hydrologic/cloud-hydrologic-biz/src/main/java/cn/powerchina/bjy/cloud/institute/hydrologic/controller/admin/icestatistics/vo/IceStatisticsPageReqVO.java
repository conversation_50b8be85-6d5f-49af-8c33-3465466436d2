package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 冰情统计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IceStatisticsPageReqVO extends PageParam {

    @Schema(description = "水文站id，必传", requiredMode = Schema.RequiredMode.REQUIRED, example = "11254")
    @NotNull(message = "站点id不能为空")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值")
    private Integer stationType;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "年份")
    private String year;

    @Schema(description = "解冻月日")
    private String thawingMonthDay;

    @Schema(description = "终止流冰月日")
    private String flowingEndDay;

    @Schema(description = "终冰月日")
    private String finalMonthDay;

    @Schema(description = "初冰月日")
    private String firstMonthDay;

    @Schema(description = "开始流冰月日")
    private String startFlowMonthDay;

    @Schema(description = "封冻月日")
    private String frozenMonthDay;

    @Schema(description = "上半年实际封冻天数")
    private String firstHalfYearDays;

    @Schema(description = "下半年实际封冻天数")
    private String secondHalfYearDays;

    @Schema(description = "河心最大冰厚(m) ")
    private String centerMaxThickness;

    @Schema(description = "河心最大冰厚出现日期")
    private String centerMaxThicknessDate;

    @Schema(description = "岸边最大冰厚(m)")
    private String shoreMaxThickness;

    @Schema(description = "岸边最大冰厚出现日期")
    private String shoreMaxThicknessDate;

    @Schema(description = "最大流冰块长度(m) ")
    private String flowMaxLength;

    @Schema(description = "最大流冰块宽度(m)")
    private String flowMaxWidth;

    @Schema(description = "冰速(m/s)")
    private String iceSpeed;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "记录id")
    private Long logId;

    /** 是否检索：0：否 、 1：是 */
    private Integer indexed;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}