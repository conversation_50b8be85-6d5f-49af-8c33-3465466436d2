package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.datamigration;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.migrationtree.vo.MigrationTreeRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filedocument.FileDocumentDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.BizConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.RedisService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.datamigration.DataMigrationService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.LoginUserUtil;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 数据迁移-备份
 * @time 2024/8/28 16:15
 */
@Tag(name = "水文 - 备份还原")
@RestController
@Slf4j
@RequestMapping("/plan/hydrologic/migration")
public class DataMigrationController {
    @Autowired
    private DataMigrationService migrationService;
    @Autowired
    private RedisService redisService;


    /**
     * 数据备份
     */
    @Operation(summary = "备份")
    @PostMapping("/backup")
    public ResponseEntity<FileSystemResource> backup(@RequestBody List<MigrationTreeRespVO> tree) throws IOException {
        return migrationService.backup(tree);
    }

    /**
     * 数据还原
     */
    @Operation(summary = "还原")
    @PostMapping("/restore")
    public CommonResult<Void> restore(@RequestBody List<MigrationTreeRespVO> tree) {
        RLock rLock = redisService.acquireDistributedLock(BizConstants.HY_RESTORE);
        try {
            return migrationService.restore(tree);
        } finally {
            rLock.unlock();
        }
    }

    @PostMapping("/upload")
    @Operation(summary = "上传文件")
    @Parameter(name = "file",description = "上传文件")
    public CommonResult<List<MigrationTreeRespVO>> fileUpload(@RequestParam("file") MultipartFile file) {
        RLock rLock = redisService.acquireDistributedLock(BizConstants.HY_RESTORE);
        try {
            return migrationService.fileUpload(file);
        } finally {
            rLock.unlock();
        }
    }


    /**
     * 获取选择tree
     */
    @Operation(summary = "备份获取选择tree")
    @GetMapping("/getTree")
    public CommonResult<List<MigrationTreeRespVO>> getTree() {
        return CommonResult.success(migrationService.getTree());
    }

}
