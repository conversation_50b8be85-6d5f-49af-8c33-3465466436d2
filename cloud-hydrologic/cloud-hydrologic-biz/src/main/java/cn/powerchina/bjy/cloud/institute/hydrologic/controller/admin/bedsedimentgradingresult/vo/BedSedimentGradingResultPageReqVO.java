package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.bedsedimentgradingresult.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 实测床沙颗粒级配成果分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BedSedimentGradingResultPageReqVO extends PageParam {

    @Schema(description = "记录id")
    private Long logId;

    @Schema(description = "检索记录判断", example = "1")
    private Long indexed;

    @Schema(description = "水文站id", example = "11040")
    private Long stationId;

    @Schema(description = "站点类型", example = "1")
    private Integer stationType;

    @Schema(description = "数据类型", example = "2")
    private Integer dataType;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "月份")
    private Integer month;

    @Schema(description = "日")
    private Integer day;

    @Schema(description = "0.002mm粒径级百分比")
    private BigDecimal value002;

    @Schema(description = "0.004mm粒径级百分比")
    private BigDecimal value004;

    @Schema(description = "0.008mm粒径级百分比")
    private BigDecimal value008;

    @Schema(description = "0.016mm粒径级百分比")
    private BigDecimal value016;

    @Schema(description = "0.031mm粒径级百分比")
    private BigDecimal value031;

    @Schema(description = "0.062mm粒径级百分比")
    private BigDecimal value062;

    @Schema(description = "0.125mm粒径级百分比")
    private BigDecimal value125;

    @Schema(description = "0.25mm粒径级百分比")
    private BigDecimal value250;

    @Schema(description = "0.5mm粒径级百分比")
    private BigDecimal value500;

    @Schema(description = "1.0mm粒径级百分比")
    private BigDecimal value1000;

    @Schema(description = "2.0mm粒径级百分比")
    private BigDecimal value2000;

    @Schema(description = "4.0mm粒径级百分比")
    private BigDecimal value4000;

    @Schema(description = "8.0mm粒径级百分比")
    private BigDecimal value8000;

    @Schema(description = "中数粒径(mm)")
    private BigDecimal medianDiameter;

    @Schema(description = "平均粒径(mm)")
    private BigDecimal meanDiameter;

    @Schema(description = "最大粒径(mm)")
    private BigDecimal maxDiameter;

    @Schema(description = "取样方法")
    private String sampleMethod;

    @Schema(description = "分析方法")
    private String analysisMethod;

    @Schema(description = "备注/附注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}