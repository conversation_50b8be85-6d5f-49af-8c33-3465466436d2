package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.tidelevelday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 水文站—潮位日统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TideLevelDayRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12233")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21525")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站", example = "1")
    @ExcelProperty("站点类型，1：雨量站，2：水文站")
    private Integer stationType;

    @Schema(description = "数据类型", example = "2")
    @ExcelProperty("数据类型")
    private Integer dataType;

    @Schema(description = "年")
    @ExcelProperty("年")
    private Integer year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private Integer month;

    @Schema(description = "日")
    @ExcelProperty("日")
    private Integer day;

    @Schema(description = "潮别")
    @ExcelProperty("潮别")
    private String value1;

    @Schema(description = "潮位")
    @ExcelProperty("潮位")
    private String value2;

    @Schema(description = "时分")
    @ExcelProperty("时分")
    private String value3;

    @Schema(description = "潮差")
    @ExcelProperty("潮差")
    private String value4;

    @Schema(description = "历时")
    @ExcelProperty("历时")
    private String value5;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "记录时间")
    @ExcelProperty("记录时间")
    private LocalDate currentDay;

    @Schema(description = "版本")
    @ExcelProperty("版本")
    private Integer version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}