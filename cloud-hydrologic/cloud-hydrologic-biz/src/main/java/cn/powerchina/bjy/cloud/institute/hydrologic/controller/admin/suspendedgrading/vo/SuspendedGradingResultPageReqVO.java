package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedgrading.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 月年平均悬移质颗粒级配成果分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SuspendedGradingResultPageReqVO extends PageParam {

    @Schema(description = "站点ID", example = "16047")
    private String stationId;

    @Schema(description = "站点类型", example = "1")
    private String stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "年份(4位数字)")
    private Integer year;

    @Schema(description = "月份(1-12,0表示全年)")
    private Integer month;

    @Schema(description = "0.005mm粒径百分数")
    private String value0005;

    @Schema(description = "0.007mm粒径百分数")
    private String value0007;

    @Schema(description = "0.01mm粒径百分数")
    private String value001;

    @Schema(description = "0.025mm粒径百分数")
    private String value0025;

    @Schema(description = "0.05mm粒径百分数")
    private String value005;

    @Schema(description = "0.1mm粒径百分数")
    private String value01;

    @Schema(description = "0.25mm粒径百分数")
    private String value025;

    @Schema(description = "0.5mm粒径百分数")
    private String value05;

    @Schema(description = "1.0mm粒径百分数")
    private String value10;

    @Schema(description = "2.0mm粒径百分数")
    private String value20;

    @Schema(description = "3.0mm粒径百分数")
    private String value30;

    @Schema(description = "中数粒径(mm)")
    private String medianSize;

    @Schema(description = "平均粒径(mm)")
    private String meanSize;

    @Schema(description = "最大粒径(mm)")
    private String maxSize;

    @Schema(description = "是否为最新版本(1是,0否)")
    private Integer latest;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "检索记录判断", example = "1")
    private Long indexed;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}