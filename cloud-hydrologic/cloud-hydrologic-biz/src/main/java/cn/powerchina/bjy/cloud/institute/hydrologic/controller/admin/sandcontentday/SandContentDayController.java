package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentday;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentday.vo.SandContentDayPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentday.vo.SandContentDayRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentday.vo.SandContentDaySaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sandcontentday.vo.SandYearBookUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.YearForm;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sandcontentday.SandContentDayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台—含沙量-逐日平均含沙量")
@RestController
@RequestMapping("/plan/hydrologic/sand/content/day")
@Validated
public class SandContentDayController {

    @Resource
    private SandContentDayService sandContentDayService;

    @PutMapping("/update")
    @Operation(summary = "更新水文站—输沙率逐日平均含沙量")
//    @PreAuthorize("@ss.hasPermission('hydrologic:sand-content-day:update')")
    public CommonResult<Boolean> updateSandContentDay(@Valid @RequestBody SandContentDaySaveReqVO updateReqVO) {
        sandContentDayService.updateSandContentDay(updateReqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得逐日平均含沙量分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:sand-content-day:query')")
    public CommonResult<PageResult<SandContentDayRespVO>> getSandContentDayPage(@Valid SandContentDayPageReqVO pageReqVO) {
        PageResult<SandContentDayRespVO> pageResult = sandContentDayService.getSandContentDayPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-逐日平均含沙量分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_SAND_DAY)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<SandContentDayRespVO>> getSandContentDayDataPage(@Valid SandContentDayPageReqVO pageReqVO) {
        if (null == pageReqVO.getCurrentDay() || pageReqVO.getCurrentDay().length == 0) {
            return success(PageResult.empty());
        }
        pageReqVO.setLogId(null);
        PageResult<SandContentDayRespVO> pageResult = sandContentDayService.getSandContentDayPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export")
    @Operation(summary = "（提交记录）导出序列格式 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportSandContentDayExcel(@Valid SandContentDayPageReqVO pageReqVO,
                                          HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        sandContentDayService.exportExcel(response, pageReqVO);
    }

    @GetMapping("/export/book")
    @Operation(summary = "（提交记录）导出年鉴格式 Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "logId", description = "记录Id", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportSandContentDayBookExcel(@RequestParam("stationId") Long stationId,
                                              @RequestParam(value = "logId") Long logId,
                                              HttpServletResponse response) throws IOException {
        sandContentDayService.exportBookExcel(response, stationId, logId, null);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出逐日平均含沙量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportSandContentDayDataExcel(@Valid SandContentDayPageReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        sandContentDayService.exportExcel(response, pageReqVO);
    }

    @GetMapping("/export/databook")
    @Operation(summary = "（检索）导出逐日平均含沙量年鉴格式 Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "year", description = "年", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportSandContentDayDataExcel(@RequestParam("stationId") Long stationId,
                                              @RequestParam(value = "year") String year,
                                              HttpServletResponse response) throws IOException {

        sandContentDayService.exportBookExcel(response, stationId, null, year);
    }

    @GetMapping("/book/info")
    @Operation(summary = "（年鉴）逐日平均含沙量年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-hydrologic-year:yearbook-info')")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "logId", description = "记录id")
    public CommonResult<List<YearForm>> getYearBookList(@RequestParam("stationId") Long stationId,
                                                        @RequestParam(value = "logId", required = false) Long logId) {
        return success(sandContentDayService.getYearBookList(stationId, logId, null));
    }

    @GetMapping("/book/info/search")
    @Operation(summary = "数据检索-平均含沙量年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-hydrologic-year:yearbook-info')")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "year", description = "年")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.HYDROLOGIC_STATION_SAND_BOOK)
    public CommonResult<List<YearForm>> searchYearBookDataList(@RequestParam("stationId") Long stationId,
                                                               @RequestParam(value = "year", required = false) String year) {
        if (StringUtils.isBlank(year)) {
            return success(new ArrayList<>());
        }
        return success(sandContentDayService.getYearBookList(stationId, null, year));
    }

    @PostMapping("/book/update")
    @Operation(summary = "（年鉴修改）逐日平均含沙量-修改数据")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:yearbook-update')")
    public CommonResult<Boolean> updateYearBookList(@RequestBody SandYearBookUpdateReqVO reqVO) {
        sandContentDayService.modifyYearBookList(reqVO.getStationId(), reqVO.getDataMode());
        return success(true);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入逐日平均含沙量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:ice-day:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "type", description = "年鉴或序列（1:序列;2:年鉴）", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId,
                                             @RequestParam("type") Integer type) throws IOException {
        sandContentDayService.importData(file, stationId, type);
        return success(true);
    }

}