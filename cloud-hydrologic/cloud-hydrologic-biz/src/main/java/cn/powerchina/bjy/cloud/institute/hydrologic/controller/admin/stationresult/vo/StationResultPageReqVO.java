package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationresult.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 站点-成果分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StationResultPageReqVO extends PageParam {

    @Schema(description = "记录id")
    private Long logId;

    @Schema(description = "水文站id", example = "18320")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "2")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "月")
    private Integer month;

    @Schema(description = "日")
    private Integer day;

    @Schema(description = "时分")
    private String hoursMinute;

    @Schema(description = "0.005粒径级（mm）")
    private String grain005;

    @Schema(description = "0.007粒径级（mm）")
    private String grain007;

    @Schema(description = "0.01粒径级（mm）")
    private String grain01;

    @Schema(description = "0.025粒径级（mm）")
    private String grain025;

    @Schema(description = "0.05粒径级（mm）")
    private String grain05;

    @Schema(description = "0.1粒径级（mm）")
    private String grain1;

    @Schema(description = "0.25粒径级（mm）")
    private String grain25;

    @Schema(description = "0.5粒径级（mm）")
    private String grain5;

    @Schema(description = "1.0粒径级（mm）")
    private String grain10;

    @Schema(description = "2.0粒径级（mm）")
    private String grain20;

    @Schema(description = "3.0粒径级（mm）")
    private String grain30;

    @Schema(description = "中数粒径(mm)", example = "12117")
    private String grainMid;

    @Schema(description = "最大粒径(mm)")
    private String grainMax;

    @Schema(description = "单样含沙量（kg/m3)")
    private String sampleSandContent;

    @Schema(description = "施测水温(℃)")
    private String waterTemperature;

    @Schema(description = "取样方法")
    private String samplingMethod;

    @Schema(description = "分析方法")
    private String analysisMethod;

    @Schema(description = "水文-实测悬移质颗粒级配成果表-平均粒径(mm)")
    private String grainAverage;

    @Schema(description = "水文-实测悬移质颗粒级配成果表-平均沉速（m/s)")
    private String averageSinkSpeed;

    @Schema(description = "水文站-实测流量成果表-起始时分")
    private String startHoursMinute;

    @Schema(description = "水文站-实测流量成果表-起始时分")
    private String endHoursMinute;

    @Schema(description = "水文站-实测流量成果表-断面位置")
    private String sectionPosition;

    @Schema(description = "水文站-实测流量成果表-测验方法")
    private String testingMethods;

    @Schema(description = "水文站-实测流量成果表-基本水尺水位(m)")
    private String basicWaterLevel;

    @Schema(description = "水文站-实测流量成果表-流量(m3/s)")
    private String flow;

    @Schema(description = "水文站-实测流量成果表-断面面积（m2)")
    private String sectionalArea;

    @Schema(description = "水文站-实测流量成果表-平均流速")
    private String averageVelocityFlow;

    @Schema(description = "水文站-实测流量成果表-最大流速")
    private String maxVelocityFlow;

    @Schema(description = "水文站-实测流量成果表-水面宽")
    private String waterSurfaceWidth;

    @Schema(description = "水文站-实测流量成果表-最大水深")
    private String maxWaterDepth;

    @Schema(description = "水文站-实测流量成果表-平均水深")
    private String agerageWaterDepth;

    @Schema(description = "水文站-实测流量成果表-水面比降")
    private String waterSurfaceGradient;

    @Schema(description = "水文站-实测流量成果表-糙率")
    private String roughness;

    @Schema(description = "水文站-实测悬移质输沙率成果表-断面输沙率(kg/s)")
    private String sectionTransportRate;

    @Schema(description = "水文站-实测悬移质输沙率成果表-含沙量断面平均(kg/m3)")
    private String sectionAverageValue;

    @Schema(description = "水文站-实测悬移质输沙率成果表-含沙量单样(kg/m3)")
    private String sectionSampleValue;

    @Schema(description = "水文站-实测悬移质输沙率成果表-测验方法-断面平均含沙量")
    private String testingMethodsAverageValue;

    @Schema(description = "水文站-实测悬移质输沙率成果表-测验方法-单样含沙量")
    private String testingMethodsSampleValue;

    @Schema(description = "附注", example = "随便")
    private String remark;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "记录日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] currentDay;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /** 是否检索：0：否 、 1：是 */
    private Integer indexed;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}