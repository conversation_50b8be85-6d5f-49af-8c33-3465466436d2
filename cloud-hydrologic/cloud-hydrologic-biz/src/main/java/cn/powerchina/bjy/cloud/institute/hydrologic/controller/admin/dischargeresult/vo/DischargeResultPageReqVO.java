package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.dischargeresult.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 水文站-实测悬移质输沙率成果分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DischargeResultPageReqVO extends PageParam {

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11254")
    @NotNull(message = "站点id不能为空")
    private Long stationId;

    @Schema(description = "年")
    private String year;

    @Schema(description = "月")
    private String month;

    @Schema(description = "日")
    private String day;

    @Schema(description = "起始时分")
    private String startHoursMinute;

    @Schema(description = "起始时分")
    private String endHoursMinute;

    @Schema(description = "流量(m3/s)")
    private String flow;

    @Schema(description = "断面输沙率(kg/s)")
    private String sectionTransportRate;

    @Schema(description = "含沙量断面平均(kg/m3)")
    private String sectionAverageValue;

    @Schema(description = "含沙量单样(kg/m3)")
    private String sectionSampleValue;

    @Schema(description = "测验方法-断面平均含沙量")
    private String testingMethodsAverageValue;

    @Schema(description = "测验方法-单样含沙量")
    private String testingMethodsSampleValue;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "检索时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] currentDay;

    @Schema(description = "记录id")
    private Long logId;

    public LocalDateTime[] getCurrentDay() {
        return createTime;
    }

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}