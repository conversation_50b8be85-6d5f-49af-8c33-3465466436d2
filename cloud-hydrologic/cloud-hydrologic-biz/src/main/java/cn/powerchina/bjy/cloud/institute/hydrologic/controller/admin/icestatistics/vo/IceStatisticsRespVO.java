package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 冰情统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class IceStatisticsRespVO {

    @Schema(description = "主键",example = "25603")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", example = "25543")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "年份")
    @ExcelProperty("年份")
    private String year;

    @Schema(description = "解冻月日")
    @ExcelProperty("解冻月日")
    private String thawingMonthDay;

    @Schema(description = "终止流冰月日")
    @ExcelProperty("终止流冰月日")
    private String flowingEndDay;

    @Schema(description = "终冰月日")
    @ExcelProperty("终冰月日")
    private String finalMonthDay;

    @Schema(description = "初冰月日")
    @ExcelProperty("初冰月日")
    private String firstMonthDay;

    @Schema(description = "开始流冰月日")
    @ExcelProperty("开始流冰月日")
    private String startFlowMonthDay;

    @Schema(description = "封冻月日")
    @ExcelProperty("封冻月日")
    private String frozenMonthDay;

    @Schema(description = "上半年实际封冻天数")
    @ExcelProperty("上半年实际封冻天数")
    private String firstHalfYearDays;

    @Schema(description = "下半年实际封冻天数")
    @ExcelProperty("下半年实际封冻天数")
    private String secondHalfYearDays;

    @Schema(description = "河心最大冰厚(m) ")
    @ExcelProperty("河心最大冰厚(m) ")
    private String centerMaxThickness;

    @Schema(description = "河心最大冰厚出现日期")
    @ExcelProperty("河心最大冰厚出现日期")
    private String centerMaxThicknessDate;

    @Schema(description = "岸边最大冰厚(m)")
    @ExcelProperty("岸边最大冰厚(m)")
    private String shoreMaxThickness;

    @Schema(description = "岸边最大冰厚出现日期")
    @ExcelProperty("岸边最大冰厚出现日期")
    private String shoreMaxThicknessDate;

    @Schema(description = "最大流冰块长度(m) ")
    @ExcelProperty("最大流冰块长度(m) ")
    private String flowMaxLength;

    @Schema(description = "最大流冰块宽度(m)")
    @ExcelProperty("最大流冰块宽度(m)")
    private String flowMaxWidth;

    @Schema(description = "冰速(m/s)")
    @ExcelProperty("冰速(m/s)")
    private String iceSpeed;

    @Schema(description = "版本")
    @ExcelProperty("版本")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}