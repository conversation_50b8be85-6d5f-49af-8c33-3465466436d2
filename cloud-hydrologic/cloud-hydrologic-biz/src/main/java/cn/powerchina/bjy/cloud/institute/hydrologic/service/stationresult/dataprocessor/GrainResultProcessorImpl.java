package cn.powerchina.bjy.cloud.institute.hydrologic.service.stationresult.dataprocessor;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationresult.vo.StationResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationresult.StationResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.LatestEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.GrainResultImportModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationresult.StationResultService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.DateUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.StringUtils;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@Slf4j
public class GrainResultProcessorImpl extends AbstractResultDataProcessor {

    @Autowired
    StationResultService stationResultService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(StationResultSaveReqVO updateReqVO) {
        List<StationResultSaveReqVO.StationResultData> dataList = updateReqVO.getDataList();
        List<Long> deleteIds = updateReqVO.getDeleteIds();
        //没有要更新或删除的内容直接返回
        if (CollectionUtil.isEmpty(dataList) && CollectionUtil.isEmpty(deleteIds)) {
            log.warn("没有要更新或删除的内容 stationId=[{}] ,dataType=[{}]", updateReqVO.getStationId(), updateReqVO.getDataType());
            return;
        }

        if(CollectionUtil.isNotEmpty(dataList)) {
            dataList.forEach(item -> {
                boolean validate = DateUtils.isValidDate(item.getYear(), item.getMonth(), item.getDay());
                if (BooleanUtil.isFalse(validate)) {
                    throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
                }
            });
        }

        //剔除掉要删除的数据只保留要更新的元素，从更新数据list中： 处理一种特殊情况（前端操作了更新之后又删除）
        if (CollectionUtil.isNotEmpty(dataList) && CollectionUtil.isNotEmpty(deleteIds)) {
            dataList = dataList.stream().filter(item -> !deleteIds.contains(item.getId())).toList();
        }


        //本次更新的内容
        List<GrainResultImportModel> importDataList = new ArrayList<>();

        List<Long> updateIdList = new ArrayList<>();

        //判断本次提交的数据是否跟库中已有的数据有重复
        if (CollectionUtil.isNotEmpty(dataList)) {
            for (StationResultSaveReqVO.StationResultData stationResultData : dataList) {

                Long id = stationResultData.getId();
                if (Objects.isNull(id)) {
                    continue;
                }
                updateIdList.add(id);

                //更新场景重复判断
                StationResultDO stationResultDO = stationResultService.selectOne(
                        updateReqVO.getStationId(),
                        updateReqVO.getDataType(),
                        stationResultData.getYear(),
                        stationResultData.getMonth(),
                        stationResultData.getDay(),
                        stationResultData.getHoursMinute());
                if (Objects.nonNull(stationResultDO) && !stationResultDO.getId().equals(id)) {
                    throw ServiceExceptionUtil.exception(ErrorCodeConstants.DATA_FORMAT_ERROR);
                }
            }

            //获取更新的内容
            importDataList = updateReqVO.getDataList().stream()
                    .map(item -> GrainResultImportModel.builder()
                            .year(item.getYear())
                            .month(item.getMonth())
                            .day(item.getDay())
                            .remark(item.getRemark())
                            .analysisMethod(item.getAnalysisMethod())
                            .samplingMethod(item.getSamplingMethod())
                            .waterTemperature(item.getWaterTemperature())
                            .averageSinkSpeed(item.getAverageSinkSpeed())
                            .grainMax(item.getGrainMax())
                            .grainAverage(item.getGrainAverage())
                            .grainMid(item.getGrainMid())
                            .grain30(item.getGrain30())
                            .grain20(item.getGrain20())
                            .grain10(item.getGrain10())
                            .grain5(item.getGrain5())
                            .grain25(item.getGrain25())
                            .grain1(item.getGrain1())
                            .grain05(item.getGrain05())
                            .grain025(item.getGrain025())
                            .grain01(item.getGrain01())
                            .grain005(item.getGrain005())
                            .grain007(item.getGrain007())
                            .build())
                    .toList();
        }
        dayDataToDbBuilder(updateReqVO, importDataList, updateIdList, false);
    }

    @Override
    public void importExcel(MultipartFile file, Long stationId, Integer stationType, Integer dataType) throws IOException {
        //获取excel内容
        List<GrainResultImportModel> importDataList = EasyExcel.read(file.getInputStream(), GrainResultImportModel.class, null).sheet().headRowNumber(2).doReadSync();
        if (CollectionUtils.isEmpty(importDataList)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_DATA_EMPTY_ERROR);
        }
        StationResultSaveReqVO stationResultSaveReqVO = new StationResultSaveReqVO();
        stationResultSaveReqVO.setStationId(stationId);
        stationResultSaveReqVO.setStationType(stationType);
        stationResultSaveReqVO.setDataType(dataType);
        dayDataToDbBuilder(stationResultSaveReqVO, importDataList, null, true);
    }


    /**
     * 站点-日序列数据 入库
     * @param importDataList
     */
    private void dayDataToDbBuilder(StationResultSaveReqVO updateReqVO, List<GrainResultImportModel> importDataList, List<Long> updateIdList, boolean importFlag) {

        Long stationId = updateReqVO.getStationId();
        Integer stationType = updateReqVO.getStationType();
        Integer dataType = updateReqVO.getDataType();

        //获取该统计表-年鉴格式相关的所有统计表的下一个版本
        //获取相关统计表的下个版本信息


        Integer versions =  fetchVersions(stationId, stationType, dataType);

        List<StationResultDO> stationResultList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(importDataList)) {
            //数据校验
            importDataList=dataCheck(importDataList, importFlag);
            //组装数据：本次更新或者导入的内容

            stationResultList = dataBuilder(stationId, stationType, dataType, importDataList, versions);

        }

        List<Long> idList = new ArrayList<>();

        CollUtil.addAll(idList, updateIdList);
        CollUtil.addAll(idList, updateReqVO.getDeleteIds());

        //写入数据
        dataUpdate(stationId,
                stationType,
                dataType,
                stationResultList,
                idList,
                importFlag);
    }


    private List<StationResultDO> dataBuilder(Long stationId, Integer stationType, Integer dataType, List<GrainResultImportModel> importDataList, Integer nextVersion) {
        String loginUserId = WebFrameworkUtils.getLoginUserId().toString();
        return importDataList.stream()
                //过滤不符合要求的日期数据
                .filter(item -> DateUtils.isValidDate(item.getYear()+"-"+item.getMonth()+"-"+item.getDay(), DateUtils.PATTERN))
                .map(item -> {
                    StationResultDO ResultDO = new StationResultDO();
                    ResultDO.setStationId(stationId);
                    ResultDO.setStationType(stationType);
                    ResultDO.setDataType(dataType);
                    ResultDO.setVersion(nextVersion);
                    ResultDO.setYear(item.getYear());
                    ResultDO.setMonth(item.getMonth());
                    ResultDO.setDay(item.getDay());
                    ResultDO.setRemark(item.getRemark());
                    ResultDO.setAnalysisMethod(item.getAnalysisMethod());
                    ResultDO.setSamplingMethod(item.getSamplingMethod());
                    ResultDO.setWaterTemperature(item.getWaterTemperature());
                    ResultDO.setAverageSinkSpeed(item.getAverageSinkSpeed());
                    ResultDO.setGrainMax(item.getGrainMax());
                    ResultDO.setGrainAverage(item.getGrainAverage());
                    ResultDO.setGrainMid(item.getGrainMid());
                    ResultDO.setGrain30(item.getGrain30());
                    ResultDO.setGrain20(item.getGrain20());
                    ResultDO.setGrain10(item.getGrain10());
                    ResultDO.setGrain5(item.getGrain5());
                    ResultDO.setGrain25(item.getGrain25());
                    ResultDO.setGrain1(item.getGrain1());
                    ResultDO.setGrain05(item.getGrain05());
                    ResultDO.setGrain025(item.getGrain025());
                    ResultDO.setGrain01(item.getGrain01());
                    ResultDO.setGrain005(item.getGrain005());
                    ResultDO.setGrain007(item.getGrain007());
                    ResultDO.setLatest(LatestEnum.LATEST.getType());
                    ResultDO.setCreator(loginUserId);
                    ResultDO.setUpdater(loginUserId);
                    return ResultDO;
                }).toList();
    }


    private List<GrainResultImportModel> dataCheck(List<GrainResultImportModel> importDataList, boolean importFlag) {
        //数据唯一性判断
        List<String> repeatStr = new ArrayList<>();
        if (CollectionUtil.isEmpty(importDataList)) {
            return null;
        }
        Integer temYear=null;
        Integer temMonth=null;
        Integer temDay=null;
        for (int i = 0; i < importDataList.size(); i++) {
            GrainResultImportModel importModel = importDataList.get(i);

            //校验年月日
            Integer year=null;
            Integer month=null;
            Integer day=null;
            if(importModel.getYear()==null)
            {
                year=temYear;
            }else {
                temYear=importModel.getYear();
                year=importModel.getYear();
            }
            if(importModel.getMonth()==null)
            {
                month=temMonth;
            }else {
                temMonth=importModel.getMonth();
                month=importModel.getMonth();
            }
            if(importModel.getDay()==null)
            {
                day=temDay;
            }else {
                temDay=importModel.getDay();
                day=importModel.getDay();
            }
            importDataList.get(i).setYear(year);
            importDataList.get(i).setMonth(month);
            importDataList.get(i).setDay(day);

            StringUtils.checkYearMonthDay(year, month, day, i + 2, importFlag);
//            if (repeatStr.contains(year + "-" + month + "-" + day+"")) {
//                if (importFlag) {
//                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_EXCERPT_REPEAT_EXISTS_ERROR, i + 2);
//                } else {
//                    throw exception(ErrorCodeConstants.EXCEL_IMPORT_RAINFALL_YMD_REPEAT_EXISTS_ERROR, year, month, day);
//                }
//                throw new ServiceException(ErrorCodeConstants.DATA_FORMAT_ERROR);
//            }
            repeatStr.add(year + "-" + month + "-" + day);
        }
        return importDataList;
    }

}





