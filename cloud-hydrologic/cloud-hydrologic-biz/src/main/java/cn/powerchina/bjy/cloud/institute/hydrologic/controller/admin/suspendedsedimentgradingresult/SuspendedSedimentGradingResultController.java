package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedsedimentgradingresult;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedsedimentgradingresult.vo.SuspendedSedimentGradingResultPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedsedimentgradingresult.vo.SuspendedSedimentGradingResultRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.suspendedsedimentgradingresult.vo.SuspendedSedimentGradingResultSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.suspendedsedimentgradingresult.SuspendedSedimentGradingResultDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.suspendedsedimentgradingresult.SuspendedSedimentGradingResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 实测推移质颗粒级配成果")
@RestController
@RequestMapping("/plan/hydrologic/suspended-sediment-grading-result")
@Validated
public class SuspendedSedimentGradingResultController {

    @Resource
    private SuspendedSedimentGradingResultService suspendedSedimentGradingResultService;

    @PostMapping("/create")
    @Operation(summary = "创建实测推移质颗粒级配成果")
    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:create')")
    public CommonResult<Long> createSuspendedSedimentGradingResult(@Valid @RequestBody SuspendedSedimentGradingResultSaveReqVO createReqVO) {
        return success(suspendedSedimentGradingResultService.createSuspendedSedimentGradingResult(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新实测推移质颗粒级配成果")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:update')")
    public CommonResult<Boolean> updateSuspendedSedimentGradingResult(@Valid @RequestBody SuspendedSedimentGradingResultSaveReqVO updateReqVO) {
        suspendedSedimentGradingResultService.updateSuspendedSedimentGradingResult(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除实测推移质颗粒级配成果")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:delete')")
    public CommonResult<Boolean> deleteSuspendedSedimentGradingResult(@RequestParam("id") Long id) {
        suspendedSedimentGradingResultService.deleteSuspendedSedimentGradingResult(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得实测推移质颗粒级配成果")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:query')")
    public CommonResult<SuspendedSedimentGradingResultRespVO> getSuspendedSedimentGradingResult(@RequestParam("id") Long id) {
        SuspendedSedimentGradingResultDO suspendedSedimentGradingResult = suspendedSedimentGradingResultService.getSuspendedSedimentGradingResult(id);
        return success(BeanUtils.toBean(suspendedSedimentGradingResult, SuspendedSedimentGradingResultRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得实测推移质颗粒级配成果分页")
    @HydrologicOperation
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:query')")
    public CommonResult<PageResult<SuspendedSedimentGradingResultRespVO>> getSuspendedSedimentGradingResultPage(@Valid SuspendedSedimentGradingResultPageReqVO pageReqVO) {
        PageResult<SuspendedSedimentGradingResultDO> pageResult = suspendedSedimentGradingResultService.getSuspendedSedimentGradingResultPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SuspendedSedimentGradingResultRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出实测推移质颗粒级配成果 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:export')")
    public void exportSuspendedSedimentGradingResultExcel(@Valid SuspendedSedimentGradingResultPageReqVO pageReqVO,
                                                          HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        //检索和记录 两个页面使用
        suspendedSedimentGradingResultService.exportExcel(response, pageReqVO);
    }

    @PostMapping("/import")
    @Operation(summary = "导入实测推移质颗粒级配成果")
//    @PreAuthorize("@ss.hasPermission('hydrologic:suspended-sediment-grading-result:import')")
    public CommonResult<Boolean> importSuspendedSedimentGradingResult(@RequestParam("file") MultipartFile file,
                                                                      @RequestParam("stationId") Long stationId,
                                                                      @RequestParam("stationType") Integer stationType,
                                                                      @RequestParam("dataType") Integer dataType) throws IOException {
        suspendedSedimentGradingResultService.importExcel(file, stationId, stationType, dataType);
        return success(true);
    }

}