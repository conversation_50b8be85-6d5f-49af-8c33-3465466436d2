package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyinfo;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyinfo.vo.SectionSurveyInfoPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyinfo.vo.SectionSurveyInfoRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyinfo.vo.SectionSurveyInfoSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sectionsurveyinfo.SectionSurveyInfoDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.sectionsurveyinfo.SectionSurveyInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 实测大断面成果年度基本信息")
@RestController
@RequestMapping("/hydrologic/section-survey-info")
@Validated
public class SectionSurveyInfoController {

    @Resource
    private SectionSurveyInfoService sectionSurveyInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建实测大断面成果年度基本信息")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-info:create')")
    public CommonResult<Long> createSectionSurveyInfo(@Valid @RequestBody SectionSurveyInfoSaveReqVO createReqVO) {
        return success(sectionSurveyInfoService.createSectionSurveyInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新实测大断面成果年度基本信息")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-info:update')")
    public CommonResult<Boolean> updateSectionSurveyInfo(@Valid @RequestBody SectionSurveyInfoSaveReqVO updateReqVO) {
        sectionSurveyInfoService.updateSectionSurveyInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除实测大断面成果年度基本信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-info:delete')")
    public CommonResult<Boolean> deleteSectionSurveyInfo(@RequestParam("id") Long id) {
        sectionSurveyInfoService.deleteSectionSurveyInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得实测大断面成果年度基本信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-info:query')")
    public CommonResult<SectionSurveyInfoRespVO> getSectionSurveyInfo(@RequestParam("id") Long id) {
        SectionSurveyInfoDO sectionSurveyInfo = sectionSurveyInfoService.getSectionSurveyInfo(id);
        return success(BeanUtils.toBean(sectionSurveyInfo, SectionSurveyInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得实测大断面成果年度基本信息分页")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-info:query')")
    public CommonResult<PageResult<SectionSurveyInfoRespVO>> getSectionSurveyInfoPage(@Valid SectionSurveyInfoPageReqVO pageReqVO) {
        PageResult<SectionSurveyInfoDO> pageResult = sectionSurveyInfoService.getSectionSurveyInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SectionSurveyInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出实测大断面成果年度基本信息 Excel")
    @PreAuthorize("@ss.hasPermission('hydrologic:section-survey-info:export')")
    public void exportSectionSurveyInfoExcel(@Valid SectionSurveyInfoPageReqVO pageReqVO,
                                             HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SectionSurveyInfoDO> list = sectionSurveyInfoService.getSectionSurveyInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "实测大断面成果年度基本信息.xls", "数据", SectionSurveyInfoRespVO.class,
                BeanUtils.toBean(list, SectionSurveyInfoRespVO.class));
    }

}