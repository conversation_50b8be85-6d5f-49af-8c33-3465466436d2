package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticaldirectory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 水文-统计表-目录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StatisticalDirectoryRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28356")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "parentId", example = "18273")
    @ExcelProperty("parentId")
    private Long parentId;

    @Schema(description = "目录、统计表、页签的名称", example = "张三")
    @ExcelProperty("目录、统计表、页签的名称")
    private String name;

    @Schema(description = "页签的code")
    @ExcelProperty("页签的code")
    private String code;

    @Schema(description = "站点类型，1：雨量站，2：水文站，3：气象站", example = "1")
    @ExcelProperty("站点类型，1：雨量站，2：水文站，3：气象站")
    private String stationType;

    @Schema(description = "前端组件name", example = "芋艿")
    @ExcelProperty("前端组件name")
    private String componentName;

    @Schema(description = "是否标签页，0否，1是，默认为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否标签页，0否，1是，默认为0")
    private Boolean isLabel;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}