package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainyear;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainyear.vo.StationRainYearBookUpdateReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainyear.vo.StationRainYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainyear.vo.StationRainYearRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainyear.vo.StationRainYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainyear.StationRainYearDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.model.RainFallDataRainYearModel;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationrainyear.StationRainYearService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 降水-年降水量")
@RestController
@RequestMapping("/plan/hydrologic/station/rain/year")
@Validated
public class StationRainYearController {

    @Resource
    private StationRainYearService stationRainYearService;

    @GetMapping("/page/rain")
    @Operation(summary = "雨量站-获得降水-年降水特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:query-rain')")
    public CommonResult<PageResult<StationRainYearRespVO>> getStationRainYearPage(@Valid StationRainYearPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        PageResult<StationRainYearDO> pageResult = stationRainYearService.getStationRainYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainYearRespVO.class));
    }

    @PostMapping("/import/rain")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入降水-年降水特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:import-rain')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importRainStationRainYearFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                       @RequestParam("file") MultipartFile file) {
        stationRainYearService.importStationRainYearFeatureExcel(stationId, StationEnum.RAIN.getType(), file);
        return success(true);
    }

    @PutMapping("/update/rain")
    @Operation(summary = "雨量站-更新降水-年降水特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:update-rain')")
    public CommonResult<Boolean> updateStationRainYear(@Valid @RequestBody StationRainYearSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.RAIN.getType());
        stationRainYearService.updateStationRainYear(updateReqVO);
        return success(true);
    }

    @PostMapping("/import/rainbook")
	@ExcelImportCheck
    @Operation(summary = "雨量站-导入降水-年降水量年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:import-rainbook')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    public CommonResult<Boolean> importRainStationRainYearExcel(@RequestParam("stationId") Long stationId,
                                                                @RequestParam("file") MultipartFile file) {
        stationRainYearService.importStationRainYearExcel(stationId, StationEnum.RAIN.getType(), file);
        return success(true);
    }

    @GetMapping("/rainbook/info")
    @Operation(summary = "雨量站-逐日降水量表-年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:rainbook-info')")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "logId", description = "记录id")
    public CommonResult<List<RainFallDataRainYearModel>> getStationRainYearBookList(@RequestParam("stationId") Long stationId,
                                                                                    @RequestParam(value = "logId", required = false) Long logId) {
        return success(stationRainYearService.findStationYearBookList(stationId, StationEnum.RAIN.getType(), logId, null));
    }

    @PostMapping("/rainbook/update")
    @Operation(summary = "雨量站-逐日降水量表-年鉴格式-修改数据")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:rainbook-update')")
    public CommonResult<Boolean> updateStationRainYearBookList(@RequestBody StationRainYearBookUpdateReqVO reqVO) {
        stationRainYearService.modifyStationYearBookList(reqVO.getStationId(), StationEnum.RAIN.getType(), reqVO.getDataMode());
        return success(true);
    }

    @GetMapping("/page/hydrologic")
    @Operation(summary = "水文站-获得降水-年降水量特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:query-hydrologic')")
    public CommonResult<PageResult<StationRainYearRespVO>> getStationhydrologicYearPage(@Valid StationRainYearPageReqVO pageReqVO) {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        PageResult<StationRainYearDO> pageResult = stationRainYearService.getStationRainYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainYearRespVO.class));
    }

    @PostMapping("/import/hydrologic")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入降水-年降水特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:import-hydrologic')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    public CommonResult<Boolean> importRainStationHydrologicYearFeatureExcel(@RequestParam("stationId") Long stationId,
                                                                             @RequestParam("file") MultipartFile file) {
        stationRainYearService.importStationRainYearFeatureExcel(stationId, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PutMapping("/update/hydrologic")
    @Operation(summary = "水文站-更新降水-年降水特征值")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:update-hydrologic')")
    public CommonResult<Boolean> updateStationHydrologicYear(@Valid @RequestBody StationRainYearSaveReqVO updateReqVO) {
        updateReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        stationRainYearService.updateStationRainYear(updateReqVO);
        return success(true);
    }

    @GetMapping("/hydrologicbook/info")
    @Operation(summary = "水文站-逐日降水量表-年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:hydrologicbook-info')")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "logId", description = "记录id")
    public CommonResult<List<RainFallDataRainYearModel>> getStationHydrologicYearBookList(@RequestParam("stationId") Long stationId,
                                                                                          @RequestParam(value = "logId", required = false) Long logId) {
        return success(stationRainYearService.findStationYearBookList(stationId, StationEnum.HYDROLOGIC.getType(), logId, null));
    }

    @PostMapping("/import/hydrologicbook")
	@ExcelImportCheck
    @Operation(summary = "水文站-导入降水-年降水量年鉴格式")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:import-hydrologicbook')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
    public CommonResult<Boolean> importHydrologicStationRainYearExcel(@RequestParam("stationId") Long stationId,
                                                                      @RequestParam("file") MultipartFile file) {
        stationRainYearService.importStationRainYearExcel(stationId, StationEnum.HYDROLOGIC.getType(), file);
        return success(true);
    }

    @PostMapping("/hydrologicbook/update")
    @Operation(summary = "水文站-逐日降水量表-年鉴格式-修改数据")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:update-hydrologicbook')")
    public CommonResult<Boolean> updateStationHydrologicYearBookList(@RequestBody StationRainYearBookUpdateReqVO reqVO) {
        stationRainYearService.modifyStationYearBookList(reqVO.getStationId(), StationEnum.HYDROLOGIC.getType(), reqVO.getDataMode());
        return success(true);
    }

    @GetMapping("/export/rain")
    @Operation(summary = "雨量站-导出降水-年降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:export-rain')")
    public void exportStationRainYearExcel(@Valid StationRainYearPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainYearService.exportStationRainYear(response, pageReqVO);
    }

    @GetMapping("/export/hydrologic")
    @Operation(summary = "水文站-导出降水-年降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:export-hydrologic')")
//    @OperateLog(type = EXPORT)
    public void exportStationHydrologicYearExcel(@Valid StationRainYearPageReqVO pageReqVO,
                                                 HttpServletResponse response) throws IOException {
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainYearService.exportStationRainYear(response, pageReqVO);
    }

    @GetMapping("/page/data/rain")
    @HydrologicOperation(stationType = StationEnum.RAIN, dataType = StationDataTypeEnum.STATION_RAIN_YEAR)
    @Operation(summary = "雨量站-数据检索-获得降水-年降水特征值分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:query-data-rain')")
    public CommonResult<PageResult<StationRainYearRespVO>> getStationRainYearDataPage(@Valid StationRainYearPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        PageResult<StationRainYearDO> pageResult = stationRainYearService.getStationRainYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainYearRespVO.class));
    }

    @GetMapping("/page/data/hydrologic")
    @Operation(summary = "水文站-数据检索-获得降水-年降水特征值分页")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_RAIN_YEAR)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:query-data-hydrologic')")
    public CommonResult<PageResult<StationRainYearRespVO>> getStationHydrologicYearDataPage(@Valid StationRainYearPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        PageResult<StationRainYearDO> pageResult = stationRainYearService.getStationRainYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StationRainYearRespVO.class));
    }

    @GetMapping("/export/data/rain")
    @Operation(summary = "雨量站-数据检索-导出降水-年降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:export-data-rain')")
    public void exportStationRainYearDataExcel(@Valid StationRainYearPageReqVO pageReqVO,
                                               HttpServletResponse response) {
        pageReqVO.setLogId(null);
        pageReqVO.setStationType(StationEnum.RAIN.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainYearService.exportStationRainYear(response, pageReqVO);
    }

    @GetMapping("/export/data/hydrologic")
    @Operation(summary = "水文站-数据检索-导出降水-年降水量 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:export-data-hydrologic')")
    public void exportStationHydrologicYearDataExcel(@Valid StationRainYearPageReqVO pageReqVO,
                                                     HttpServletResponse response) {
        pageReqVO.setLogId(null);
        pageReqVO.setStationType(StationEnum.HYDROLOGIC.getType());
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        stationRainYearService.exportStationRainYear(response, pageReqVO);
    }

    @GetMapping("/export/rainbook")
    @Operation(summary = "雨量站-导出降水-年降水量年鉴格式 Excel")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "logId", description = "记录id")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:export-rainbook')")
    public void exportStationRainYearBookExcel(@RequestParam("stationId") Long stationId,
                                               @RequestParam(value = "logId", required = false) Long logId,
                                               HttpServletResponse response) {
        stationRainYearService.exportStationRainYearBookExcel(response, stationId, StationEnum.RAIN.getType(), logId, null);
    }

    @GetMapping("/export/hydrologicbook")
    @Operation(summary = "水文站-导出降水-年降水量年鉴格式 Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "logId", description = "记录id")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:export-hydrologicbook')")
    public void exportStationRainHydrologicBookExcel(@RequestParam("stationId") Long stationId,
                                                     @RequestParam(value = "logId", required = false) Long logId,
                                                     HttpServletResponse response) {
        stationRainYearService.exportStationRainYearBookExcel(response, stationId, StationEnum.HYDROLOGIC.getType(), logId, null);
    }

    @GetMapping("/rainbook/data/info")
    @Operation(summary = "雨量站-数据检索-逐日降水量表-年鉴格式")
    @HydrologicOperation(stationType = StationEnum.RAIN, dataType = StationDataTypeEnum.STATION_RAIN_YEAR_BOOK)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:rainbook-data-info')")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "year", description = "年")
    public CommonResult<List<RainFallDataRainYearModel>> getStationRainYearBookDataList(@RequestParam("stationId") Long stationId,
                                                                                        @RequestParam(value = "year", required = false) Integer year) {
        if (Objects.isNull(year)) {
            return success(new ArrayList<>());
        }
        return success(stationRainYearService.findStationYearBookList(stationId, StationEnum.RAIN.getType(), null, year));
    }

    @GetMapping("/hydrologicbook/data/info")
    @Operation(summary = "水文站-数据检索-逐日降水量表-年鉴格式")
    @HydrologicOperation(stationType = StationEnum.HYDROLOGIC, dataType = StationDataTypeEnum.STATION_RAIN_YEAR_BOOK)
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:hydrologicbook-data-info')")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "year", description = "年")
    public CommonResult<List<RainFallDataRainYearModel>> getStationHydrologicYearBookDataList(@RequestParam("stationId") Long stationId,
                                                                                              @RequestParam(value = "year", required = false) Integer year) {
        if (Objects.isNull(year)) {
            return success(new ArrayList<>());
        }
        return success(stationRainYearService.findStationYearBookList(stationId, StationEnum.HYDROLOGIC.getType(), null, year));
    }

    @GetMapping("/export/data/rainbook")
    @Operation(summary = "雨量站-数据检索-导出降水-年降水量年鉴格式 Excel")
    @Parameter(name = "stationId", description = "雨量站id", required = true)
    @Parameter(name = "year", description = "年")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:export-data-rainbook')")
    public void exportStationRainYearBookDataExcel(@RequestParam("stationId") Long stationId,
                                                   @RequestParam(value = "year", required = false) Integer year,
                                                   HttpServletResponse response) {
        if (Objects.isNull(year)) {
            return;
        }
        stationRainYearService.exportStationRainYearBookExcel(response, stationId, StationEnum.RAIN.getType(), null, year);
    }

    @GetMapping("/export/data/hydrologicbook")
    @Operation(summary = "水文站-数据检索-导出降水-年降水量年鉴格式 Excel")
    @Parameter(name = "stationId", description = "水文站id", required = true)
    @Parameter(name = "year", description = "年")
//    @PreAuthorize("@ss.hasPermission('hydrologic:station-rain-year:export-data-hydrologicbook')")
    public void exportStationHydrologicYearBookDataExcel(@RequestParam("stationId") Long stationId,
                                                         @RequestParam(value = "year", required = false) Integer year,
                                                         HttpServletResponse response) {
        if (Objects.isNull(year)) {
            return;
        }
        stationRainYearService.exportStationRainYearBookExcel(response, stationId, StationEnum.HYDROLOGIC.getType(), null, year);
    }

}