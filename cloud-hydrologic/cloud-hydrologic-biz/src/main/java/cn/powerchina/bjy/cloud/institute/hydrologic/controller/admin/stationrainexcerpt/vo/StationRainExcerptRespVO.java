package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainexcerpt.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 降水-摘录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationRainExcerptRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private String month;

    @Schema(description = "日")
    @ExcelProperty("日")
    private String day;

    @Schema(description = "降水量")
    @ExcelProperty("降水量")
    private String value;

    @Schema(description = "起	时:分")
    @ExcelProperty("起	时:分")
    private String startHourMinute;

    @Schema(description = "止	时:分")
    @ExcelProperty("止	时:分")
    private String endHourMinute;

}