package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationyear.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 蒸发-年水面蒸发量特征值修改 Request VO")
@Data
public class StationEvaporationYearSaveReqVO {


    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationEvaporateYearData> dataList;

    @Schema(description = "管理后台 - 蒸发-年水面蒸发量特征值数据修改 Request VO")
    @Data
    public static class StationEvaporateYearData {

        @Schema(description = "年")
        private String year;

        @Schema(description = "平均水面蒸发量(mm)")
        private String averageValue;

        @Schema(description = "最大日水面蒸发量(mm)")
        private String maxValue;

        @Schema(description = "最大日水面蒸发量日期")
        private String maxValueDate;

        @Schema(description = "最小日水面蒸发量(mm)")
        private String minValue;

        @Schema(description = "最小日水面蒸发量日期")
        private String minValueDate;

        @Schema(description = "初冰日期")
        private String firstIceDate;

        @Schema(description = "终冰日期")
        private String endIceDate;

        @Schema(description = "备注", example = "随便")
        private String remark;
    }


}