package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 气象-六要素观测成果表(中科正奇ZK-ZDx)分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ElementObservationsZkzdResultPageReqVO extends PageParam {
    @Schema(description = "气象站id", example = "23323")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "1")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] time;

    @Schema(description = "日雨量（mm）")
    private String dailyRainfall;

    @Schema(description = "风向编码")
    private String windCode;

    @Schema(description = "风向")
    private String wind;

    @Schema(description = "温度(℃)")
    private String temperature;

    @Schema(description = "湿度(%)")
    private String humidity;

    @Schema(description = "大气压(Kpa)")
    private String atmospheric;

    @Schema(description = "风速(m/s)")
    private String velocity;

    @Schema(description = "分钟雨量（mm)")
    private String minuteRainfall;

    @Schema(description = "电压(V）")
    private String voltage;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "记录时间")
    private LocalDate currentDay;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
