package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo.StatisticalIndexRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.station.vo.StationPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticalinfo.StatisticalInfoDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticalinfo.StatisticalInfoService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jodd.util.StringUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "规划院 - 水文-统计表索引数据信息")
@RestController
@RequestMapping("/plan/hydrologic/statistical-info")
@Validated
public class StatisticalInfoController {

    @Resource
    private StatisticalInfoService statisticalInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建水文-统计表索引数据信息")
    // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-info:create')")
    public CommonResult<Long> createStatisticalInfo(@Valid @RequestBody StatisticalInfoSaveReqVO createReqVO) {
        return success(statisticalInfoService.createStatisticalInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水文-统计表索引数据信息")
    // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-info:update')")
    public CommonResult<Boolean> updateStatisticalInfo(@Valid @RequestBody StatisticalInfoSaveReqVO updateReqVO) {
        statisticalInfoService.updateStatisticalInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水文-统计表索引数据信息")
    @Parameter(name = "id", description = "编号", required = true)
    // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-info:delete')")
    public CommonResult<Boolean> deleteStatisticalInfo(@RequestParam("id") Long id) {
        statisticalInfoService.deleteStatisticalInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水文-统计表索引数据信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-info:query')")
    public CommonResult<StatisticalInfoRespVO> getStatisticalInfo(@RequestParam("id") Long id) {
        StatisticalInfoDO statisticalInfo = statisticalInfoService.getStatisticalInfo(id);
        return success(BeanUtils.toBean(statisticalInfo, StatisticalInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水文-统计表索引数据信息分页")
    // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-info:query')")
    public CommonResult<PageResult<StatisticalInfoRespVO>> getStatisticalInfoPage(@Valid StatisticalInfoPageReqVO pageReqVO) {
        PageResult<StatisticalInfoDO> pageResult = statisticalInfoService.getStatisticalInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StatisticalInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水文-统计表索引数据信息 Excel")
    // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-info:export')")
//    @OperateLog(type = EXPORT)
    public void exportStatisticalInfoExcel(@Valid StatisticalInfoPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<StatisticalInfoDO> list = statisticalInfoService.getStatisticalInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "水文-统计表索引数据信息.xls", "数据", StatisticalInfoRespVO.class,
                BeanUtils.toBean(list, StatisticalInfoRespVO.class));
    }

    @PostMapping("/getTree")
    @Operation(summary = "水文-统计表-目录")
    public CommonResult<List<StatisticalInexDataRespVO>> getTree(@Valid @RequestBody StatisticInfoGetTreeRequestVo req) {
        List<StatisticalInexDataRespVO> list = statisticalInfoService.getTree(req);
        return success(list);
    }


    @GetMapping("/getStatisticalList")
    @Operation(summary = "水文-统计表-获取站点统计表数据")
    public CommonResult<StatisticalIndexRespVO> getStatisticalIndex(@RequestParam("stationId") Long stationId,
                                                                    @RequestParam(value = "statsId", required = false) Long statsId) {
        StatisticalIndexRespVO respVO = statisticalInfoService.getStatisticalIndex(stationId, statsId);
        return success(respVO);
    }


    @GetMapping("/getForm/page")
    @Operation(summary = "水文-统计表-获得站点表单列表分页统计表数据")
    public CommonResult<PageResult<StatisticalIndexRespVO>> getStationAndFormPage(@Valid StationPageReqVO pageReqVO) {
        PageResult<StatisticalIndexRespVO> pageResult = statisticalInfoService.getStationPageAndForm(pageReqVO);
        return success(pageResult);
    }


    @GetMapping("/export")
    @Operation(summary = "导出水文-统计表索引数据信息 Excel")
    // @PreAuthorize("@ss.hasPermission('hydrologic:statistical-info:export')")
//    @OperateLog(type = EXPORT)
    public void exportStatisticalInfo(@Valid StationPageReqVO pageReqVO,
                                      HttpServletResponse response){
        PageResult<StatisticalIndexRespVO> pageResult = statisticalInfoService.getStationPageAndForm(pageReqVO);
        if(StringUtil.isBlank(pageReqVO.getStationIds())){
            //单站点
            statisticalInfoService.exportOneTypeStationExcel(response,pageResult.getList(),pageReqVO.getStationType());
        }else{
            statisticalInfoService.exportExcelStation(response,pageResult.getList());
        }
    }



}