package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationextract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 站点-摘录新增/修改 Request VO")
@Data
public class StationExtractSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8726")
    private Long id;

    @Schema(description = "水文站id", example = "30784")
    private Long stationId;

    @Schema(description = "站点类型，1：雨量站，2：水文站， 3：气象站", example = "2")
    private Integer stationType;

    @Schema(description = "数据类型", example = "1")
    private Integer dataType;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "月")
    private Integer month;

    @Schema(description = "日")
    private Integer day;

    @Schema(description = "时分")
    private String hoursMinute;

    @Schema(description = "冰情")
    private String iceSituation;

    @Schema(description = "冰厚(m)")
    private String iceThickness;

    @Schema(description = "冰上雪深(m)")
    private String deepSnowOnIce;

    @Schema(description = "岸上气温(C°)")
    private String shoreTemperature;

    @Schema(description = "水位(m)")
    private String waterLevel;

    @Schema(description = "水文站-洪水水文要素摘录表-流量(m3/s)")
    private String flow;

    @Schema(description = "水文站-洪水水文要素摘录表-含沙量(kg/m3)")
    private String sandValue;

    @Schema(description = "降水量")
    private String value;

    @Schema(description = "起时:分")
    private String startHourMinute;

    @Schema(description = "止时:分")
    private String endHourMinute;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "最新版本（1：最新；0：历史）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer latest;

    @Schema(description = "记录日期")
    private LocalDate currentDay;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationExtractData> dataList;

    @Schema(description = "本次更新删除的数据id")
    private List<Long> deleteIds;

    @Data
    public static class StationExtractData {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "水文站id", example = "30784")
        private Long stationId;

        @Schema(description = "数据类型", example = "1")
        private Integer dataType;

        @Schema(description = "年")
        private String year;

        @Schema(description = "月")
        private String month;

        @Schema(description = "日")
        private String day;

        @Schema(description = "时分")
        private String hoursMinute;

        @Schema(description = "冰情")
        private String iceSituation;

        @Schema(description = "冰厚(m)")
        private String iceThickness;

        @Schema(description = "冰上雪深(m)")
        private String deepSnowOnIce;

        @Schema(description = "岸上气温(C°)")
        private String shoreTemperature;

        @Schema(description = "水位(m)")
        private String waterLevel;

        @Schema(description = "水文站-洪水水文要素摘录表-流量(m3/s)")
        private String flow;

        @Schema(description = "水文站-洪水水文要素摘录表-含沙量(kg/m3)")
        private String sandValue;

        @Schema(description = "降水量")
        private String value;

        @Schema(description = "起时:分")
        private String startHourMinute;

        @Schema(description = "止时:分")
        private String endHourMinute;

        @Schema(description = "备注", example = "你说的对")
        private String remark;
    }
}