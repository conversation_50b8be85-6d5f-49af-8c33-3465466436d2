package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainperiodminute.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 降水-各时段最大降水量(分钟时段)新增/修改 Request VO")
@Data
public class StationRainPeriodMinuteSaveReqVO {

    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "数据类型")
    private Integer dataType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationRainPeriodMinuteData> dataList;

    @Schema(description = "要删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "管理后台 - 降水-各时段最大降水量(小时时段)数据")
    @Data
    public static class StationRainPeriodMinuteData {

        @Schema(description = "id")
        private Long id;

        @Schema(description = "年")
        private String year;

        @Schema(description = "10min降水量")
        private String valueDay10;

        @Schema(description = "10min开始月日")
        private String monthDayStart10;

        @Schema(description = "20min降水量")
        private String valueDay20;

        @Schema(description = "20min开始月日")
        private String monthDayStart20;

        @Schema(description = "30min降水量")
        private String valueDay30;

        @Schema(description = "30min开始月日")
        private String monthDayStart30;

        @Schema(description = "45min降水量")
        private String valueDay45;

        @Schema(description = "45min开始月日")
        private String monthDayStart45;

        @Schema(description = "60min降水量")
        private String valueDay60;

        @Schema(description = "60min开始月日")
        private String monthDayStart60;

        @Schema(description = "90min降水量")
        private String valueDay90;

        @Schema(description = "90min开始月日")
        private String monthDayStart90;

        @Schema(description = "120min降水量")
        private String valueDay120;

        @Schema(description = "120min开始月日")
        private String monthDayStart120;

        @Schema(description = "180min降水量")
        private String valueDay180;

        @Schema(description = "180min开始月日")
        private String monthDayStart180;

        @Schema(description = "240min降水量")
        private String valueDay240;

        @Schema(description = "240min开始月日")
        private String monthDayStart240;

        @Schema(description = "360min降水量")
        private String valueDay360;

        @Schema(description = "360min开始月日")
        private String monthDayStart360;

        @Schema(description = "540min降水量")
        private String valueDay540;

        @Schema(description = "540min开始月日")
        private String monthDayStart540;

        @Schema(description = "720min降水量")
        private String valueDay720;

        @Schema(description = "720min开始月日")
        private String monthDayStart720;

        @Schema(description = "1440min降水量")
        private String valueDay1440;

        @Schema(description = "1440min开始月日")
        private String monthDayStart1440;

        @Schema(description = "备注")
        private String remark;
    }

}