package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.sectionsurveyresult.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "实测大断面成果垂线明细数据 VO")
public class SectionSurveyDetailVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "基本信息ID")
    private Long infoId;

    @Schema(description = "垂线号")
    // 包含"左岸"、数字编号、"右岸"
    private String verticalLineNo;

    @Schema(description = "起点距(m)")
    private BigDecimal startDistance;

    @Schema(description = "河底高程(m)")
    private BigDecimal riverBedElevation;
}