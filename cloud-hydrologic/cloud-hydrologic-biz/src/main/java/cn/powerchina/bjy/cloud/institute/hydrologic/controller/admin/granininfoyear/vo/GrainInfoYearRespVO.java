package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.granininfoyear.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水文-月年平均悬移质颗粒级配 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GrainInfoYearRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "28959")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31432")
    @ExcelProperty("水文站id")
    private Long stationId;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "0.005粒径级（mm）")
    @ExcelProperty("0.005粒径级（mm）")
    private String grain005;

    @Schema(description = "0.007粒径级（mm）")
    @ExcelProperty("0.007粒径级（mm）")
    private String grain007;

    @Schema(description = "0.01粒径级（mm）")
    @ExcelProperty("0.01粒径级（mm）")
    private String grain01;

    @Schema(description = "0.025粒径级（mm）")
    @ExcelProperty("0.025粒径级（mm）")
    private String grain025;

    @Schema(description = "0.05粒径级（mm）")
    @ExcelProperty("0.05粒径级（mm）")
    private String grain05;

    @Schema(description = "0.1粒径级（mm）")
    @ExcelProperty("0.1粒径级（mm）")
    private String grain1;

    @Schema(description = "0.25粒径级（mm）")
    @ExcelProperty("0.25粒径级（mm）")
    private String grain25;

    @Schema(description = "0.5粒径级（mm）")
    @ExcelProperty("0.5粒径级（mm）")
    private String grain5;

    @Schema(description = "1.0粒径级（mm）")
    @ExcelProperty("1.0粒径级（mm）")
    private String grain10;

    @Schema(description = "2.0粒径级（mm）")
    @ExcelProperty("2.0粒径级（mm）")
    private String grain20;

    @Schema(description = "3.0粒径级（mm）")
    @ExcelProperty("3.0粒径级（mm）")
    private String grain30;

    @Schema(description = "中数粒径(mm)", example = "24567")
    @ExcelProperty("中数粒径(mm)")
    private String grainMid;

    @Schema(description = "平均粒径(mm)")
    @ExcelProperty("平均粒径(mm)")
    private String grainAverage;

    @Schema(description = "最大粒径(mm)")
    @ExcelProperty("最大粒径(mm)")
    private String grainMax;

    @Schema(description = "版本（根据原型待定）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}