package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.elementobservationszkzdresult.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.velocitywindresult.vo.VelocityWindResultSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
@Data
public class UpdateElementObservationsZkzdReqVO {
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "15581")
    private Long id;

    @Schema(description = "水文站id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11040")
    @NotNull(message = "水文站id不能为空")
    private Long stationId;

    @Schema(description = "站点类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "站点类型不能为空")
    private Integer stationType;

    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "数据类型不能为空")
    private Integer dataType;

    @Schema(description = "版本（根据原型待定）")
    private Integer version;

    @Schema(description = "最新版本（1：最新，0：历史版本，默认为1）")
    private Integer latest;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<ElementObservationsZkzdResultSaveReqVO> dataList;

    @Schema(description = "本次更新删除的数据id")
    private List<Long> deleteIds;

    @Schema(description = "是否删除 true是 ")
    private Boolean isDelete;

    @Schema(description = "年份")
    @NotNull(message = "年份不能为空")
    private Integer year;
}
