package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationevaporationmonth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 蒸发-月水面蒸发量特征值新增/修改 Request VO")
@Data
public class StationEvaporationMonthSaveReqVO {


    @Schema(description = "站点id")
    private Long stationId;

    @Schema(description = "站点类型，不用赋值，后台自己赋值")
    private Integer stationType;

    @Schema(description = "编辑的数据，只给编辑的")
    private List<StationEvaporationMonthData> dataList;

    @Schema(description = "管理后台 - 蒸发-月水面蒸发量特征值修改 Request VO")
    @Data
    public static class StationEvaporationMonthData {

        @Schema(description = "主键id")
        private Long id;

        @Schema(description = "年")
        private String year;

        @Schema(description = "月")
        private String month;

        @Schema(description = "月平均水面蒸发量(mm)")
        private String averageValue;

        @Schema(description = "月平均水面蒸发量备注")
        private String averageValueRemark;

        @Schema(description = "最大日水面蒸发量(mm)")
        private String maxValue;

        @Schema(description = "最大日水面蒸发量备注")
        private String maxValueRemark;

        @Schema(description = "最小日水面蒸发量(mm)")
        private String minValue;

        @Schema(description = "最小日水面蒸发量备注")
        private String minValueRemark;

    }

}