package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.stationrainmonth.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 降水-月降水量 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StationRainMonthRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "年")
    @ExcelProperty("年")
    private String year;

    @Schema(description = "月")
    @ExcelProperty("月")
    private String month;

    @Schema(description = "月降水量(mm)")
    @ExcelProperty("月降水量(mm)")
    private String value;

    @Schema(description = "月降水日数")
    @ExcelProperty("月降水日数")
    private Integer valueDays;

    @Schema(description = "最大日降水量(mm)")
    @ExcelProperty("最大日降水量(mm)")
    private String maxDayValue;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}