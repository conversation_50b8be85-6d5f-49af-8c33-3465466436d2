package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement;

import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.StationInfoRespIndexVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.enums.ResourceSitePermissionEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.vo.ResourceSiteManagementPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.vo.ResourceSiteManagementRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.resourcesitemanagement.vo.ResourceSiteManagementSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.resourcesitemanagement.ResourceSiteManagementService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.roleapply.RoleApplyService;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import cn.powerchina.bjy.cloud.system.enums.permission.RoleCodeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;
import static cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

@Tag(name = "管理后台 - 资源站点管理")
@RestController
@RequestMapping("/plan/hydrologic/resourcesitemanagement")
@Validated
public class ResourceSiteManagementController {
    @Resource
    private PermissionApi  permissionApi;

    @Resource
    private RoleApplyService roleApplyService;

    @Resource
    private ResourceSiteManagementService resourceSiteManagementService;

    @PostMapping("/create")
    @Operation(summary = "创建资源站点管理")
    //@PreAuthorize("@ss.hasPermission('hydrologic:project:create')")
    public CommonResult<Long> createProject(@Valid @RequestBody ResourceSiteManagementSaveReqVO createReqVO) {
        return success(resourceSiteManagementService.createResourceSiteManagement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资源站点管理")
    //@PreAuthorize("@ss.hasPermission('hydrologic:project:update')")
    public CommonResult<Boolean> updateProject(@Valid @RequestBody ResourceSiteManagementSaveReqVO updateReqVO) {
        resourceSiteManagementService.updateResourceSiteManagement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资源站点管理")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('hydrologic:project:delete')")
    public CommonResult<Boolean> deleteProject(@RequestParam("id") Long id) {
        resourceSiteManagementService.deleteResourceSiteManagement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资源站点管理")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('hydrologic:project:query')")
    public CommonResult<ResourceSiteManagementRespVO> getProject(@RequestParam("id") Long id) {
        ResourceSiteManagementRespVO project = resourceSiteManagementService.getResourceSiteManagement(id);
        return success(project);
    }

    @GetMapping("/page")
    @Operation(summary = "获得资源站点管理分页")
    //@PreAuthorize("@ss.hasPermission('hydrologic:project:query')")
    public CommonResult<PageResult<ResourceSiteManagementRespVO>> getProjectPage(@Valid ResourceSiteManagementPageReqVO pageReqVO) {
        PageResult<ResourceSiteManagementRespVO> pageResult = resourceSiteManagementService.getResourceSiteManagementPage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/import")
	@ExcelImportCheck
    @Operation(summary = "导入资源站点管理 Excel")
    //@PreAuthorize("@ss.hasPermission('hydrologic:project:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    public CommonResult<Boolean> importProjectExcel(@RequestParam("file") MultipartFile file) {
        resourceSiteManagementService.importResourceSiteManagement(file);
        return success(true);
    }



    @GetMapping("/tree")
    @Operation(summary = "获取资源站点目录树")
    public CommonResult<List<StationInfoRespIndexVO>> getResourceSiteTree() {
        // 1. 权限校验
        CommonResult<Boolean> permissionResult = permissionApi.hasAnyPermissions(getLoginUserId(), ResourceSitePermissionEnum.VIEW.getPermission());
        if (permissionResult == null || !Boolean.TRUE.equals(permissionResult.getData())) {
            throw new ServiceException(ErrorCodeConstants.FORBIDDEN);
        }

        // 2. 获取用户角色，判断是否为超级管理员
        List<RoleRespDTO> roles = roleApplyService.findRoleByUserId(getLoginUserId());
        boolean isSuperAdmin = roles.stream()
                .anyMatch(role -> RoleCodeEnum.SUPER_ADMIN.getCode().equals(role.getCode()));

        // 3. 查询站点数据并构建树形结构
        List<StationInfoRespIndexVO> tree = resourceSiteManagementService.buildResourceSiteTree(isSuperAdmin, getLoginUserId());

        return success(tree);
    }

}