package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringproject.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
@Data
public class IndexPageReqVO extends PageParam {
//    @Schema(description = "检索类型")
//    private String searchType;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "地市")
    private String city;

//    @Schema(description = "站点类型")
//    private String stationType;

//    @Schema(description = "监测项目")
//    private String monitoringItems;
//
//    @Schema(description = "系列长度")
//    private String seriesLength;

    @Schema(description = "工作深度")
    private String progress;

    @Schema(description = "是否纳规")
    private String isNaGui;

    @Schema(description = "装机容量M")
    private String InstalledCapacityM;

    @Schema(description = "装机容量W")
    private String InstalledCapacityW;

    @Schema(description = "连续满发小时数")
    private String ContinuousHours;
//
//    @Schema(description = "站点集合")
//    private List<String> stationIdList;

//    @Schema(description = "页面类型，1：数据录入，2：数据搜索")
//    private String pageType;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;
}
