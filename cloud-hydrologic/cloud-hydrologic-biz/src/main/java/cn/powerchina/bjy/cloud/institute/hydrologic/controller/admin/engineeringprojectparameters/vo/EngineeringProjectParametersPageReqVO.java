package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.engineeringprojectparameters.vo;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 工程项目信息-设计参数分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EngineeringProjectParametersPageReqVO  extends PageParam{
    @Schema(description = "项目关联id", example = "5737")
    private String projectId;

    @Schema(description = "参数")
    private String value1;

    @Schema(description = "规划")
    private String value2;

    @Schema(description = "规划备注")
    private String value3;

    @Schema(description = "预可")
    private String value4;

    @Schema(description = "预可备注")
    private String value5;

    @Schema(description = "三专")
    private String value6;

    @Schema(description = "三专备注")
    private String value7;

    @Schema(description = "可研")
    private String value8;

    @Schema(description = "可研备注")
    private String value9;

    @Schema(description = "详图")
    private String value10;

    @Schema(description = "详图备注")
    private String value11;

    @Schema(description = "已建")
    private String value12;

    @Schema(description = "已建备注")
    private String value13;


    @Schema(description = "序列")
    private Long num;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;
}
