package cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticalinfo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.station.vo.StationPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.station.vo.StationRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.statisticalinfo.vo.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.station.StationDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticaldirectory.StatisticalDirectoryDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.statisticalinfo.StatisticalInfoDO;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.station.StationMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.statisticalinfo.StatisticalInfoMapper;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.*;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.DataProcessor;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.station.StationService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.stationday.DataTypeRelation;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticaldirectory.StatisticalDirectoryService;
import cn.powerchina.bjy.cloud.institute.hydrologic.util.MergeStrategy;
import cn.powerchina.bjy.cloud.system.api.area.AreaApi;
import cn.powerchina.bjy.cloud.system.api.area.dto.AreaRespDTO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.dromara.hutool.core.bean.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.hydrologic.enums.HydrologicDataProjectEnum.*;


/**
 * 水文-统计表索引数据信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class StatisticalInfoServiceImpl implements StatisticalInfoService {

    @Resource
    private StatisticalInfoMapper statisticalInfoMapper;
    @Autowired
    private StatisticalDirectoryService statisticalDirectoryService;
    @Autowired
    private StationService stationService;
    @Resource
    private StationMapper stationMapper;
    @Resource
    private AreaApi areaApi;

    @Override
    public Long createStatisticalInfo(StatisticalInfoSaveReqVO createReqVO) {
        // 插入
        StatisticalInfoDO statisticalInfo = BeanUtils.toBean(createReqVO, StatisticalInfoDO.class);
        statisticalInfoMapper.insert(statisticalInfo);
        // 返回
        return statisticalInfo.getId();
    }

    @Override
    public void updateStatisticalInfo(StatisticalInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateStatisticalInfoExists(updateReqVO.getId());
        // 更新
        StatisticalInfoDO updateObj = BeanUtils.toBean(updateReqVO, StatisticalInfoDO.class);
        statisticalInfoMapper.updateById(updateObj);
    }

    @Override
    public void deleteStatisticalInfo(Long id) {
        // 校验存在
        validateStatisticalInfoExists(id);
        // 删除
        statisticalInfoMapper.deleteById(id);
    }

    private void validateStatisticalInfoExists(Long id) {
        if (statisticalInfoMapper.selectById(id) == null) {
//            throw exception(STATISTICAL_INFO_NOT_EXISTS);
        }
    }

    @Override
    public StatisticalInfoDO getStatisticalInfo(Long id) {
        return statisticalInfoMapper.selectById(id);
    }

    @Override
    public PageResult<StatisticalInfoDO> getStatisticalInfoPage(StatisticalInfoPageReqVO pageReqVO) {
        return statisticalInfoMapper.selectPage(pageReqVO);
    }

    @Override
    public List<StatisticalInexDataRespVO> getTree(StatisticInfoGetTreeRequestVo req) {
        List<StatisticalInexDataRespVO> result = new ArrayList<>();
        if ("1".equals(req.getStationType())) {
            // 查询并过滤数据
            List<StatisticalInexDataRespVO> list = listRainStatisticalIndexData(req.getStationType(), req.getStationId());

            // 构建树形结构
            result = new ArrayList<>();
            buildTree(list, null, result);

            // 检查并移除 "泥沙" 节点
            removeMudNodeIfRequired(result);
        }
        else if ("2".equals(req.getStationType())) {

            // 查询并过滤数据
            List<StatisticalInexDataRespVO> list = listStatisticalIndexData(req.getStationType(), req.getStationId());

            // 构建树形结构
            result = new ArrayList<>();
            buildTree(list, null, result);



            // 检查并移除 "泥沙" 节点
            removeMudNodeIfRequired(result);
        }
        else if ("3".equals(req.getStationType())){
            List<StatisticalInexDataRespVO> list = statisticalInfoMapper.listStatisticalIndexData(req.getStationType(), req.getStationId());
            buildTree(list, null, result);
        }else{
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        return result;

    }

    @Override
    public StatisticalIndexRespVO getStatisticalIndex(Long stationId, Long statsId) {

        //结果数据
        StatisticalIndexRespVO respVO = new StatisticalIndexRespVO();
        //列表数据
        List<StatisticalIndexRespVO.StatisticalFrom> froms = new ArrayList<>();
        //图表数据
        List<StatisticalIndexRespVO.StatisticalHistogram> histogram = new ArrayList<>();
        //查询站点
        StationRespVO station = stationService.getStation(stationId);
        if (BeanUtil.isEmpty(station)) {
            throw exception(ErrorCodeConstants.EXCEL_IMPORT_STATION_NO_EXISTS_ERROR);
        }
        //查询表单目录
        //第一层  label
        List<StatisticalDirectoryDO> topDict = statisticalDirectoryService.getTopDirectory(station.getStationType());
        List<StatisticalIndexRespVO.Label> labels = topDict.stream()
                .map(item -> {
                    StatisticalIndexRespVO.Label label = new StatisticalIndexRespVO.Label();
                    label.setName(item.getName());
                    label.setStatsId(item.getId());
                    return label;
                })
                .toList();
        //第二层  表单
        List<StatisticalDirectoryDO> secDict = statisticalDirectoryService.getSecDirectory(station.getStationType());

        if (Objects.nonNull(statsId)) {
            //label条件筛选
            secDict = secDict.stream().filter(item -> (item.getIsLabel() && station.getStationType().equals(StationEnum.WEATHER.getType()) && statsId.equals(item.getParentId())) || (!item.getIsLabel() && statsId.equals(item.getParentId()))).collect(Collectors.toList());
        }

        //查询非label的子级  如含沙量的下级
        if (Objects.equals(statsId, 53L)) {
            List<Long> secDictIds = secDict.stream().map(StatisticalDirectoryDO::getId).collect(Collectors.toList());
            List<StatisticalDirectoryDO> childDirectory = statisticalDirectoryService.getChildDirectoryByParentIds(secDictIds);
            if (CollectionUtil.isNotEmpty(childDirectory)) {
                //有子级时 合并
                List<StatisticalDirectoryDO> list = new ArrayList<>(secDict);
                list.addAll(childDirectory);
                secDict = list;
            }
        }

        //查询索引 (条件站点id和表单id)
        LambdaQueryWrapperX<StatisticalInfoDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eqIfPresent(StatisticalInfoDO::getStationId, stationId);
        wrapperX.inIfPresent(StatisticalInfoDO::getStatsId, secDict.stream().map(StatisticalDirectoryDO::getId).toList());
        List<StatisticalInfoDO> statisticalInfoDOS = statisticalInfoMapper.selectList(wrapperX);

        //图表时间段  图表x轴
        Optional<StatisticalInfoDO> min = statisticalInfoDOS.stream().filter(item -> Objects.nonNull(item.getStartYear())).min(Comparator.comparing(StatisticalInfoDO::getStartYear));
        int startYear = 0;
        int endYear = 0;
        if (min.isPresent()) {
            startYear = min.get().getStartYear();
        }
        Optional<StatisticalInfoDO> max = statisticalInfoDOS.stream().filter(item -> Objects.nonNull(item.getEndYear())).max(Comparator.comparing(StatisticalInfoDO::getEndYear));
        if (max.isPresent()) {
            endYear = max.get().getEndYear();
        }
        int[] array = IntStream.range(startYear, endYear + 1).toArray();
        StatisticalIndexRespVO.StatisticalHistogram x = new StatisticalIndexRespVO.StatisticalHistogram();
        x.setName("年");
        x.setNode(array);
        histogram.add(x);
        //组装索引数据和目录数据
        secDict.sort(Comparator.comparing(StatisticalDirectoryDO::getSort));
        for (StatisticalDirectoryDO statisticalDirectoryDO : secDict) {
            //列表
            StatisticalIndexRespVO.StatisticalFrom statisticalFrom = new StatisticalIndexRespVO.StatisticalFrom();
            statisticalFrom.setFromName(statisticalDirectoryDO.getName());
            statisticalFrom.setStationId(station.getId());
            statisticalFrom.setStationName(station.getHydrologicName());
            statisticalFrom.setYears("-");
            statisticalFrom.setDate("-");

            StatisticalInfoDO statisticalInfoDO = null;
            //用statsId匹配
            Optional<StatisticalInfoDO> any = statisticalInfoDOS.stream().filter(item -> statisticalDirectoryDO.getId().equals(item.getStatsId())).findAny();
            if (any.isPresent()) {
                statisticalInfoDO = any.get();
            }
            if (BeanUtil.isNotEmpty(statisticalInfoDO)) {
                statisticalFrom.setYears(Objects.isNull(statisticalInfoDO.getYearCount()) || statisticalInfoDO.getYearCount() == 0 ? "-" : statisticalInfoDO.getYearCount().toString());
                statisticalFrom.setDate((Objects.nonNull(statisticalInfoDO.getStartYear()) ? statisticalInfoDO.getStartYear().toString() : "")
                        + "-"
                        + (Objects.nonNull(statisticalInfoDO.getEndYear()) ? statisticalInfoDO.getEndYear().toString() : ""));
            }

            //图表
            StatisticalIndexRespVO.StatisticalHistogram statisticalHistogram = new StatisticalIndexRespVO.StatisticalHistogram();
            statisticalHistogram.setName(statisticalDirectoryDO.getName());
            int[] ints = new int[array.length];
            Arrays.fill(ints, 0);
            if (BeanUtil.isNotEmpty(statisticalInfoDO) && StringUtils.isNotEmpty(statisticalInfoDO.getYears())) {
                List<String> strings = Arrays.stream(statisticalInfoDO.getYears().split(";")).toList();
                for (String string : strings) {
                    int i = ArrayUtil.indexOf(array, Integer.parseInt(string));
                    if (i >= 0) {
                        ints[i] = 1;
                    }
                }
            }
            statisticalHistogram.setNode(ints);
            histogram.add(statisticalHistogram);
            froms.add(statisticalFrom);
        }

        respVO.setStatisticalHistograms(histogram);
        respVO.setStatisticalFroms(froms);
        respVO.setLabels(labels);
        return respVO;
    }


    @Override
    public PageResult<StatisticalIndexRespVO> getStationPageAndForm(StationPageReqVO pageReqVO) {
        //站点
        if (StringUtils.isEmpty(pageReqVO.getStationIds())) {
            PageResult<StationDO> stationDOPageResult = new PageResult<>();
            //单站点类型情况
            stationDOPageResult = stationMapper.selectPage(pageReqVO);
            //目录
            List<StatisticalDirectoryDO> topDict = statisticalDirectoryService.getTopDirectory(pageReqVO.getStationType());
            //对应站点类型的目录(最顶层的)
            Map<String, List<StatisticalDirectoryDO>> collect1 = topDict.stream().collect(Collectors.groupingBy(StatisticalDirectoryDO::getStationType));

            return getStatisticalIndexRespVOPageResult(stationDOPageResult, collect1);
        } else {
            //全部站点类型情况
            List<Long> stationIds = Arrays.stream(pageReqVO.getStationIds().split(",")).toList().stream().map(Long::valueOf).toList();
            LambdaQueryWrapperX<StationDO> wrapperX = new LambdaQueryWrapperX<>();
            wrapperX.in(StationDO::getId, stationIds);
            //目录
            List<StatisticalDirectoryDO> topDict = statisticalDirectoryService.getTopDirectory(null);
            //对应站点类型的目录
            Map<String, List<StatisticalDirectoryDO>> collect1 = topDict.stream().collect(Collectors.groupingBy(StatisticalDirectoryDO::getStationType));
            PageResult<StatisticalIndexRespVO> statisticalIndexRespVOPageResult = new PageResult<>();
            List<StatisticalIndexRespVO> list = new ArrayList<>();
            for (StationDO stationDO : stationMapper.selectList(wrapperX)) {
                PageResult<StationDO> stationDOPageResult = new PageResult<>();
                stationDOPageResult.setList(List.of(stationDO));
                PageResult<StatisticalIndexRespVO> statisticalIndexRespVOPageResult1 = getStatisticalIndexRespVOPageResult(stationDOPageResult, collect1);
                list.addAll(statisticalIndexRespVOPageResult1.getList());

            }
            statisticalIndexRespVOPageResult.setList(list);
            return statisticalIndexRespVOPageResult;
        }
    }

    private PageResult<StatisticalIndexRespVO> getStatisticalIndexRespVOPageResult(PageResult<StationDO> stationDOPageResult, Map<String, List<StatisticalDirectoryDO>> collect1) {
        //站点id
        List<Long> ids = stationDOPageResult.getList().stream().map(StationDO::getId).toList();

        //索引数据查询条件
        LambdaQueryWrapperX<StatisticalInfoDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.inIfPresent(StatisticalInfoDO::getStationId, ids);

        //索引数据
        List<StatisticalInfoDO> statisticalInfoDOS = statisticalInfoMapper.selectList(wrapperX);
        //站点:索引数据列表
        Map<Long, List<StatisticalInfoDO>> stationInfoList = statisticalInfoDOS.stream().collect(Collectors.groupingBy(StatisticalInfoDO::getStationId));

        //目录数据
        List<StatisticalDirectoryDO> statisticalDirectoryDOS = statisticalDirectoryService.getStatisticalDirectorys();
        //站点类型:目录数据列表
        Map<String, List<StatisticalDirectoryDO>> directoryList = statisticalDirectoryDOS.stream().collect(Collectors.groupingBy(StatisticalDirectoryDO::getStationType));


        //获取地区  省
        List<AreaRespDTO> provincies = areaApi.cascadedProvinceCityArea();
        //市
        List<AreaRespDTO.City> citys = new ArrayList<>();
        provincies.forEach(item -> {
            citys.addAll(item.getChildren());
        });
        //区县
        List<AreaRespDTO.City.Area> countys = new ArrayList<>();
        citys.forEach(item -> {
            countys.addAll(item.getChildren());
        });


        //每个站点组装数据
        List<StatisticalIndexRespVO> list = stationDOPageResult.getList().stream().map(stationDO -> {
            StatisticalIndexRespVO formRespVO = new StatisticalIndexRespVO();
            formRespVO.setId(stationDO.getId());
            formRespVO.setStationType(stationDO.getStationType());
            formRespVO.setHydrologicName(stationDO.getHydrologicName());
            //标签页大类(水温 冰情,降水,泥沙等)
            formRespVO.setLabels(
                    collect1.get(stationDO.getStationType().toString())
                            .stream()
                            .map(item -> {
                                StatisticalIndexRespVO.Label label = new StatisticalIndexRespVO.Label();
                                label.setName(item.getName());
                                label.setStatsId(item.getId());
                                return label;
                            }).toList()
            );
            //当前站点的目录
            List<StatisticalDirectoryDO> statisticalDirectoryDOS1 = directoryList.get(stationDO.getStationType().toString()).stream().filter(item -> {
                return (stationDO.getStationType().equals(StationEnum.WEATHER.getType()) && Objects.isNull(item.getParentId()) || (!item.getIsLabel() && Objects.nonNull(item.getParentId())));
            }).toList();
            //取当前站点的索引数据
            List<StatisticalInfoDO> statisticalInfoDOS1 = stationInfoList.get(stationDO.getId());


            List<StatisticalIndexRespVO.StatisticalFrom> froms = statisticalDirectoryDOS1.stream().map(item -> {
                //表单数据 (表名-年限-起止年份)
                StatisticalIndexRespVO.StatisticalFrom statisticalFrom = new StatisticalIndexRespVO.StatisticalFrom();
                statisticalFrom.setFromName(item.getName());
                statisticalFrom.setStationId(stationDO.getId());
                statisticalFrom.setStationName(stationDO.getHydrologicName());
                statisticalFrom.setYears("-");
                statisticalFrom.setDate("-");
                if (CollectionUtil.isNotEmpty(statisticalInfoDOS1)) {
                    Optional<StatisticalInfoDO> any = statisticalInfoDOS1.stream().filter(items -> items.getStatsId().equals(item.getId())).findAny();
                    if (any.isPresent()) {
                        StatisticalInfoDO statisticalInfoDO = any.get();
                        statisticalFrom.setYears(Objects.isNull(statisticalInfoDO.getYearCount()) || statisticalInfoDO.getYearCount() == 0 ? "-" : statisticalInfoDO.getYearCount().toString());
                        statisticalFrom.setDate((Objects.isNull(statisticalInfoDO.getStartYear()) ? "" : statisticalInfoDO.getStartYear()) + "-" + (Objects.isNull(statisticalInfoDO.getEndYear()) ? "" : statisticalInfoDO.getEndYear()));
                    }
                }
                //处理地区  把编号换成汉字
                String location = "";
                if (StringUtils.isNotEmpty(stationDO.getProvince())) {
                    Optional<AreaRespDTO> provinceOpt = provincies.stream().filter(item1 -> item1.getCode().equals(stationDO.getProvince())).findAny();
                    if (provinceOpt.isPresent()) {
                        location = provinceOpt.get().getName();
                    }
                }
                if (StringUtils.isNotEmpty(stationDO.getCity())) {
                    Optional<AreaRespDTO.City> city = citys.stream().filter(item1 -> item1.getCode().equals(stationDO.getCity())).findAny();
                    if (city.isPresent()) {
                        location = location + "/" + city.get().getName();
                    }
                }
                if (StringUtils.isNotEmpty(stationDO.getCounty())) {
                    Optional<AreaRespDTO.City.Area> county = countys.stream().filter(item1 -> item1.getCode().equals(stationDO.getCounty())).findAny();
                    if (county.isPresent()) {
                        location = location + "/" + county.get().getName();
                    }
                }
                statisticalFrom.setLocation(location);
                return statisticalFrom;
            }).filter(BeanUtil::isNotEmpty).toList();
            formRespVO.setStatisticalFroms(froms);

            return formRespVO;
        }).toList();
        PageResult<StatisticalIndexRespVO> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal(stationDOPageResult.getTotal());
        return pageResult;
    }

    @Override
    public void exportOneTypeStationExcel(HttpServletResponse response, List<StatisticalIndexRespVO> list, Integer stationType) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(StationDataTypeEnumV2.STATISTICAL_INFO.getDescription() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            Integer code = 0;
            if (StationEnum.WEATHER.getType().equals(stationType)) {
                //气象
                code = StationDataTypeEnumV2.STATISTICAL_INFO_WEATHER.getCode();
            } else if (StationEnum.RAIN.getType().equals(stationType)) {
                //雨量
                code = StationDataTypeEnumV2.STATISTICAL_INFO_RAIN.getCode();
            } else {
                //水文
                code = StationDataTypeEnumV2.STATISTICAL_INFO_HYDROLOGIC.getCode();
            }
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + code + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream())
                    .build();

            List<StatisticalIndexRespVO.StatisticalFrom> listFrom = new ArrayList<>();
            list.forEach(item -> {
                if (CollectionUtil.isNotEmpty(item.getStatisticalFroms())) {
                    if (stationType.equals(item.getStationType())) {
                        listFrom.addAll(item.getStatisticalFroms());
                    }
                }
            });
            sortStatisticalFromByLocationAndStationId(listFrom);
            if(CollectionUtil.isEmpty(listFrom)){
                StatisticalIndexRespVO.StatisticalFrom e = new StatisticalIndexRespVO.StatisticalFrom();
                e.setStationId(0L);
                e.setFromName("");
                e.setYears("");
                e.setDate("");
                e.setStationName("");
                e.setLocation("");
                listFrom.add(e);
            }
            if (CollectionUtil.isNotEmpty(listFrom)) {
                excelWriter.fill(listFrom, EasyExcel.writerSheet(0)
                        .registerWriteHandler(new MergeStrategy(listFrom.size(), 0, 1))
                        .build());
            }
            excelWriter.finish();
        } catch (Exception e) {
            log.error("exportExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }

    @Override
    public void exportExcelStation(HttpServletResponse response, List<StatisticalIndexRespVO> list) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(StationDataTypeEnumV2.STATISTICAL_INFO.getDescription() + PlanningDesignConstants.EXCEL_SUFFIX, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            ClassPathResource resource = new ClassPathResource(PlanningDesignConstants.EXCEL_TEMPLATE_PATH + StationDataTypeEnumV2.STATISTICAL_INFO.getCode() + PlanningDesignConstants.EXCEL_SUFFIX);
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(resource.getInputStream())
                    .build();

            //按站点分为三个list
            List<StatisticalIndexRespVO.StatisticalFrom> list1 = new ArrayList<>();
            List<StatisticalIndexRespVO.StatisticalFrom> list2 = new ArrayList<>();
            List<StatisticalIndexRespVO.StatisticalFrom> list3 = new ArrayList<>();
            list.forEach(item -> {
                if (CollectionUtil.isNotEmpty(item.getStatisticalFroms())) {

                    if (StationEnum.WEATHER.getType().equals(item.getStationType())) {
                        list1.addAll(item.getStatisticalFroms());
                    }
                    if (StationEnum.RAIN.getType().equals(item.getStationType())) {
                        list2.addAll(item.getStatisticalFroms());
                    }
                    if (StationEnum.HYDROLOGIC.getType().equals(item.getStationType())) {
                        list3.addAll(item.getStatisticalFroms());
                    }
                }
            });
            sortStatisticalFromByLocationAndStationId(list1);
            sortStatisticalFromByLocationAndStationId(list2);
            sortStatisticalFromByLocationAndStationId(list3);
            if (CollectionUtil.isNotEmpty(list3)) {
                excelWriter.fill(list3, EasyExcel.writerSheet("水文站")
                        .registerWriteHandler(new MergeStrategy(list3.size(), 0, 1))
                        .build());
            }
            if (CollectionUtil.isNotEmpty(list2)) {
                excelWriter.fill(list2, EasyExcel.writerSheet("雨量站")
                        .registerWriteHandler(new MergeStrategy(list2.size(), 0, 1))
                        .build());
            }
            if (CollectionUtil.isNotEmpty(list1)) {
                excelWriter.fill(list1, EasyExcel.writerSheet("气象站")
                        .registerWriteHandler(new MergeStrategy(list1.size(), 0, 1))
                        .build());
            }


            excelWriter.finish();
        } catch (Exception e) {
            log.error("exportExcel--->error", e);
            throw exception(ErrorCodeConstants.EXCEL_EXPORT_ERROR);
        }
    }


    private static void sortStatisticalFromByLocationAndStationId(List<StatisticalIndexRespVO.StatisticalFrom> list1) {
        Collections.sort(list1, new Comparator<StatisticalIndexRespVO.StatisticalFrom>() {
            @Override
            public int compare(StatisticalIndexRespVO.StatisticalFrom p1, StatisticalIndexRespVO.StatisticalFrom p2) {
                int compareTo = p1.getLocation().compareTo(p2.getLocation());
                if (compareTo != 0) {
                    return compareTo;
                }
                return p1.getStationId().compareTo(p2.getStationId());
            }
        });
    }

    @Override
    public List<StatisticalInfoDO> getStatisticaInfosByStationId(Long id) {
        LambdaQueryWrapperX<StatisticalInfoDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eqIfPresent(StatisticalInfoDO::getStationId, id);
        return statisticalInfoMapper.selectList(wrapperX);
    }

    @Override
    public void updateStatisticalInfoYear(Long statsId, Long stationId, Long startYear, Long endYear) {
        LambdaUpdateWrapper<StatisticalInfoDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StatisticalInfoDO::getStatsId, statsId)
                .eq(StatisticalInfoDO::getStationId, stationId)
                .set(StatisticalInfoDO::getStartYear, startYear)
                .set(StatisticalInfoDO::getEndYear, endYear)
                .set(StatisticalInfoDO::getYearCount, startYear + 1 - endYear);
        statisticalInfoMapper.update(wrapper);
    }

    @Override
    public StatisticalInfoDO getStatisticalInfo(Long stationId, Long statisId) {
        return statisticalInfoMapper.selectOneByStationIdAndDataType(stationId, statisId);
    }

    @Override
    public Long createStatisticalInfo(StatisticalInfoDO statisticalInfoDO) {
        statisticalInfoMapper.insert(statisticalInfoDO);
        // 返回
        return statisticalInfoDO.getId();
    }

    @Override
    public void updateStatisticalInfo(StatisticalInfoDO statisticalInfo) {
        Long loginUserId = Objects.isNull(WebFrameworkUtils.getLoginUserId()) ? 1 : WebFrameworkUtils.getLoginUserId();
        statisticalInfo.setUpdater(loginUserId.toString());
        statisticalInfo.setUpdateTime(LocalDateTime.now());
        statisticalInfoMapper.updateById(statisticalInfo);
    }

    @Override
    public List<StatisticalInfoDO> listByParentId(Long parentId, Long stationId) {
        return statisticalInfoMapper.listByParentId(parentId, stationId);
    }


    /**
     * 更新索引数据年份信息
     */
    @Override
    public void updateIndexDataYearInfo(Integer stationType, Integer dataType, Long stationId) {
        StopWatch stopWatch = new StopWatch("更新索引数据年份信息: " + stationId + "_" + dataType);
        stopWatch.start();
        try {
            //年鉴格式
            List<Integer> relationDataTypes = DataTypeRelation.getRelationDataTypes(dataType);
            //年鉴格式
            if (!CollectionUtils.isEmpty(relationDataTypes) && relationDataTypes.get(0).equals(dataType)) {
                updateYearBookRelationAll(stationType, stationId, relationDataTypes);
            }
            //非年鉴格式
            else {
                updateNonYearBook(stationType, dataType, stationId, relationDataTypes);
            }
        } catch (Exception e) {
            log.error("更新索引数据年份信息异常", e);
        }
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    //更新索引数据
    @Override
    public void updateIndexData(Integer stationType, Long stationId) {
        Set<StationDataTypeEnumV2> dataTypes = Arrays.stream(StationDataTypeEnumV2.values()).filter(item -> item.isMonitorData() && Objects.equals(stationType,item.getStationType())).collect(Collectors.toSet());
        for (StationDataTypeEnumV2 dataTypeEnum : dataTypes) {
            //气象资料统计表的索引数据不用处理
            if (dataTypeEnum.equals(StationDataTypeEnumV2.WEATHER_METEOROLOGICAL_DATA_STATISTICS)) {
                continue;
            }
            log.info("更新电站 {} ,统计的表{}的年份数据：开始", stationId, dataTypeEnum.getCode());
            updateIndexDataYearInfo(stationType, dataTypeEnum.getCode(), stationId);
            log.info("更新电站 {} ,统计的表{}的年份数据：结束", stationId, dataTypeEnum.getCode());
        }
    }

    /**
     * @param list     所有数据
     * @param parentId 父id
     * @param result   最终结果
     * @desc 构建统计表目录
     * <AUTHOR>
     * @time 2024/8/15 13:01
     */
    private static void buildTree(List<StatisticalInexDataRespVO> list, Long parentId, List<StatisticalInexDataRespVO> result) {
        for (StatisticalInexDataRespVO node : list) {
            List<StatisticalInexDataRespVO> children = node.getChildren();
            if (Objects.isNull(children)) {
                node.setChildren(new ArrayList<>());
            }
            List<StatisticalInexDataRespVO> labels = node.getLabels();
            if (Objects.isNull(labels)) {
                node.setLabels(new ArrayList<>());
            }
            if (Objects.equals(node.getParentId(), parentId)) {
                result.add(node);
                // 当前节点为组件节点 则子元素放到labels中
                buildTree(list, node.getId(), StringUtils.isNotEmpty(node.getComponentName()) ? node.getLabels() : node.getChildren());
            }
        }
    }

    private void removeMudNodeIfRequired(List<StatisticalInexDataRespVO> result) {
        // 初始化标志变量
        boolean[] existFlags = new boolean[3];
        String[] requiredNodes = {
                HydrologicDataProjectEnum.TRANSPORT_RATE.getDesc(),
                HydrologicDataProjectEnum.SAND_CONTENT.getDesc(),
                HydrologicDataProjectEnum.GRAIN.getDesc()
        };

        // 检查是否存在所需的节点
        for (StatisticalInexDataRespVO node : result) {
            checkNode(node, existFlags);
        }

        // 检查标志变量，如果这些节点都不存在，则移除 "泥沙" 节点
        boolean allNotExist = true;
        for (boolean flag : existFlags) {
            if (flag) {
                allNotExist = false;
                break;
            }
        }

        if (allNotExist) {
            Iterator<StatisticalInexDataRespVO> iterator = result.iterator();
            while (iterator.hasNext()) {
                StatisticalInexDataRespVO node = iterator.next();
                if ("泥沙".equals(node.getName())) {
                    iterator.remove();
                }
            }
        }
    }

    private void checkNode(StatisticalInexDataRespVO node, boolean[] existFlags) {
        String[] requiredNodes = {
                HydrologicDataProjectEnum.TRANSPORT_RATE.getDesc(),
                HydrologicDataProjectEnum.SAND_CONTENT.getDesc(),
                HydrologicDataProjectEnum.GRAIN.getDesc()
        };

        for (int i = 0; i < requiredNodes.length; i++) {
            if (requiredNodes[i].equals(node.getName())) {
                existFlags[i] = true;
            }
        }

        if (node.getChildren() != null) {
            for (StatisticalInexDataRespVO child : node.getChildren()) {
                checkNode(child, existFlags);
            }
        }

        if (node.getLabels() != null) {
            for (StatisticalInexDataRespVO label : node.getLabels()) {
                checkNode(label, existFlags);
            }
        }
    }
    /**
     * @param stationType
     * @param stationId
     * @param relationDataTypes
     * @return void
     * @desc 更新年鉴相关的所有表以及其父级的年份信息
     * <AUTHOR>
     * @time 2024/8/27 16:04
     */
    @Override
    public void updateYearBookRelationAll(Integer stationType, Long stationId, List<Integer> relationDataTypes) {
        List<Integer> listYear = new ArrayList<>();
        for (Integer dataType : relationDataTypes) {
            if (Objects.equals(-1, dataType)) {
                continue;
            }
            StationDataTypeEnumV2 stationDataTypeEnumV2 = StationDataTypeEnumV2.fromCode(dataType);
            DataProcessor dataProcessor = (DataProcessor) SpringUtil.getBean(stationDataTypeEnumV2.getImportModel());
            List<Integer> list = dataProcessor.listYear(stationId, dataType);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            //除过年鉴格式
            if (!relationDataTypes.get(0).equals(dataType)) {
                //处理当前统计表
                updateStatisticalInfo(stationType, stationId, list, dataType);
            }
            listYear.addAll(list);
        }

        //去重
        listYear = new ArrayList<>(new HashSet<>(listYear));
        listYear.removeIf(Objects::isNull);
        //排序
        Collections.sort(listYear);
        //处理年鉴格式年份信息
        Integer dataType = relationDataTypes.get(0);
        updateStatisticalInfo(stationType, stationId, listYear, dataType);
        //它们的parentId是相同的
        //处理父级年份数据
        StatisticalDirectoryDO statisticalDirectory = statisticalDirectoryService.getStatisticalDirectory(dataType);
        Long parentId = statisticalDirectory.getParentId();
        if (Objects.nonNull(parentId)) {
            updateParentInfo(stationType, stationId, parentId);
        }
    }


    /**
     * 已知年份信息、且无年鉴格式
     * 例如：气象资料统计表年份信息更新
     */
    @Override
    public void updateNonYearBook(List<Integer> listYear, Integer stationType, Integer dataType, Long stationId) {
        if (!CollectionUtils.isEmpty(listYear)) {
            //更新该站点统计表的索引数据
            updateStatisticalInfo(stationType, stationId, listYear, dataType);

            //处理该统计表上级年份数据
            StatisticalDirectoryDO statisticalDirectory = statisticalDirectoryService.getStatisticalDirectory(dataType);
            Long parentId = statisticalDirectory.getParentId();
            if (Objects.nonNull(parentId)) {
                updateParentInfo(stationType, stationId, parentId);
            }
        }
    }


    /**
     * 更新非年鉴格式统计表索引数据
     */
    @Override
    public void updateNonYearBook(Integer stationType, Integer dataType, Long stationId, List<Integer> relationDataTypes) {
        //获取对应的service
        StationDataTypeEnumV2 stationDataTypeEnum = StationDataTypeEnumV2.fromCode(dataType);
        DataProcessor dataProcessor = (DataProcessor) SpringUtil.getBean(stationDataTypeEnum.getImportModel());
        List<Integer> listYear = dataProcessor.listYear(stationId, dataType);

        //气象资料表 为手动录入 无需更新
        if (!StationDataTypeEnumV2.WEATHER_METEOROLOGICAL_DATA_STATISTICS.getCode().equals(dataType)) {
            //更新该站点统计表的索引数据
            updateStatisticalInfo(stationType, stationId, listYear, dataType);
        }

        //处理该统计表上级年份数据
        StatisticalDirectoryDO statisticalDirectory = statisticalDirectoryService.getStatisticalDirectory(dataType);
        if (Objects.isNull(statisticalDirectory)) {
            log.warn("此datatype无对应的统计表{}", dataType);
            return;
        }
        Long parentId = statisticalDirectory.getParentId();
        if (Objects.nonNull(parentId)) {
            updateParentInfo(stationType, stationId, parentId);
        }
        //跟年鉴相关
        if (relationDataTypes.get(0) > 0) {
            updateYearBookInfo(stationType, stationId, relationDataTypes);
        }
    }

    /**
     * 更新所有的索引数据
     */
    public void updateAllData() {
        List<StatisticalDirectoryDO> statisticalDirectoryDOS = statisticalDirectoryService.listAll();
        Map<String, List<StatisticalDirectoryDO>> direMap = statisticalDirectoryDOS.stream().collect(Collectors.groupingBy(StatisticalDirectoryDO::getStationType));
        Map<Long, Integer> direCodeMap = statisticalDirectoryDOS.stream().collect(Collectors.toMap(StatisticalDirectoryDO::getId, StatisticalDirectoryDO::getCode));

        //站点类型
        for (StationEnum stationEnum : StationEnum.values()) {
            //该类型站点所有的统计表
            Integer stationType = stationEnum.getType();
            List<StatisticalDirectoryDO> dirList = direMap.get(String.valueOf(stationType));
            List<StatisticalDirectoryDO> labelList = dirList.stream().filter(StatisticalDirectoryDO::getIsLabel).toList();
            List<StationDO> stationDOS = stationService.listByStationType(stationType);

            if (CollectionUtil.isEmpty(stationDOS)) {
                continue;
            }

            for (StationDO stationDO : stationDOS) {
                //获取所有统计表
                for (StatisticalDirectoryDO directoryDO : labelList) {
                    //获取对应的service
                    Integer dataType = directoryDO.getCode();
                    Long stationId = stationDO.getId();

                    updateIndexDataYearInfo(stationType, dataType, stationId);
                }
            }
        }
    }


    @Override
    public List<StatisticalInfoDO> listByStationIds(List<Long> stationIds) {
        return statisticalInfoMapper.listByStationIds(stationIds);
    }

    @Override
    public void insertList(List<StatisticalInfoDO> statisticalInfoDOS) {
        statisticalInfoMapper.insertList(statisticalInfoDOS);
    }

    @Override
    public void updateStationId(Long oldStationId, Long newStationId) {
        stationMapper.updateStationId(oldStationId, newStationId);
    }

    @Override
    public void updateCreator(Integer stationType) {
        statisticalInfoMapper.updateCreator(stationType);
    }

    @Override
    public List<StatisticalInfoDO> listByStationId(Long stationId) {
        return statisticalInfoMapper.listByStationId(stationId);
    }

    @Override
    public void batchInsert(List<StatisticalInfoDO> statisticalInfoDOS) {
        statisticalInfoMapper.insertBatch(statisticalInfoDOS, BizConstants.BATCH_SIZE);
    }

    @Override
    public void updateById(StatisticalInfoDO statisticalInfo) {
        statisticalInfoMapper.updateById(statisticalInfo);
    }

    @Override
    public void insert(StatisticalInfoDO statisticalInfo) {
        statisticalInfoMapper.insert(statisticalInfo);
    }


    /**
     * 更新年鉴格式统计表索引数据
     */
    private void updateYearBookInfo(Integer stationType, Long stationId, List<Integer> relationDataTypes) {
        List<Integer> listYear = new ArrayList<>();

        for (Integer dataType : relationDataTypes) {
            if (dataType < 0) {
                continue;
            }
            StationDataTypeEnumV2 stationDataTypeEnumV2 = StationDataTypeEnumV2.fromCode(dataType);
            //年鉴格式没有对应的物理表
            if (dataType.equals(relationDataTypes.get(0))) {
                continue;
            }
            DataProcessor dataProcessor = (DataProcessor) SpringUtil.getBean(stationDataTypeEnumV2.getImportModel());
            List<Integer> list = dataProcessor.listYear(stationId, dataType);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            listYear.addAll(list);
        }
        if (CollectionUtils.isEmpty(listYear)) {
            updateStatisticalInfo(stationType, stationId, listYear, relationDataTypes.get(0));
            StatisticalDirectoryDO statisticalDirectory = statisticalDirectoryService.getStatisticalDirectory(relationDataTypes.get(0));
            Long parentId = statisticalDirectory.getParentId();
            if (Objects.nonNull(parentId)) {
                updateParentInfo(stationType, stationId, parentId);
            }
            return;
        }
        listYear = listYear.stream().distinct().collect(Collectors.toList());
        Collections.sort(listYear);

        Integer dataType = relationDataTypes.get(0);
        updateStatisticalInfo(stationType, stationId, listYear, dataType);


    }

    private void updateStatisticalInfo(Integer stationType, Long stationId, List<Integer> listYear, Integer dataType) {
        StatisticalDirectoryDO statisticalDirectory = statisticalDirectoryService.getStatisticalDirectory(dataType);
        if (Objects.isNull(statisticalDirectory)) {
            log.warn("此datatype无对应的统计表{}", dataType);
            return;
        }
        StatisticalInfoDO statisticalInfo = this.getStatisticalInfo(stationId, statisticalDirectory.getId());
        StatisticalInfoDO statisticalInfoParent = this.getStatisticalInfo(stationId, statisticalDirectory.getParentId());
        //年为空时
        if (CollectionUtil.isEmpty(listYear)) {
            //无索引数据
            if (Objects.isNull(statisticalInfo)) {
                statisticalInfo = new StatisticalInfoDO();
                statisticalInfo.setStationId(stationId);
                statisticalInfo.setStatsId(statisticalDirectory.getId());
                statisticalInfo.setStartYear(null);
                statisticalInfo.setEndYear(null);
                statisticalInfo.setYearCount(null);
                statisticalInfo.setYears(null);
                statisticalInfo.setStationType(stationType);
                this.createStatisticalInfo(statisticalInfo);

                /* //父级
                statisticalInfoParent = this.getStatisticalInfo(stationId, statisticalDirectory.getParentId());
               if (BeanUtil.isEmpty(statisticalInfoParent)) {
                    StatisticalInfoDO parentStatisticalInfoDO = new StatisticalInfoDO();
                    parentStatisticalInfoDO.setId(null);
                    parentStatisticalInfoDO.setStationId(stationId);
                    parentStatisticalInfoDO.setStartYear(null);
                    parentStatisticalInfoDO.setEndYear(null);
                    parentStatisticalInfoDO.setYearCount(null);
                    parentStatisticalInfoDO.setYears(null);
                    parentStatisticalInfoDO.setStationType(stationType);
                    parentStatisticalInfoDO.setStatsId(statisticalDirectory.getParentId());
                    this.createStatisticalInfo(parentStatisticalInfoDO);
                }*/
            } else {
                //有索引数据时
/*                //如果年份数跟以前相同则不更新
                Integer startYear = statisticalInfo.getStartYear();
                Integer endYear = statisticalInfo.getEndYear();
                String years1 = statisticalInfo.getYears();

                if (Objects.equals(startYear, null) && Objects.equals(endYear, null) && Objects.equals(years1, null)) {
                    log.info("年份数无变化无需更新");
                    return;
                }*/
                statisticalInfo.setStartYear(null);
                statisticalInfo.setEndYear(null);
                statisticalInfo.setYearCount(null);
                statisticalInfo.setYears(null);
                this.updateStatisticalInfo(statisticalInfo);
                //statisticalInfoParent = this.getStatisticalInfo(stationId, statisticalDirectory.getParentId());

            }
        } else {
            //年份有值时
            String years = listYear.stream().map(String::valueOf).collect(Collectors.joining(";"));
            if (Objects.isNull(statisticalInfo)) {
                //无索引数据时
                statisticalInfo = new StatisticalInfoDO();
                statisticalInfo.setStationId(stationId);
                statisticalInfo.setStatsId(statisticalDirectory.getId());
                statisticalInfo.setStartYear(listYear.get(0));
                statisticalInfo.setEndYear(listYear.get(listYear.size() - 1));
                statisticalInfo.setYearCount(listYear.size());
                statisticalInfo.setYears(years);
                statisticalInfo.setStationType(stationType);
                this.createStatisticalInfo(statisticalInfo);
               /* //父级
                 statisticalInfoParent = this.getStatisticalInfo(stationId, statisticalDirectory.getParentId());
                if (BeanUtil.isEmpty(statisticalInfoParent)) {
                    StatisticalInfoDO parentStatisticalInfoDO = new StatisticalInfoDO();
                    parentStatisticalInfoDO.setStationId(stationId);
                    parentStatisticalInfoDO.setStatsId(statisticalDirectory.getParentId());
                    parentStatisticalInfoDO.setStartYear(listYear.get(0));
                    parentStatisticalInfoDO.setEndYear(listYear.get(listYear.size() - 1));
                    parentStatisticalInfoDO.setYearCount(listYear.size());
                    parentStatisticalInfoDO.setYears(years);
                    parentStatisticalInfoDO.setStationType(stationType);
                    this.createStatisticalInfo(parentStatisticalInfoDO);
                }*/
            } else {
/*                //如果年份数跟以前相同则不更新
                Integer startYear = statisticalInfo.getStartYear();
                Integer endYear = statisticalInfo.getEndYear();
                String years1 = statisticalInfo.getYears();

                if (Objects.equals(startYear, listYear.get(0)) && Objects.equals(endYear, listYear.get(listYear.size() - 1)) && Objects.equals(years1, years)) {
                    log.info("年份数无变化无需更新");
                    return;
                }*/
                statisticalInfo.setStartYear(listYear.get(0));
                statisticalInfo.setEndYear(listYear.get(listYear.size() - 1));
                statisticalInfo.setYearCount(!listYear.isEmpty() ? listYear.size() : null);
                statisticalInfo.setYears(years);
                this.updateStatisticalInfo(statisticalInfo);
            }
            if (Objects.isNull(statisticalInfoParent)) {
                statisticalInfoParent = new StatisticalInfoDO();
                statisticalInfoParent.setStationId(stationId);
                statisticalInfoParent.setStatsId(statisticalDirectory.getParentId());
                statisticalInfoParent.setStartYear(listYear.get(0));
                statisticalInfoParent.setEndYear(listYear.get(listYear.size() - 1));
                statisticalInfoParent.setYearCount(listYear.size());
                statisticalInfoParent.setYears(years);
                statisticalInfoParent.setStationType(stationType);
                this.createStatisticalInfo(statisticalInfoParent);
            } else {
                //如果年份数跟以前相同则不更新
                statisticalInfoParent.setStartYear(listYear.get(0));
                statisticalInfoParent.setEndYear(listYear.get(listYear.size() - 1));
                statisticalInfoParent.setYearCount(listYear.size());
                statisticalInfoParent.setYears(years);
                this.updateStatisticalInfo(statisticalInfoParent);
            }

        }
    }

    /**
     * @param stationType 站点类型
     * @param stationId   站点id
     * @param parentId    parentId
     * @desc 更新统计表年份信息
     * <AUTHOR>
     * @time 2024/8/27 15:11
     */
    private void updateParentInfo(Integer stationType, Long stationId, Long parentId) {
        StatisticalInfoDO statisticalInfo;
        if (Objects.nonNull(parentId)) {
            StatisticalDirectoryDO statisticalDirectory = statisticalDirectoryService.getStatisticalDirectory(parentId);
            statisticalInfo = this.getStatisticalInfo(stationId, statisticalDirectory.getId());
            //计算父年份
            StatisticalInfoDO statisticalInfoDO = calcParentYearCount(parentId, stationId);
            if (Objects.isNull(statisticalInfo)) {
                statisticalInfo = new StatisticalInfoDO();
                statisticalInfo.setStationId(stationId);
                statisticalInfo.setStatsId(parentId);

                if (statisticalInfoDO != null) {
                    statisticalInfo.setYearCount(statisticalInfoDO.getYearCount());
                    statisticalInfo.setYears(statisticalInfoDO.getYears());
                    statisticalInfo.setEndYear(statisticalInfoDO.getEndYear());
                    statisticalInfo.setStartYear(statisticalInfoDO.getStartYear());
                } else {
                    statisticalInfo.setYearCount(0);
                    statisticalInfo.setYears(null);
                    statisticalInfo.setEndYear(null);
                    statisticalInfo.setStartYear(null);
                }
                statisticalInfo.setStationType(stationType);
                this.createStatisticalInfo(statisticalInfo);
            } else {
                //查询年份信息的子集、重新计算年份信息
                if (statisticalInfoDO != null) {
                    statisticalInfo.setYearCount(statisticalInfoDO.getYearCount());
                    statisticalInfo.setYears(statisticalInfoDO.getYears());
                    statisticalInfo.setEndYear(statisticalInfoDO.getEndYear());
                    statisticalInfo.setStartYear(statisticalInfoDO.getStartYear());
                } else {
                    statisticalInfo.setYearCount(0);
                    statisticalInfo.setYears(null);
                    statisticalInfo.setEndYear(null);
                    statisticalInfo.setStartYear(null);
                }

                this.updateStatisticalInfo(statisticalInfo);

            }
            if (Objects.nonNull(statisticalDirectory.getParentId())) {
                updateParentInfo(stationType, stationId, statisticalDirectory.getParentId());
            }
        }
    }

    /**
     * 计算上级的yearCount
     */
    private StatisticalInfoDO calcParentYearCount(Long parentId, Long stationId) {
        Map<String, String> map = new HashMap<>();
        List<StatisticalInfoDO> statisticalInfoDOS = this.listByParentId(parentId, stationId);
        if (CollectionUtil.isNotEmpty(statisticalInfoDOS)) {
            Optional<StatisticalInfoDO> yearCountMax = statisticalInfoDOS.stream().filter(item -> Objects.nonNull(item.getYearCount()))
                    .max(Comparator.comparingInt(StatisticalInfoDO::getYearCount));
            if (yearCountMax.isPresent()) {
                return yearCountMax.get();
            }
        }
        return null;
    }
    public List<StatisticalInexDataRespVO> listStatisticalIndexData(@Param("stationType") String stationType, @Param("stationId") Long stationId) {
        // 查询 stationDO 并获取 dataProjectSet
        StationDO stationDO = stationMapper.selectById(stationId);
        // 获取站点的子类型
        String hydrologicType = stationDO.getHydrologicType();
        Set<Integer> dataProjectSet = new HashSet<>();

        if (!stationDO.getDataProject().isEmpty()) {
            String dataProject = stationDO.getDataProject();
            if(dataProject.contains(",")||dataProject.length()==1){
                List<String> dataProjectList = Arrays.stream(dataProject.split(","))
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
                dataProjectSet=new HashSet<>(dataProjectList.stream().map(Integer::parseInt).collect(Collectors.toList()));
//                String[] dataProjects = dataProject.split(",");
//                for (String project : dataProjects) {
//                    int code = Integer.parseInt(project);
//                    dataProjectSet.add(mapDataProject(code));
//                }
            }else{
                dataProjectSet.add(0);
            }
        }else{
            dataProjectSet.add(0);
        }

        // 查询数据
        List<StatisticalInexDataRespVO> list = statisticalInfoMapper.listStatisticalIndexData(stationType, stationId);

        // 过滤节点
        List<StatisticalInexDataRespVO> filteredList = new ArrayList<>();
        for (StatisticalInexDataRespVO node : list) {
            if (shouldIncludeNode(node.getName(), dataProjectSet, stationDO.getHydrologicType())) {
                filteredList.add(node);
            }
        }
        Optional<StatisticalInexDataRespVO> mudNodeOptional = filteredList.stream()
                .filter(node -> "泥沙".equals(node.getName()))
                .findFirst();

        if (mudNodeOptional.isPresent()) {
            StatisticalInexDataRespVO mudNode = mudNodeOptional.get();

            // 收集“输沙率”、“含沙量”和“颗粒级配”节点的 years
            Set<String> yearsSet = new HashSet<>();
            String[] requiredNodes = {
                    HydrologicDataProjectEnum.TRANSPORT_RATE.getDesc(),
                    HydrologicDataProjectEnum.SAND_CONTENT.getDesc(),
                    HydrologicDataProjectEnum.GRAIN.getDesc()
            };
            int maxYearCount = 0;
            for (String requiredNode : requiredNodes) {
                Optional<StatisticalInexDataRespVO> requiredNodeOptional = filteredList.stream()
                        .filter(node -> requiredNode.equals(node.getName()))
                        .findFirst();

                if (requiredNodeOptional.isPresent()) {
                    StatisticalInexDataRespVO requiredNodeData = requiredNodeOptional.get();
                    String years = requiredNodeOptional.get().getYears();
                    if (years != null) {
                        yearsSet.addAll(Arrays.asList(years.split(";")));
                    }
                    int yearCount = requiredNodeData.getYearCount() != null ? requiredNodeData.getYearCount() : 0;
                    maxYearCount = Math.max(maxYearCount, yearCount);
                }
            }

            // 合并 years 并去重
            List<String> sortedYears = new ArrayList<>(yearsSet);
            Collections.sort(sortedYears, Comparator.naturalOrder());

            // 设置 years
            String combinedYears = String.join(";", sortedYears);
            mudNode.setYears(combinedYears);

            // 设置 yearCount
            mudNode.setYearCount(maxYearCount);

            // 设置 startYear 和 endYear
            if (!sortedYears.isEmpty()) {
                mudNode.setStartYear(sortedYears.get(0));
                mudNode.setEndYear(sortedYears.get(sortedYears.size() - 1));
            } else {
                mudNode.setStartYear(null);
                mudNode.setEndYear(null);
            }
        }
        return filteredList;
    }
    public List<StatisticalInexDataRespVO> listRainStatisticalIndexData(@Param("stationType") String stationType, @Param("stationId") Long stationId) {
        // 查询 stationDO 并获取 dataProjectSet
        StationDO stationDO = stationMapper.selectByStationId(stationId);
        // 过滤节点
        List<StatisticalInexDataRespVO> filteredList = new ArrayList<>();
        Set<Integer> dataProjectSet = new HashSet<>();

        if(stationDO!=null)
        {
            if (!stationDO.getDataProject().isEmpty()) {
                String dataProject = stationDO.getDataProject();
                dataProjectSet = Arrays.stream(dataProject.split(","))
                        .map(Integer::parseInt)
                        .map(this::mapDataProject)
                        .collect(Collectors.toSet());
            }
            // 查询数据
            List<StatisticalInexDataRespVO> list = statisticalInfoMapper.listStatisticalIndexData(stationType, stationId);
            for (StatisticalInexDataRespVO node : list) {
                if (shouldIncludeNode(node.getName(), dataProjectSet, stationDO.getHydrologicType())) {
                    filteredList.add(node);
                }
            }
        }
        return filteredList;
    }
    private int mapDataProject(int code) {
        switch (code) {
            case 1:
                return RAINFALL.getCode();
            case 2:
                return EVAPORATION.getCode();
            default:
                return code;
        }
    }
    private boolean shouldIncludeNode(String nodeName, Set<Integer> dataProjectSet,String hydrologicType) {
//        // 如果 dataProjectSet 为空，根据hydrologicType进行过滤
//        if (dataProjectSet.isEmpty()) {
//            if ("水库".equals(hydrologicType)) {
//                return nodeName.contains("水库水文要素摘录表");
//            } else if ("水文".equals(hydrologicType)) {
//                return nodeName.contains("洪水水文要素摘录表") || nodeName.contains("水温");
//            }
//            return true;
//        }
//
//        Integer code = HydrologicDataProjectEnum.getCodeByDesc(nodeName);
//        if (code == null) {
//            return true; // 默认情况下包括所有节点
//        }
//
//        return dataProjectSet.contains(code);
        // 先根据站点类型过滤
        // 如果是水文要素摘录表，需要根据站点类型过滤
        if (nodeName.contains("水文要素摘录表")){
            if ("水库".equals(hydrologicType)) {
                // 使用code精确匹配而不是名称匹配
                return nodeName.equals(StationDataTypeEnumV2.HYDROLOGIC_WATER_HYDROLOGICAL_ELEMENTS_EXCERPT.getDescription());
            } else if ("水文".equals(hydrologicType)) {
                return nodeName.equals(StationDataTypeEnumV2.HYDROLOGIC_FLOOD_HYDROLOGICAL_ELEMENTS_EXCERPT.getDescription());
            }
        }

        // 如果没有配置数据项目，则显示所有表
        if (dataProjectSet.isEmpty()) {
            return true;
        }


        // 检查数据项目权限
        Integer code = HydrologicDataProjectEnum.getCodeByDesc(nodeName);
        if (code == null) {
            return true;
        }
        return dataProjectSet.contains(code);
    }
}