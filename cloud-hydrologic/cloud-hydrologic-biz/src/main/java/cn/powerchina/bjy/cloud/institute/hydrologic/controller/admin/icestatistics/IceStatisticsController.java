package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.ExcelImportCheck;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.enums.StationEnum;
import cn.powerchina.bjy.cloud.institute.hydrologic.annotation.HydrologicOperation;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics.vo.IceStatisticsPageReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics.vo.IceStatisticsRespVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.icestatistics.vo.IceStatisticsSaveReqVO;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.icestatistics.IceStatisticsService;
import cn.powerchina.bjy.cloud.institute.hydrologic.service.statisticalinfo.StatisticalInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 冰情统计")
@RestController
@RequestMapping("/plan/hydrologic/ice/statistics")
@Validated
public class IceStatisticsController {

    @Resource
    private IceStatisticsService iceStatisticsService;
    @Autowired
    private StatisticalInfoService statisticalInfoService;

    @PutMapping("/update")
    @Operation(summary = "更新冰情统计")
//    @PreAuthorize("@ss.hasPermission('hydrologic:ice-statistics:update')")
    public CommonResult<Boolean> updateIceStatistics(@Valid @RequestBody IceStatisticsSaveReqVO updateReqVO) {
        if(CollectionUtils.isEmpty(updateReqVO.getDeleteIds())){
            iceStatisticsService.updateIceStatistics(updateReqVO);
        }else{
            iceStatisticsService.deleteIceStatisticsData(updateReqVO);
        }
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得冰情统计分页")
//    @PreAuthorize("@ss.hasPermission('hydrologic:ice-statistics:query')")
    public CommonResult<PageResult<IceStatisticsRespVO>> getIceStatisticsPage(@Valid IceStatisticsPageReqVO pageReqVO) {
        PageResult<IceStatisticsRespVO> pageResult = iceStatisticsService.getIceStatisticsPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page/search")
    @Operation(summary = "数据检索-获得冰情统计分页")
    @HydrologicOperation
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-day:query-data')")
    public CommonResult<PageResult<IceStatisticsRespVO>> getIceStatisticsDataPage(@Valid IceStatisticsPageReqVO pageReqVO) {
        pageReqVO.setLogId(null);
        PageResult<IceStatisticsRespVO> pageResult = iceStatisticsService.getIceStatisticsPage(pageReqVO);
        return success(pageResult);
    }



    @GetMapping("/export")
    @Operation(summary = "（记录）导出冰情统计 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportIceStatisticsExcel(@Valid IceStatisticsPageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        iceStatisticsService.exportExcel(response, pageReqVO);
    }

    @GetMapping("/export/data")
    @Operation(summary = "（检索）导出冰情统计 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:weather-temperature:export-data')")
    public void exportIceStatisticsDataExcel(@Valid IceStatisticsPageReqVO pageReqVO,
                                             HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        iceStatisticsService.exportExcel(response, pageReqVO);
    }

    @PostMapping("/import/excel")
	@ExcelImportCheck
    @Operation(summary = "导入冰情统计 Excel")
//    @PreAuthorize("@ss.hasPermission('hydrologic:ice-statistics:import')")
    @Parameter(name = "file", description = "excel文件", required = true)
    @Parameter(name = "stationId", description = "水文站id", required = true)
//    @OperateLog(type = EXPORT)
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                             @RequestParam("stationId") Long stationId,
                                             @RequestParam("stationType") Integer stationType,
                                             @RequestParam("dataType") Integer  dataType
    ) throws IOException {
        iceStatisticsService.importData(file, stationId,stationType,dataType);
        return success(true);
    }


}