package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.waterlevelday.vo;

import cn.powerchina.bjy.cloud.institute.hydrologic.model.YearForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 输沙率年鉴数据修改
 *
 * <AUTHOR>
 **/
@Data
public class WaterYearBookUpdateReqVO {
    @Schema(description = "站点id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11254")
    @NotNull(message = "站点id不能为空")
    private Long stationId;

    @Schema(description = "数据")
    private List<YearForm> dataMode;
}
