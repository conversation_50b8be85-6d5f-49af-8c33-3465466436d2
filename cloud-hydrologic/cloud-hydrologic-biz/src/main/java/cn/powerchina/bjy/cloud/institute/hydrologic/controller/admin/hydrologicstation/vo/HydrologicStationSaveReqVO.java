package cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.hydrologicstation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "管理后台 - 水文站新增/修改 Request VO")
@Data
public class HydrologicStationSaveReqVO {

    @Schema(description = "主键", example = "23021")
    private Long id;

    @Schema(description = "测站编码")
    private String hydrologicCode;

    @Schema(description = "水系", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "水系不能为空")
    private String riverSystem;

    @Schema(description = "河名", example = "芋艿", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "河名不能为空")
    private String riverName;

    @Schema(description = "站名", example = "王五", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "站名不能为空")
    private String hydrologicName;

    @Schema(description = "站别", example = "水文或水位")
    @NotBlank(message = "站别不能为空")
    private String hydrologicType;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "省不能为空")
    private String province;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "市不能为空")
    private String city;

    @Schema(description = "县")
    private String county;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "经度不能为空")
    private String longitude;

    @Schema(description = "纬度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "纬度不能为空")
    private String latitude;

    @Schema(description = "集水面积", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "集水面积不能为空")
    private String catchmentArea;

    @Schema(description = "距河口距离")
    private String distanceFromTheRiverMouth;

    @Schema(description = "年份", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "年份不能为空")
    private String year;

    @Schema(description = "月份")
    private String month;

    @Schema(description = "资料项目,1:水位,2:流量,3:含沙量,4:颗粒级配,5:冰情、水温,6:降水,7:蒸发")
    private String dataProject;

    @Schema(description = "领导机关")
    private String leadershipOrganization;

    @Schema(description = "历史沿革")
    private String historicalEvolution;

    @Schema(description = "控制条件")
    private String controlConditions;

    @Schema(description = "年限")
    private Integer dataAge;

    @Schema(description = "开始年份")
    private String dataStartYear;

    @Schema(description = "结束年份")
    private String dataEndYear;

}