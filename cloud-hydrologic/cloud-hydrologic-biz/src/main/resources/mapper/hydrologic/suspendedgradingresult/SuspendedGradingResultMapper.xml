<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.suspendedgradingresult.SuspendedGradingResultMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <!-- 更新历史版本 -->
    <update id="updateHistoryVersion">
        UPDATE hydrologic_suspended_grading_result
        SET latest = 0
        WHERE station_id = #{stationId}
          AND station_type = #{stationType}
          AND data_type = #{dataType}
          AND latest = 1
          AND deleted = 0
    </update>

    <!-- 批量插入数据 -->
    <insert id="batchInsert">
        INSERT INTO hydrologic_suspended_grading_result (
        station_id,
        station_type,
        data_type,
        year,
        month,
        value0005,
        value0007,
        value001,
        value0025,
        value005,
        value01,
        value025,
        value05,
        value10,
        value20,
        value30,
        median_size,
        mean_size,
        max_size,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.stationId},
            #{item.stationType},
            #{item.dataType},
            #{item.year},
            #{item.month},
            #{item.value0005},
            #{item.value0007},
            #{item.value001},
            #{item.value0025},
            #{item.value005},
            #{item.value01},
            #{item.value025},
            #{item.value05},
            #{item.value10},
            #{item.value20},
            #{item.value30},
            #{item.medianSize},
            #{item.meanSize},
            #{item.maxSize},
            #{item.version},
            #{item.latest},
            now(),
            now(),
            #{item.creator},
            #{item.updater},
            0
            )
        </foreach>
    </insert>
</mapper>