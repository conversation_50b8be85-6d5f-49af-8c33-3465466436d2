<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationresult.StationResultMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        `day`,
        hours_minute,
        grain005,
        grain007,
        grain01,
        grain025,
        grain05,
        grain1,
        grain25,
        grain5,
        grain10,
        grain20,
        grain30,
        grain_mid,
        grain_max,
        sample_sand_content,
        water_temperature,
        sampling_method,
        analysis_method,
        grain_average,
        average_sink_speed,
        start_hours_minute,
        end_hours_minute,
        section_position,
        testing_methods,
        basic_water_level,
        flow,
        sectional_area,
        average_velocity_flow,
        max_velocity_flow,
        water_surface_width,
        max_water_depth,
        agerage_water_depth,
        water_surface_gradient,
        roughness,
        section_transport_rate,
        section_average_value,
        section_sample_value,
        testing_methods_average_value,
        testing_methods_sample_value,
        remark,
        version,
        latest,
        current_day,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationresult.StationResultDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="hours_minute" property="hoursMinute"/>
        <result column="grain005" property="grain005"/>
        <result column="grain007" property="grain007"/>
        <result column="grain01" property="grain01"/>
        <result column="grain025" property="grain025"/>
        <result column="grain05" property="grain05"/>
        <result column="grain1" property="grain1"/>
        <result column="grain25" property="grain25"/>
        <result column="grain5" property="grain5"/>
        <result column="grain10" property="grain10"/>
        <result column="grain20" property="grain20"/>
        <result column="grain30" property="grain30"/>
        <result column="grain_mid" property="grainMid"/>
        <result column="grain_max" property="grainMax"/>
        <result column="sample_sand_content" property="sampleSandContent"/>
        <result column="water_temperature" property="waterTemperature"/>
        <result column="sampling_method" property="samplingMethod"/>
        <result column="analysis_method" property="analysisMethod"/>
        <result column="grain_average" property="grainAverage"/>
        <result column="average_sink_speed" property="averageSinkSpeed"/>
        <result column="start_hours_minute" property="startHoursMinute"/>
        <result column="end_hours_minute" property="endHoursMinute"/>
        <result column="section_position" property="sectionPosition"/>
        <result column="testing_methods" property="testingMethods"/>
        <result column="basic_water_level" property="basicWaterLevel"/>
        <result column="flow" property="flow"/>
        <result column="sectional_area" property="sectionalArea"/>
        <result column="average_velocity_flow" property="averageVelocityFlow"/>
        <result column="max_velocity_flow" property="maxVelocityFlow"/>
        <result column="water_surface_width" property="waterSurfaceWidth"/>
        <result column="max_water_depth" property="maxWaterDepth"/>
        <result column="agerage_water_depth" property="agerageWaterDepth"/>
        <result column="water_surface_gradient" property="waterSurfaceGradient"/>
        <result column="roughness" property="roughness"/>
        <result column="section_transport_rate" property="sectionTransportRate"/>
        <result column="section_average_value" property="sectionAverageValue"/>
        <result column="section_sample_value" property="sectionSampleValue"/>
        <result column="testing_methods_average_value" property="testingMethodsAverageValue"/>
        <result column="testing_methods_sample_value" property="testingMethodsSampleValue"/>
        <result column="remark" property="remark"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="current_day" property="currentDay"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <update id="updateHistoryVersion">
        update hydrologic_station_result
        set latest = 0
        where station_id = #{stationId}
          and station_type = #{stationType}
          and data_type = #{dataType}
          and latest != 0
    </update>

    <select id="listYear">
        select year
        from hydrologic_station_result
        where deleted = 0
          and station_id = #{stationId}
          and data_type = #{dataType}
        and latest = 1
        group by year
        order by year asc
    </select>

    <select id="selectMaxStationResultVersion">
        select max(version)
        from hydrologic_station_result
        where deleted = 0
          and station_id = #{stationId}
          and data_type = #{dataType}
          and station_type = #{stationType}
    </select>



    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationresult.StationResultDO">
        select * from hydrologic_station_result
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0 and latest=1
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_station_result(
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        `day`,
        hours_minute,
        grain005,
        grain007,
        grain01,
        grain025,
        grain05,
        grain1,
        grain25,
        grain5,
        grain10,
        grain20,
        grain30,
        grain_mid,
        grain_max,
        sample_sand_content,
        water_temperature,
        sampling_method,
        analysis_method,
        grain_average,
        average_sink_speed,
        start_hours_minute,
        end_hours_minute,
        section_position,
        testing_methods,
        basic_water_level,
        flow,
        sectional_area,
        average_velocity_flow,
        max_velocity_flow,
        water_surface_width,
        max_water_depth,
        agerage_water_depth,
        water_surface_gradient,
        roughness,
        section_transport_rate,
        section_average_value,
        section_sample_value,
        testing_methods_average_value,
        testing_methods_sample_value,
        remark,
        version,
        latest,
        current_day,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.month},
            #{element.day},
            #{element.hoursMinute},
            #{element.grain005},
            #{element.grain007},
            #{element.grain01},
            #{element.grain025},
            #{element.grain05},
            #{element.grain1},
            #{element.grain25},
            #{element.grain5},
            #{element.grain10},
            #{element.grain20},
            #{element.grain30},
            #{element.grainMid},
            #{element.grainMax},
            #{element.sampleSandContent},
            #{element.waterTemperature},
            #{element.samplingMethod},
            #{element.analysisMethod},
            #{element.grainAverage},
            #{element.averageSinkSpeed},
            #{element.startHoursMinute},
            #{element.endHoursMinute},
            #{element.sectionPosition},
            #{element.testingMethods},
            #{element.basicWaterLevel},
            #{element.flow},
            #{element.sectionalArea},
            #{element.averageVelocityFlow},
            #{element.maxVelocityFlow},
            #{element.waterSurfaceWidth},
            #{element.maxWaterDepth},
            #{element.agerageWaterDepth},
            #{element.waterSurfaceGradient},
            #{element.roughness},
            #{element.sectionTransportRate},
            #{element.sectionAverageValue},
            #{element.sectionSampleValue},
            #{element.testingMethodsAverageValue},
            #{element.testingMethodsSampleValue},
            #{element.remark},
            #{element.version},
            #{element.latest},
            #{element.currentDay},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_station_result where id &lt; 1 and station_id=#{stationId} and creator='1';
    </select>

    <select id="getLatestVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_station_result
        where deleted = 0
        and latest = 1
        and station_id = #{stationId}
        and data_type = #{dataType}
    </select>

    <insert id="batchInsert">
        INSERT INTO hydrologic_station_result(
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        `day`,
        hours_minute,
        grain005,
        grain007,
        grain01,
        grain025,
        grain05,
        grain1,
        grain25,
        grain5,
        grain10,
        grain20,
        grain30,
        grain_mid,
        grain_max,
        sample_sand_content,
        water_temperature,
        sampling_method,
        analysis_method,
        grain_average,
        average_sink_speed,
        start_hours_minute,
        end_hours_minute,
        section_position,
        testing_methods,
        basic_water_level,
        flow,
        sectional_area,
        average_velocity_flow,
        max_velocity_flow,
        water_surface_width,
        max_water_depth,
        agerage_water_depth,
        water_surface_gradient,
        roughness,
        section_transport_rate,
        section_average_value,
        section_sample_value,
        testing_methods_average_value,
        testing_methods_sample_value,
        remark,
        version,
        latest,
        current_day,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.month},
            #{element.day},
            #{element.hoursMinute},
            #{element.grain005},
            #{element.grain007},
            #{element.grain01},
            #{element.grain025},
            #{element.grain05},
            #{element.grain1},
            #{element.grain25},
            #{element.grain5},
            #{element.grain10},
            #{element.grain20},
            #{element.grain30},
            #{element.grainMid},
            #{element.grainMax},
            #{element.sampleSandContent},
            #{element.waterTemperature},
            #{element.samplingMethod},
            #{element.analysisMethod},
            #{element.grainAverage},
            #{element.averageSinkSpeed},
            #{element.startHoursMinute},
            #{element.endHoursMinute},
            #{element.sectionPosition},
            #{element.testingMethods},
            #{element.basicWaterLevel},
            #{element.flow},
            #{element.sectionalArea},
            #{element.averageVelocityFlow},
            #{element.maxVelocityFlow},
            #{element.waterSurfaceWidth},
            #{element.maxWaterDepth},
            #{element.agerageWaterDepth},
            #{element.waterSurfaceGradient},
            #{element.roughness},
            #{element.sectionTransportRate},
            #{element.sectionAverageValue},
            #{element.sectionSampleValue},
            #{element.testingMethodsAverageValue},
            #{element.testingMethodsSampleValue},
            #{element.remark},
            #{element.version},
            #{element.latest},
            #{element.currentDay},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0
            )
        </foreach>
    </insert>


    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
        update hydrologic_station_result set

            hours_minute = #{updated.hoursMinute},
            grain005 = #{updated.grain005},
            grain007 = #{updated.grain007},
            grain01 = #{updated.grain01},
            grain025 = #{updated.grain025},
            grain05 = #{updated.grain05},
            grain1 = #{updated.grain1},
            grain25 = #{updated.grain25},
            grain5 = #{updated.grain5},
            grain10 = #{updated.grain10},
            grain20 = #{updated.grain20},
            grain30 = #{updated.grain30},
            grain_mid = #{updated.grainMid},
            grain_max = #{updated.grainMax},
            sample_sand_content = #{updated.sampleSandContent},
            water_temperature = #{updated.waterTemperature},
            sampling_method = #{updated.samplingMethod},
            analysis_method = #{updated.analysisMethod},
            grain_average = #{updated.grainAverage},
            average_sink_speed = #{updated.averageSinkSpeed},
            start_hours_minute = #{updated.startHoursMinute},
            end_hours_minute = #{updated.endHoursMinute},
            section_position = #{updated.sectionPosition},
            testing_methods = #{updated.testingMethods},
            basic_water_level = #{updated.basicWaterLevel},
            flow = #{updated.flow},
            sectional_area = #{updated.sectionalArea},
            average_velocity_flow = #{updated.averageVelocityFlow},
            max_velocity_flow = #{updated.maxVelocityFlow},
            water_surface_width = #{updated.waterSurfaceWidth},
            max_water_depth = #{updated.maxWaterDepth},
            agerage_water_depth = #{updated.agerageWaterDepth},
            water_surface_gradient = #{updated.waterSurfaceGradient},
            roughness = #{updated.roughness},
            section_transport_rate = #{updated.sectionTransportRate},
            section_average_value = #{updated.sectionAverageValue},
            section_sample_value = #{updated.sectionSampleValue},
            testing_methods_average_value = #{updated.testingMethodsAverageValue},
            testing_methods_sample_value = #{updated.testingMethodsSampleValue},
            remark = #{updated.remark},
            version = #{updated.version},
            latest = #{updated.latest},
            current_day = #{updated.currentDay},
            update_time = #{updated.updateTime},
            updater = #{updated.updater}

        where id=#{updated.id}
        </foreach>
    </update>
</mapper>