<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationyear.StationYearMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        average_flow,
        runoff_rate,
        runoff_depth,
        runoff_modulus,
        max_value,
        max_value_date,
        min_value,
        min_value_date,
        max24h_value,
        max24h_value_date,
        max1d_value,
        max1d_value_date,
        max3d_value,
        max3d_value_date,
        max7d_value,
        max7d_value_date,
        max15d_value,
        max15d_value_date,
        max30d_value,
        max30d_value_date,
        max60d_value,
        max60d_value_date,
        grain005,
        grain007,
        grain01,
        grain025,
        grain05,
        grain1,
        grain25,
        grain5,
        grain10,
        grain20,
        grain30,
        grain_mid,
        grain_average,
        grain_max,
        average_value,
        first_ice_date,
        end_ice_date,
        max_section_average,
        max_section_average_date,
        min_section_average,
        min_section_average_date,
        average_sand_content,
        average_discharge_rate,
        total_value,
        discharge_modulus,
        `value`,
        value_days,
        remark,
        average_value_remark,
        max_value_remark,
        min_value_remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationyear.StationYearDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="average_flow" property="averageFlow"/>
        <result column="runoff_rate" property="runoffRate"/>
        <result column="runoff_depth" property="runoffDepth"/>
        <result column="runoff_modulus" property="runoffModulus"/>
        <result column="max_value" property="maxValue"/>
        <result column="max_value_date" property="maxValueDate"/>
        <result column="min_value" property="minValue"/>
        <result column="min_value_date" property="minValueDate"/>
        <result column="max24h_value" property="max24hValue"/>
        <result column="max24h_value_date" property="max24hValueDate"/>
        <result column="max1d_value" property="max1dValue"/>
        <result column="max1d_value_date" property="max1dValueDate"/>
        <result column="max3d_value" property="max3dValue"/>
        <result column="max3d_value_date" property="max3dValueDate"/>
        <result column="max7d_value" property="max7dValue"/>
        <result column="max7d_value_date" property="max7dValueDate"/>
        <result column="max15d_value" property="max15dValue"/>
        <result column="max15d_value_date" property="max15dValueDate"/>
        <result column="max30d_value" property="max30dValue"/>
        <result column="max30d_value_date" property="max30dValueDate"/>
        <result column="max60d_value" property="max60dValue"/>
        <result column="max60d_value_date" property="max60dValueDate"/>
        <result column="grain005" property="grain005"/>
        <result column="grain007" property="grain007"/>
        <result column="grain01" property="grain01"/>
        <result column="grain025" property="grain025"/>
        <result column="grain05" property="grain05"/>
        <result column="grain1" property="grain1"/>
        <result column="grain25" property="grain25"/>
        <result column="grain5" property="grain5"/>
        <result column="grain10" property="grain10"/>
        <result column="grain20" property="grain20"/>
        <result column="grain30" property="grain30"/>
        <result column="grain_mid" property="grainMid"/>
        <result column="grain_average" property="grainAverage"/>
        <result column="grain_max" property="grainMax"/>
        <result column="average_value" property="averageValue"/>
        <result column="first_ice_date" property="firstIceDate"/>
        <result column="end_ice_date" property="endIceDate"/>
        <result column="max_section_average" property="maxSectionAverage"/>
        <result column="max_section_average_date" property="maxSectionAverageDate"/>
        <result column="min_section_average" property="minSectionAverage"/>
        <result column="min_section_average_date" property="minSectionAverageDate"/>
        <result column="average_sand_content" property="averageSandContent"/>
        <result column="average_discharge_rate" property="averageDischargeRate"/>
        <result column="total_value" property="totalValue"/>
        <result column="discharge_modulus" property="dischargeModulus"/>
        <result column="value" property="value"/>
        <result column="value_days" property="valueDays"/>
        <result column="remark" property="remark"/>
        <result column="average_value_remark" property="averageValueRemark"/>
        <result column="max_value_remark" property="maxValueRemark"/>
        <result column="min_value_remark" property="minValueRemark"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <update id="updateHistoryVersion">
        update hydrologic_station_year
        set latest = 0
        where station_id = #{stationId}
          and station_type = #{stationType}
          and data_type = #{dataType}
          and latest != 0
    </update>

    <select id="selectMaxVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_station_year
        where deleted = 0
          and station_id = #{stationId}
          and station_type = #{stationType}
          and data_type = #{dataType}
    </select>

    <select id="listYear">
        select year
        from hydrologic_station_year
        where deleted = 0
          and station_id = #{stationId}
          and data_type = #{dataType}
        and latest = 1
        group by year
        order by year asc
    </select>
    <select id="getYearLatestVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_station_year
        where deleted = 0
          and latest = 1
          and station_id = #{stationId}
          and data_type = #{dataType}
    </select>
    <select id="getLatestVersion">
        SELECT version
        FROM hydrologic_station_year
        WHERE station_id = #{stationId}
          AND data_type = #{dataType}
          AND latest = 1
          AND deleted = 0
        limit 1
    </select>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationyear.StationYearDO">
        select * from hydrologic_station_year
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0 and latest=1
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_station_year(
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        average_flow,
        runoff_rate,
        runoff_depth,
        runoff_modulus,
        max_value,
        max_value_date,
        min_value,
        min_value_date,
        max24h_value,
        max24h_value_date,
        max1d_value,
        max1d_value_date,
        max3d_value,
        max3d_value_date,
        max7d_value,
        max7d_value_date,
        max15d_value,
        max15d_value_date,
        max30d_value,
        max30d_value_date,
        max60d_value,
        max60d_value_date,
        grain005,
        grain007,
        grain01,
        grain025,
        grain05,
        grain1,
        grain25,
        grain5,
        grain10,
        grain20,
        grain30,
        grain_mid,
        grain_average,
        grain_max,
        average_value,
        first_ice_date,
        end_ice_date,
        max_section_average,
        max_section_average_date,
        min_section_average,
        min_section_average_date,
        average_sand_content,
        average_discharge_rate,
        total_value,
        discharge_modulus,
        `value`,
        value_days,
        remark,
        average_value_remark,
        max_value_remark,
        min_value_remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.averageFlow},
            #{element.runoffRate},
            #{element.runoffDepth},
            #{element.runoffModulus},
            #{element.maxValue},
            #{element.maxValueDate},
            #{element.minValue},
            #{element.minValueDate},
            #{element.max24hValue},
            #{element.max24hValueDate},
            #{element.max1dValue},
            #{element.max1dValueDate},
            #{element.max3dValue},
            #{element.max3dValueDate},
            #{element.max7dValue},
            #{element.max7dValueDate},
            #{element.max15dValue},
            #{element.max15dValueDate},
            #{element.max30dValue},
            #{element.max30dValueDate},
            #{element.max60dValue},
            #{element.max60dValueDate},
            #{element.grain005},
            #{element.grain007},
            #{element.grain01},
            #{element.grain025},
            #{element.grain05},
            #{element.grain1},
            #{element.grain25},
            #{element.grain5},
            #{element.grain10},
            #{element.grain20},
            #{element.grain30},
            #{element.grainMid},
            #{element.grainAverage},
            #{element.grainMax},
            #{element.averageValue},
            #{element.firstIceDate},
            #{element.endIceDate},
            #{element.maxSectionAverage},
            #{element.maxSectionAverageDate},
            #{element.minSectionAverage},
            #{element.minSectionAverageDate},
            #{element.averageSandContent},
            #{element.averageDischargeRate},
            #{element.totalValue},
            #{element.dischargeModulus},
            #{element.value},
            #{element.valueDays},
            #{element.remark},
            #{element.averageValueRemark},
            #{element.maxValueRemark},
            #{element.minValueRemark},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_station_year where id &lt; 1 and station_id=#{stationId} and creator='1';
    </select>
    <select id="getStationIdBySeriesLength" resultType="java.lang.String">
        select s.station_id from (select t.station_id,count(t.station_id) seriesLength from (SELECT x.station_id,x.`year` FROM hydrologic_station_year x GROUP by x.station_id,x.`year`) t  GROUP by t.station_id) s where s.seriesLength &lt;= #{seriesLength}
    </select>
    <select id="getYearCount" resultType="java.lang.String">
        select t.station_id from
            (SELECT x.station_id,x.year_count
             FROM hydrologic_statistical_info x GROUP by x.station_id,x.`year_count`) t
        where t.year_count >= #{seriesLength}
    </select>
    <insert id="batchInsert">
        INSERT INTO hydrologic_station_year(
        station_id,
        station_type,
        data_type,
        `year`,
        average_flow,
        runoff_rate,
        runoff_depth,
        runoff_modulus,
        max_value,
        max_value_date,
        min_value,
        min_value_date,
        max24h_value,
        max24h_value_date,
        max1d_value,
        max1d_value_date,
        max3d_value,
        max3d_value_date,
        max7d_value,
        max7d_value_date,
        max15d_value,
        max15d_value_date,
        max30d_value,
        max30d_value_date,
        max60d_value,
        max60d_value_date,
        grain005,
        grain007,
        grain01,
        grain025,
        grain05,
        grain1,
        grain25,
        grain5,
        grain10,
        grain20,
        grain30,
        grain_mid,
        grain_average,
        grain_max,
        average_value,
        first_ice_date,
        end_ice_date,
        max_section_average,
        max_section_average_date,
        min_section_average,
        min_section_average_date,
        average_sand_content,
        average_discharge_rate,
        total_value,
        discharge_modulus,
        `value`,
        value_days,
        remark,
        average_value_remark,
        max_value_remark,
        min_value_remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.averageFlow},
            #{element.runoffRate},
            #{element.runoffDepth},
            #{element.runoffModulus},
            #{element.maxValue},
            #{element.maxValueDate},
            #{element.minValue},
            #{element.minValueDate},
            #{element.max24hValue},
            #{element.max24hValueDate},
            #{element.max1dValue},
            #{element.max1dValueDate},
            #{element.max3dValue},
            #{element.max3dValueDate},
            #{element.max7dValue},
            #{element.max7dValueDate},
            #{element.max15dValue},
            #{element.max15dValueDate},
            #{element.max30dValue},
            #{element.max30dValueDate},
            #{element.max60dValue},
            #{element.max60dValueDate},
            #{element.grain005},
            #{element.grain007},
            #{element.grain01},
            #{element.grain025},
            #{element.grain05},
            #{element.grain1},
            #{element.grain25},
            #{element.grain5},
            #{element.grain10},
            #{element.grain20},
            #{element.grain30},
            #{element.grainMid},
            #{element.grainAverage},
            #{element.grainMax},
            #{element.averageValue},
            #{element.firstIceDate},
            #{element.endIceDate},
            #{element.maxSectionAverage},
            #{element.maxSectionAverageDate},
            #{element.minSectionAverage},
            #{element.minSectionAverageDate},
            #{element.averageSandContent},
            #{element.averageDischargeRate},
            #{element.totalValue},
            #{element.dischargeModulus},
            #{element.value},
            #{element.valueDays},
            #{element.remark},
            #{element.averageValueRemark},
            #{element.maxValueRemark},
            #{element.minValueRemark},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0
            )
        </foreach>
    </insert>


    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
            update hydrologic_station_year set

            average_flow = #{updated.averageFlow},
            runoff_rate = #{updated.runoffRate},
            runoff_depth = #{updated.runoffDepth},
            runoff_modulus = #{updated.runoffModulus},
            max_value = #{updated.maxValue},
            max_value_date = #{updated.maxValueDate},
            min_value = #{updated.minValue},
            min_value_date = #{updated.minValueDate},
            max24h_value = #{updated.max24hValue},
            max24h_value_date = #{updated.max24hValueDate},
            max1d_value = #{updated.max1dValue},
            max1d_value_date = #{updated.max1dValueDate},
            max3d_value = #{updated.max3dValue},
            max3d_value_date = #{updated.max3dValueDate},
            max7d_value = #{updated.max7dValue},
            max7d_value_date = #{updated.max7dValueDate},
            max15d_value = #{updated.max15dValue},
            max15d_value_date = #{updated.max15dValueDate},
            max30d_value = #{updated.max30dValue},
            max30d_value_date = #{updated.max30dValueDate},
            max60d_value = #{updated.max60dValue},
            max60d_value_date = #{updated.max60dValueDate},
            grain005 = #{updated.grain005},
            grain007 = #{updated.grain007},
            grain01 = #{updated.grain01},
            grain025 = #{updated.grain025},
            grain05 = #{updated.grain05},
            grain1 = #{updated.grain1},
            grain25 = #{updated.grain25},
            grain5 = #{updated.grain5},
            grain10 = #{updated.grain10},
            grain20 = #{updated.grain20},
            grain30 = #{updated.grain30},
            grain_mid = #{updated.grainMid},
            grain_average = #{updated.grainAverage},
            grain_max = #{updated.grainMax},
            average_value = #{updated.averageValue},
            first_ice_date = #{updated.firstIceDate},
            end_ice_date = #{updated.endIceDate},
            max_section_average = #{updated.maxSectionAverage},
            max_section_average_date = #{updated.maxSectionAverageDate},
            min_section_average = #{updated.minSectionAverage},
            min_section_average_date = #{updated.minSectionAverageDate},
            average_sand_content = #{updated.averageSandContent},
            average_discharge_rate = #{updated.averageDischargeRate},
            total_value = #{updated.totalValue},
            discharge_modulus = #{updated.dischargeModulus},
            value = #{updated.value},
            value_days = #{updated.valueDays},
            remark = #{updated.remark},
            average_value_remark = #{updated.averageValueRemark},
            max_value_remark = #{updated.maxValueRemark},
            min_value_remark = #{updated.minValueRemark},
            version = #{updated.version},
            latest = #{updated.latest},
            update_time = #{updated.updateTime},
            updater = #{updated.updater}

            where id = #{updated.id}
        </foreach>
    </update>
</mapper>