<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationphoto.StationPhotoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        image_path,
        image_name,
        creator_name,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationphoto.StationPhotoDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="image_path" property="imagePath"/>
        <result column="image_name" property="imageName"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationphoto.StationPhotoDO">
        select *
        from hydrologic_station_photo
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted = 0;
    </select>

    <insert id="insertList">
        INSERT INTO hydrologic_station_photo(
            id,
            station_id,
            station_type,
            data_type,
            `year`,
            image_path,
            image_name,
            creator_name,
            create_time,
            update_time,
            creator,
            updater,
            deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.imagePath},
            #{element.imageName},
            #{element.creatorName},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_station_photo where id &lt; 1 and station_id=#{stationId} and creator='1';
    </select>

    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
            update hydrologic_station_photo set

            image_path = #{updated.imagePath},
            image_name = #{updated.imageName},
            update_time = #{updated.updateTime},
            updater = #{updated.updater}

            where id = #{updated.id}
        </foreach>
    </update>

    <insert id="batchInsert">
        INSERT INTO hydrologic_station_photo(
        station_id,
        station_type,
        data_type,
        `year`,
        image_path,
        image_name,
        creator_name,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.imagePath},
            #{element.imageName},
            #{element.creatorName},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0
            )
        </foreach>
    </insert>
</mapper>