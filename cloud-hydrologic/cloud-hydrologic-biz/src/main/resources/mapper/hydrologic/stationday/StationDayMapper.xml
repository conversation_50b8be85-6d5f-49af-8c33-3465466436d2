<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationday.StationDayMapper">
    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        `day`,
        current_day,
        `value`,
        remark,
        symbol,
        average_value,
        max_value,
        min_value,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationday.StationDayDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="current_day" property="currentDay"/>
        <result column="value" property="value"/>
        <result column="remark" property="remark"/>
        <result column="symbol" property="symbol"/>
        <result column="average_value" property="averageValue"/>
        <result column="max_value" property="maxValue"/>
        <result column="min_value" property="minValue"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <update id="updateHistoryVersion">
        update hydrologic_station_day set latest = 0 where station_id = #{stationId} and station_type = #{stationType} and data_type=#{dataType} and latest!=0
    </update>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <select id="selectMaxVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_station_day
        where deleted = 0
          and station_id = #{stationId}
          and station_type = #{stationType}
          and data_type = #{dataType}
    </select>
    <select id="listYear">
        select year
        from hydrologic_station_day
        where deleted = 0
          and station_id = #{stationId}
          and data_type = #{dataType}
          and latest = 1
          group by year order by year asc
    </select>
    <select id="getDayLatestVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_station_day
        where deleted = 0
          and latest = 1
          and station_id = #{stationId}
          and data_type = #{dataType}
    </select>

    <select id="getLatestVersion">
        SELECT version
        FROM hydrologic_station_day
        WHERE station_id = #{stationId}
          AND data_type = #{dataType}
          AND latest = 1
          AND deleted = 0
        limit 1
    </select>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationday.StationDayDO">
        select * from hydrologic_station_day
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0 and latest=1
    </select>


    <select id="listAllByDataType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hydrologic_station_day
        <where>
            <if test="dataType != null">
                and data_type=#{dataType}
            </if>
        </where>
    </select>


    <insert id="insertList">

        INSERT INTO hydrologic_station_day(
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        `day`,
        current_day,
        `value`,
        remark,
        symbol,
        average_value,
        max_value,
        min_value,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.month},
            #{element.day},
            #{element.currentDay},
            #{element.value},
            #{element.remark},
            #{element.symbol},
            #{element.averageValue},
            #{element.maxValue},
            #{element.minValue},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>


    <insert id="batchInsert">
        INSERT INTO hydrologic_station_day(
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        `day`,
        current_day,
        `value`,
        remark,
        symbol,
        average_value,
        max_value,
        min_value,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.month},
            #{element.day},
            #{element.currentDay},
            #{element.value},
            #{element.remark},
            #{element.symbol},
            #{element.averageValue},
            #{element.maxValue},
            #{element.minValue},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0
            )
        </foreach>
    </insert>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_station_day where id &lt; 1 and station_id=#{stationId} and creator='1';
    </select>


    <update id="updateById">
        update hydrologic_station_day
        <set>
            <if test="updated.id != null">
                id = #{updated.id},
            </if>
            <if test="updated.stationId != null">
                station_id = #{updated.stationId},
            </if>
            <if test="updated.stationType != null">
                station_type = #{updated.stationType},
            </if>
            <if test="updated.dataType != null">
                data_type = #{updated.dataType},
            </if>
            <if test="updated.year != null">
                year = #{updated.year},
            </if>
            <if test="updated.month != null">
                month = #{updated.month},
            </if>
            <if test="updated.day != null">
                day = #{updated.day},
            </if>
            <if test="updated.currentDay != null">
                current_day = #{updated.currentDay},
            </if>
            <if test="updated.value != null">
                value = #{updated.value},
            </if>
            <if test="updated.remark != null">
                remark = #{updated.remark},
            </if>
            <if test="updated.symbol != null">
                symbol = #{updated.symbol},
            </if>
            <if test="updated.averageValue != null">
                average_value = #{updated.averageValue},
            </if>
            <if test="updated.maxValue != null">
                max_value = #{updated.maxValue},
            </if>
            <if test="updated.minValue != null">
                min_value = #{updated.minValue},
            </if>
            <if test="updated.version != null">
                version = #{updated.version},
            </if>
            <if test="updated.latest != null">
                latest = #{updated.latest},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime},
            </if>
            <if test="updated.creator != null">
                creator = #{updated.creator},
            </if>
            <if test="updated.updater != null">
                updater = #{updated.updater},
            </if>
            <if test="updated.deleted != null">
                deleted = #{updated.deleted},
            </if>
        </set>
        where id=#{id}
    </update>

    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
            update hydrologic_station_day set
            value = #{updated.value},
            remark = #{updated.remark},
            symbol = #{updated.symbol},
            average_value = #{updated.averageValue},
            max_value = #{updated.maxValue},
            min_value = #{updated.minValue},
            version = #{updated.version},
            latest = #{updated.latest},
            create_time = #{updated.createTime},
            update_time = #{updated.updateTime},
            creator = #{updated.creator},
            updater = #{updated.updater}

            where id=#{updated.id}
        </foreach>
    </update>


    <insert id="insertSelective">
        INSERT INTO hydrologic_station_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="stationId != null">
                station_id,
            </if>
            <if test="stationType != null">
                station_type,
            </if>
            <if test="dataType != null">
                data_type,
            </if>
            <if test="year != null">
                `year`,
            </if>
            <if test="month != null">
                `month`,
            </if>
            <if test="day != null">
                `day`,
            </if>
            <if test="currentDay != null">
                current_day,
            </if>
            <if test="value != null">
                `value`,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="symbol != null">
                symbol,
            </if>
            <if test="averageValue != null">
                average_value,
            </if>
            <if test="maxValue != null">
                max_value,
            </if>
            <if test="minValue != null">
                min_value,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="latest != null">
                latest,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="deleted != null">
                deleted
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="stationId != null">
                #{stationId},
            </if>
            <if test="stationType != null">
                #{stationType},
            </if>
            <if test="dataType != null">
                #{dataType},
            </if>
            <if test="year != null">
                #{year},
            </if>
            <if test="month != null">
                #{month},
            </if>
            <if test="day != null">
                #{day},
            </if>
            <if test="currentDay != null">
                #{currentDay},
            </if>
            <if test="value != null">
                #{value},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="symbol != null">
                #{symbol},
            </if>
            <if test="averageValue != null">
                #{averageValue},
            </if>
            <if test="maxValue != null">
                #{maxValue},
            </if>
            <if test="minValue != null">
                #{minValue},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="latest != null">
                #{latest},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="updater != null">
                #{updater},
            </if>
            <if test="deleted != null">
                #{deleted}
            </if>
        </trim>
    </insert>
</mapper>