<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationmonth.StationMonthMapper">
    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        average_value,
        average_value_remark,
        max_value,
        max_value_date,
        max_value_remark,
        min_value,
        min_value_date,
        min_value_remark,
        `value`,
        value_days,
        max_day_value,
        grain005,
        grain007,
        grain01,
        grain025,
        grain05,
        grain1,
        grain25,
        grain5,
        grain10,
        grain20,
        grain30,
        grain_mid,
        grain_average,
        grain_max,
        version,
        latest,
        current_day,
        remark,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth.StationMonthDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="average_value" property="averageValue"/>
        <result column="average_value_remark" property="averageValueRemark"/>
        <result column="max_value" property="maxValue"/>
        <result column="max_value_date" property="maxValueDate"/>
        <result column="max_value_remark" property="maxValueRemark"/>
        <result column="min_value" property="minValue"/>
        <result column="min_value_date" property="minValueDate"/>
        <result column="min_value_remark" property="minValueRemark"/>
        <result column="value" property="value"/>
        <result column="value_days" property="valueDays"/>
        <result column="max_day_value" property="maxDayValue"/>
        <result column="grain005" property="grain005"/>
        <result column="grain007" property="grain007"/>
        <result column="grain01" property="grain01"/>
        <result column="grain025" property="grain025"/>
        <result column="grain05" property="grain05"/>
        <result column="grain1" property="grain1"/>
        <result column="grain25" property="grain25"/>
        <result column="grain5" property="grain5"/>
        <result column="grain10" property="grain10"/>
        <result column="grain20" property="grain20"/>
        <result column="grain30" property="grain30"/>
        <result column="grain_mid" property="grainMid"/>
        <result column="grain_average" property="grainAverage"/>
        <result column="grain_max" property="grainMax"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="current_day" property="currentDay"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <update id="updateHistoryVersion">
        update hydrologic_station_month
        set latest = 0
        where station_id = #{stationId}
          and station_type = #{stationType}
          and data_type=#{dataType}
          and latest!=0
    </update>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <select id="selectMaxVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_station_month
        where deleted = 0
          and station_id = #{stationId}
          and station_type = #{stationType}
          and data_type = #{dataType}
    </select>

    <select id="listYear" resultType="java.lang.Integer">
        select year
        from hydrologic_station_month
        where deleted = 0
          and station_id = #{stationId}
          and data_type = #{dataType}
        and latest = 1
        group by year order by year asc
    </select>
    <select id="getMonthLatestVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_station_month
        where deleted = 0
          and latest = 1
          and station_id = #{stationId}
          and data_type = #{dataType}
    </select>
    <select id="getLatestVersion" resultType="java.lang.Integer">
        SELECT version
        FROM hydrologic_station_month
        WHERE station_id = #{stationId}
          AND data_type = #{dataType}
          AND latest = 1
          AND deleted = 0
        limit 1
    </select>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth.StationMonthDO">

        select * from hydrologic_station_month
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0 and latest=1
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_station_month(
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        average_value,
        average_value_remark,
        max_value,
        max_value_date,
        max_value_remark,
        min_value,
        min_value_date,
        min_value_remark,
        `value`,
        value_days,
        max_day_value,
        grain005,
        grain007,
        grain01,
        grain025,
        grain05,
        grain1,
        grain25,
        grain5,
        grain10,
        grain20,
        grain30,
        grain_mid,
        grain_average,
        grain_max,
        version,
        latest,
        current_day,
        remark,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.month},
            #{element.averageValue},
            #{element.averageValueRemark},
            #{element.maxValue},
            #{element.maxValueDate},
            #{element.maxValueRemark},
            #{element.minValue},
            #{element.minValueDate},
            #{element.minValueRemark},
            #{element.value},
            #{element.valueDays},
            #{element.maxDayValue},
            #{element.grain005},
            #{element.grain007},
            #{element.grain01},
            #{element.grain025},
            #{element.grain05},
            #{element.grain1},
            #{element.grain25},
            #{element.grain5},
            #{element.grain10},
            #{element.grain20},
            #{element.grain30},
            #{element.grainMid},
            #{element.grainAverage},
            #{element.grainMax},
            #{element.version},
            #{element.latest},
            #{element.currentDay},
            #{element.remark},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_station_month where id &lt; 1 and station_id=#{stationId} and creator='1';
    </select>

    <insert id="batchInsert">
        INSERT INTO hydrologic_station_month(
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        average_value,
        average_value_remark,
        max_value,
        max_value_date,
        max_value_remark,
        min_value,
        min_value_date,
        min_value_remark,
        `value`,
        value_days,
        max_day_value,
        grain005,
        grain007,
        grain01,
        grain025,
        grain05,
        grain1,
        grain25,
        grain5,
        grain10,
        grain20,
        grain30,
        grain_mid,
        grain_average,
        grain_max,
        version,
        latest,
        current_day,
        remark,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.month},
            #{element.averageValue},
            #{element.averageValueRemark},
            #{element.maxValue},
            #{element.maxValueDate},
            #{element.maxValueRemark},
            #{element.minValue},
            #{element.minValueDate},
            #{element.minValueRemark},
            #{element.value},
            #{element.valueDays},
            #{element.maxDayValue},
            #{element.grain005},
            #{element.grain007},
            #{element.grain01},
            #{element.grain025},
            #{element.grain05},
            #{element.grain1},
            #{element.grain25},
            #{element.grain5},
            #{element.grain10},
            #{element.grain20},
            #{element.grain30},
            #{element.grainMid},
            #{element.grainAverage},
            #{element.grainMax},
            #{element.version},
            #{element.latest},
            #{element.currentDay},
            #{element.remark},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0
            )
        </foreach>
    </insert>



    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
            update hydrologic_station_month set

            average_value = #{updated.averageValue},
            average_value_remark = #{updated.averageValueRemark},
            max_value = #{updated.maxValue},
            max_value_date = #{updated.maxValueDate},
            max_value_remark = #{updated.maxValueRemark},
            min_value = #{updated.minValue},
            min_value_date = #{updated.minValueDate},
            min_value_remark = #{updated.minValueRemark},
            value = #{updated.value},
            value_days = #{updated.valueDays},
            max_day_value = #{updated.maxDayValue},
            grain005 = #{updated.grain005},
            grain007 = #{updated.grain007},
            grain01 = #{updated.grain01},
            grain025 = #{updated.grain025},
            grain05 = #{updated.grain05},
            grain1 = #{updated.grain1},
            grain25 = #{updated.grain25},
            grain5 = #{updated.grain5},
            grain10 = #{updated.grain10},
            grain20 = #{updated.grain20},
            grain30 = #{updated.grain30},
            grain_mid = #{updated.grainMid},
            grain_average = #{updated.grainAverage},
            grain_max = #{updated.grainMax},
            version = #{updated.version},
            latest = #{updated.latest},
            current_day = #{updated.currentDay},
            remark = #{updated.remark},
            update_time = #{updated.updateTime},
            updater = #{updated.updater}

            where id = #{updated.id}
        </foreach>
    </update>

    <select id="listCurrent"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationmonth.StationMonthDO">
        select * from hydrologic_station_month where station_id=#{stationId} and data_type=#{dataType} and latest=1 and deleted=0;
    </select>
</mapper>