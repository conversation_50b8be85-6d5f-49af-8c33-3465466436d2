<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.icestatistics.IceStatisticsMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        thawing_month_day,
        flowing_end_day,
        final_month_day,
        first_month_day,
        start_flow_month_day,
        frozen_month_day,
        first_half_year_days,
        second_half_year_days,
        center_max_thickness,
        center_max_thickness_date,
        shore_max_thickness,
        shore_max_thickness_date,
        flow_max_length,
        flow_max_width,
        ice_speed,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.icestatistics.IceStatisticsDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="thawing_month_day" property="thawingMonthDay"/>
        <result column="flowing_end_day" property="flowingEndDay"/>
        <result column="final_month_day" property="finalMonthDay"/>
        <result column="first_month_day" property="firstMonthDay"/>
        <result column="start_flow_month_day" property="startFlowMonthDay"/>
        <result column="frozen_month_day" property="frozenMonthDay"/>
        <result column="first_half_year_days" property="firstHalfYearDays"/>
        <result column="second_half_year_days" property="secondHalfYearDays"/>
        <result column="center_max_thickness" property="centerMaxThickness"/>
        <result column="center_max_thickness_date" property="centerMaxThicknessDate"/>
        <result column="shore_max_thickness" property="shoreMaxThickness"/>
        <result column="shore_max_thickness_date" property="shoreMaxThicknessDate"/>
        <result column="flow_max_length" property="flowMaxLength"/>
        <result column="flow_max_width" property="flowMaxWidth"/>
        <result column="ice_speed" property="iceSpeed"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.icestatistics.IceStatisticsDO">
        select * from hydrologic_ice_statistics
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0 and latest=1
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_ice_statistics(
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        thawing_month_day,
        flowing_end_day,
        final_month_day,
        first_month_day,
        start_flow_month_day,
        frozen_month_day,
        first_half_year_days,
        second_half_year_days,
        center_max_thickness,
        center_max_thickness_date,
        shore_max_thickness,
        shore_max_thickness_date,
        flow_max_length,
        flow_max_width,
        ice_speed,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.thawingMonthDay},
            #{element.flowingEndDay},
            #{element.finalMonthDay},
            #{element.firstMonthDay},
            #{element.startFlowMonthDay},
            #{element.frozenMonthDay},
            #{element.firstHalfYearDays},
            #{element.secondHalfYearDays},
            #{element.centerMaxThickness},
            #{element.centerMaxThicknessDate},
            #{element.shoreMaxThickness},
            #{element.shoreMaxThicknessDate},
            #{element.flowMaxLength},
            #{element.flowMaxWidth},
            #{element.iceSpeed},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <select id="listYear" resultType="java.lang.Integer">
        select year
        from hydrologic_ice_statistics
        where deleted = 0
        and station_id = #{stationId}
        and data_type = #{dataType}
        and latest = 1
        group by year order by year asc
    </select>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_ice_statistics where id &lt; 1 and station_id=#{stationId} and creator='1';
    </select>

    <select id="getLatestVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_ice_statistics
        where deleted = 0
        and latest = 1
        and station_id = #{stationId}
    </select>

    <insert id="batchInsert">
        INSERT INTO hydrologic_ice_statistics(
        station_id,
        station_type,
        data_type,
        `year`,
        thawing_month_day,
        flowing_end_day,
        final_month_day,
        first_month_day,
        start_flow_month_day,
        frozen_month_day,
        first_half_year_days,
        second_half_year_days,
        center_max_thickness,
        center_max_thickness_date,
        shore_max_thickness,
        shore_max_thickness_date,
        flow_max_length,
        flow_max_width,
        ice_speed,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.thawingMonthDay},
            #{element.flowingEndDay},
            #{element.finalMonthDay},
            #{element.firstMonthDay},
            #{element.startFlowMonthDay},
            #{element.frozenMonthDay},
            #{element.firstHalfYearDays},
            #{element.secondHalfYearDays},
            #{element.centerMaxThickness},
            #{element.centerMaxThicknessDate},
            #{element.shoreMaxThickness},
            #{element.shoreMaxThicknessDate},
            #{element.flowMaxLength},
            #{element.flowMaxWidth},
            #{element.iceSpeed},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0
            )
        </foreach>
    </insert>


    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
        update hydrologic_ice_statistics set

        thawing_month_day = #{updated.thawingMonthDay},
        flowing_end_day = #{updated.flowingEndDay},
        final_month_day = #{updated.finalMonthDay},
        first_month_day = #{updated.firstMonthDay},
        start_flow_month_day = #{updated.startFlowMonthDay},
        frozen_month_day = #{updated.frozenMonthDay},
        first_half_year_days = #{updated.firstHalfYearDays},
        second_half_year_days = #{updated.secondHalfYearDays},
        center_max_thickness = #{updated.centerMaxThickness},
        center_max_thickness_date = #{updated.centerMaxThicknessDate},
        shore_max_thickness = #{updated.shoreMaxThickness},
        shore_max_thickness_date = #{updated.shoreMaxThicknessDate},
        flow_max_length = #{updated.flowMaxLength},
        flow_max_width = #{updated.flowMaxWidth},
        ice_speed = #{updated.iceSpeed},
        version = #{updated.version},
        latest = #{updated.latest},
        update_time = #{updated.updateTime},
        updater = #{updated.updater}

        where id=#{updated.id}
        </foreach>
    </update>
</mapper>