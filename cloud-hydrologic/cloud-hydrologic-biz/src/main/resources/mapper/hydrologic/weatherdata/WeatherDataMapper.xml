<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.weatherdata.WeatherDataMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `month`,
        temperature_average_value,
        temperature_max_extremum,
        temperature_max_date,
        temperature_max_year,
        temperature_min_extremum,
        temperature_min_date,
        temperature_min_year,
        precipitation_average_value,
        precipitation_max_value,
        precipitation01_days,
        precipitation05_days,
        precipitation2_days,
        precipitation5_days,
        precipitation10_days,
        precipitation25_days,
        precipitation30_days,
        precipitation50_days,
        wind_lead_direction,
        wind_lead_direction_frequency,
        wind_average_value,
        wind_max_extremum,
        wind_max_date,
        wind_max_year,
        wind_max_direction,
        wind_days5,
        wind_days10,
        wind_days12,
        wind_days15,
        ground_tem_average_value,
        ground_tem_max_extremum,
        ground_tem_max_date,
        ground_tem_max_year,
        ground_tem_min_extremum,
        ground_tem_min_date,
        ground_tem_min_year,
        humidity_average_value,
        humidity_day_min,
        snow_depth_max_extremum,
        snow_depth_max_date,
        snow_depth_max_year,
        frozen_depth_max_extremum,
        frozen_depth_max_date,
        frozen_depth_max_year,
        average_air_pressure,
        sunlight_hours,
        number_of_foggy_days,
        number_of_hail_days,
        number_of_thunderstorm_days,
        number_of_snowfall_days,
        number_of_snow_covered_days,
        evaporation_capacity,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.weatherdata.WeatherDataDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="month" property="month"/>
        <result column="temperature_average_value" property="temperatureAverageValue"/>
        <result column="temperature_max_extremum" property="temperatureMaxExtremum"/>
        <result column="temperature_max_date" property="temperatureMaxDate"/>
        <result column="temperature_max_year" property="temperatureMaxYear"/>
        <result column="temperature_min_extremum" property="temperatureMinExtremum"/>
        <result column="temperature_min_date" property="temperatureMinDate"/>
        <result column="temperature_min_year" property="temperatureMinYear"/>
        <result column="precipitation_average_value" property="precipitationAverageValue"/>
        <result column="precipitation_max_value" property="precipitationMaxValue"/>
        <result column="precipitation01_days" property="precipitation01Days"/>
        <result column="precipitation05_days" property="precipitation05Days"/>
        <result column="precipitation2_days" property="precipitation2Days"/>
        <result column="precipitation5_days" property="precipitation5Days"/>
        <result column="precipitation10_days" property="precipitation10Days"/>
        <result column="precipitation25_days" property="precipitation25Days"/>
        <result column="precipitation30_days" property="precipitation30Days"/>
        <result column="precipitation50_days" property="precipitation50Days"/>
        <result column="wind_lead_direction" property="windLeadDirection"/>
        <result column="wind_lead_direction_frequency" property="windLeadDirectionFrequency"/>
        <result column="wind_average_value" property="windAverageValue"/>
        <result column="wind_max_extremum" property="windMaxExtremum"/>
        <result column="wind_max_date" property="windMaxDate"/>
        <result column="wind_max_year" property="windMaxYear"/>
        <result column="wind_max_direction" property="windMaxDirection"/>
        <result column="wind_days5" property="windDays5"/>
        <result column="wind_days10" property="windDays10"/>
        <result column="wind_days12" property="windDays12"/>
        <result column="wind_days15" property="windDays15"/>
        <result column="ground_tem_average_value" property="groundTemAverageValue"/>
        <result column="ground_tem_max_extremum" property="groundTemMaxExtremum"/>
        <result column="ground_tem_max_date" property="groundTemMaxDate"/>
        <result column="ground_tem_max_year" property="groundTemMaxYear"/>
        <result column="ground_tem_min_extremum" property="groundTemMinExtremum"/>
        <result column="ground_tem_min_date" property="groundTemMinDate"/>
        <result column="ground_tem_min_year" property="groundTemMinYear"/>
        <result column="humidity_average_value" property="humidityAverageValue"/>
        <result column="humidity_day_min" property="humidityDayMin"/>
        <result column="snow_depth_max_extremum" property="snowDepthMaxExtremum"/>
        <result column="snow_depth_max_date" property="snowDepthMaxDate"/>
        <result column="snow_depth_max_year" property="snowDepthMaxYear"/>
        <result column="frozen_depth_max_extremum" property="frozenDepthMaxExtremum"/>
        <result column="frozen_depth_max_date" property="frozenDepthMaxDate"/>
        <result column="frozen_depth_max_year" property="frozenDepthMaxYear"/>
        <result column="average_air_pressure" property="averageAirPressure"/>
        <result column="sunlight_hours" property="sunlightHours"/>
        <result column="number_of_foggy_days" property="numberOfFoggyDays"/>
        <result column="number_of_hail_days" property="numberOfHailDays"/>
        <result column="number_of_thunderstorm_days" property="numberOfThunderstormDays"/>
        <result column="number_of_snowfall_days" property="numberOfSnowfallDays"/>
        <result column="number_of_snow_covered_days" property="numberOfSnowCoveredDays"/>
        <result column="evaporation_capacity" property="evaporationCapacity"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.weatherdata.WeatherDataDO">
        select * from hydrologic_weather_data
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0 and latest=1
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_weather_data(
        id,
        station_id,
        station_type,
        data_type,
        `month`,
        temperature_average_value,
        temperature_max_extremum,
        temperature_max_date,
        temperature_max_year,
        temperature_min_extremum,
        temperature_min_date,
        temperature_min_year,
        precipitation_average_value,
        precipitation_max_value,
        precipitation01_days,
        precipitation05_days,
        precipitation2_days,
        precipitation5_days,
        precipitation10_days,
        precipitation25_days,
        precipitation30_days,
        precipitation50_days,
        wind_lead_direction,
        wind_lead_direction_frequency,
        wind_average_value,
        wind_max_extremum,
        wind_max_date,
        wind_max_year,
        wind_max_direction,
        wind_days5,
        wind_days10,
        wind_days12,
        wind_days15,
        ground_tem_average_value,
        ground_tem_max_extremum,
        ground_tem_max_date,
        ground_tem_max_year,
        ground_tem_min_extremum,
        ground_tem_min_date,
        ground_tem_min_year,
        humidity_average_value,
        humidity_day_min,
        snow_depth_max_extremum,
        snow_depth_max_date,
        snow_depth_max_year,
        frozen_depth_max_extremum,
        frozen_depth_max_date,
        frozen_depth_max_year,
        average_air_pressure,
        sunlight_hours,
        number_of_foggy_days,
        number_of_hail_days,
        number_of_thunderstorm_days,
        number_of_snowfall_days,
        number_of_snow_covered_days,
        evaporation_capacity,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.month},
            #{element.temperatureAverageValue},
            #{element.temperatureMaxExtremum},
            #{element.temperatureMaxDate},
            #{element.temperatureMaxYear},
            #{element.temperatureMinExtremum},
            #{element.temperatureMinDate},
            #{element.temperatureMinYear},
            #{element.precipitationAverageValue},
            #{element.precipitationMaxValue},
            #{element.precipitation01Days},
            #{element.precipitation05Days},
            #{element.precipitation2Days},
            #{element.precipitation5Days},
            #{element.precipitation10Days},
            #{element.precipitation25Days},
            #{element.precipitation30Days},
            #{element.precipitation50Days},
            #{element.windLeadDirection},
            #{element.windLeadDirectionFrequency},
            #{element.windAverageValue},
            #{element.windMaxExtremum},
            #{element.windMaxDate},
            #{element.windMaxYear},
            #{element.windMaxDirection},
            #{element.windDays5},
            #{element.windDays10},
            #{element.windDays12},
            #{element.windDays15},
            #{element.groundTemAverageValue},
            #{element.groundTemMaxExtremum},
            #{element.groundTemMaxDate},
            #{element.groundTemMaxYear},
            #{element.groundTemMinExtremum},
            #{element.groundTemMinDate},
            #{element.groundTemMinYear},
            #{element.humidityAverageValue},
            #{element.humidityDayMin},
            #{element.snowDepthMaxExtremum},
            #{element.snowDepthMaxDate},
            #{element.snowDepthMaxYear},
            #{element.frozenDepthMaxExtremum},
            #{element.frozenDepthMaxDate},
            #{element.frozenDepthMaxYear},
            #{element.averageAirPressure},
            #{element.sunlightHours},
            #{element.numberOfFoggyDays},
            #{element.numberOfHailDays},
            #{element.numberOfThunderstormDays},
            #{element.numberOfSnowfallDays},
            #{element.numberOfSnowCoveredDays},
            #{element.evaporationCapacity},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_weather_data where id &lt; 1  and station_id=#{stationId} and creator='1';
    </select>

    <insert id="batchInsert">

        INSERT INTO hydrologic_weather_data(
        station_id,
        station_type,
        data_type,
        `month`,
        temperature_average_value,
        temperature_max_extremum,
        temperature_max_date,
        temperature_max_year,
        temperature_min_extremum,
        temperature_min_date,
        temperature_min_year,
        precipitation_average_value,
        precipitation_max_value,
        precipitation01_days,
        precipitation05_days,
        precipitation2_days,
        precipitation5_days,
        precipitation10_days,
        precipitation25_days,
        precipitation30_days,
        precipitation50_days,
        wind_lead_direction,
        wind_lead_direction_frequency,
        wind_average_value,
        wind_max_extremum,
        wind_max_date,
        wind_max_year,
        wind_max_direction,
        wind_days5,
        wind_days10,
        wind_days12,
        wind_days15,
        ground_tem_average_value,
        ground_tem_max_extremum,
        ground_tem_max_date,
        ground_tem_max_year,
        ground_tem_min_extremum,
        ground_tem_min_date,
        ground_tem_min_year,
        humidity_average_value,
        humidity_day_min,
        snow_depth_max_extremum,
        snow_depth_max_date,
        snow_depth_max_year,
        frozen_depth_max_extremum,
        frozen_depth_max_date,
        frozen_depth_max_year,
        average_air_pressure,
        sunlight_hours,
        number_of_foggy_days,
        number_of_hail_days,
        number_of_thunderstorm_days,
        number_of_snowfall_days,
        number_of_snow_covered_days,
        evaporation_capacity,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.month},
            #{element.temperatureAverageValue},
            #{element.temperatureMaxExtremum},
            #{element.temperatureMaxDate},
            #{element.temperatureMaxYear},
            #{element.temperatureMinExtremum},
            #{element.temperatureMinDate},
            #{element.temperatureMinYear},
            #{element.precipitationAverageValue},
            #{element.precipitationMaxValue},
            #{element.precipitation01Days},
            #{element.precipitation05Days},
            #{element.precipitation2Days},
            #{element.precipitation5Days},
            #{element.precipitation10Days},
            #{element.precipitation25Days},
            #{element.precipitation30Days},
            #{element.precipitation50Days},
            #{element.windLeadDirection},
            #{element.windLeadDirectionFrequency},
            #{element.windAverageValue},
            #{element.windMaxExtremum},
            #{element.windMaxDate},
            #{element.windMaxYear},
            #{element.windMaxDirection},
            #{element.windDays5},
            #{element.windDays10},
            #{element.windDays12},
            #{element.windDays15},
            #{element.groundTemAverageValue},
            #{element.groundTemMaxExtremum},
            #{element.groundTemMaxDate},
            #{element.groundTemMaxYear},
            #{element.groundTemMinExtremum},
            #{element.groundTemMinDate},
            #{element.groundTemMinYear},
            #{element.humidityAverageValue},
            #{element.humidityDayMin},
            #{element.snowDepthMaxExtremum},
            #{element.snowDepthMaxDate},
            #{element.snowDepthMaxYear},
            #{element.frozenDepthMaxExtremum},
            #{element.frozenDepthMaxDate},
            #{element.frozenDepthMaxYear},
            #{element.averageAirPressure},
            #{element.sunlightHours},
            #{element.numberOfFoggyDays},
            #{element.numberOfHailDays},
            #{element.numberOfThunderstormDays},
            #{element.numberOfSnowfallDays},
            #{element.numberOfSnowCoveredDays},
            #{element.evaporationCapacity},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0
            )
        </foreach>
    </insert>


    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
        update hydrologic_weather_data set

            temperature_average_value = #{updated.temperatureAverageValue},
            temperature_max_extremum = #{updated.temperatureMaxExtremum},
            temperature_max_date = #{updated.temperatureMaxDate},
            temperature_max_year = #{updated.temperatureMaxYear},
            temperature_min_extremum = #{updated.temperatureMinExtremum},
            temperature_min_date = #{updated.temperatureMinDate},
            temperature_min_year = #{updated.temperatureMinYear},
            precipitation_average_value = #{updated.precipitationAverageValue},
            precipitation_max_value = #{updated.precipitationMaxValue},
            precipitation01_days = #{updated.precipitation01Days},
            precipitation05_days = #{updated.precipitation05Days},
            precipitation2_days = #{updated.precipitation2Days},
            precipitation5_days = #{updated.precipitation5Days},
            precipitation10_days = #{updated.precipitation10Days},
            precipitation25_days = #{updated.precipitation25Days},
            precipitation30_days = #{updated.precipitation30Days},
            precipitation50_days = #{updated.precipitation50Days},
            wind_lead_direction = #{updated.windLeadDirection},
            wind_lead_direction_frequency = #{updated.windLeadDirectionFrequency},
            wind_average_value = #{updated.windAverageValue},
            wind_max_extremum = #{updated.windMaxExtremum},
            wind_max_date = #{updated.windMaxDate},
            wind_max_year = #{updated.windMaxYear},
            wind_max_direction = #{updated.windMaxDirection},
            wind_days5 = #{updated.windDays5},
            wind_days10 = #{updated.windDays10},
            wind_days12 = #{updated.windDays12},
            wind_days15 = #{updated.windDays15},
            ground_tem_average_value = #{updated.groundTemAverageValue},
            ground_tem_max_extremum = #{updated.groundTemMaxExtremum},
            ground_tem_max_date = #{updated.groundTemMaxDate},
            ground_tem_max_year = #{updated.groundTemMaxYear},
            ground_tem_min_extremum = #{updated.groundTemMinExtremum},
            ground_tem_min_date = #{updated.groundTemMinDate},
            ground_tem_min_year = #{updated.groundTemMinYear},
            humidity_average_value = #{updated.humidityAverageValue},
            humidity_day_min = #{updated.humidityDayMin},
            snow_depth_max_extremum = #{updated.snowDepthMaxExtremum},
            snow_depth_max_date = #{updated.snowDepthMaxDate},
            snow_depth_max_year = #{updated.snowDepthMaxYear},
            frozen_depth_max_extremum = #{updated.frozenDepthMaxExtremum},
            frozen_depth_max_date = #{updated.frozenDepthMaxDate},
            frozen_depth_max_year = #{updated.frozenDepthMaxYear},
            average_air_pressure = #{updated.averageAirPressure},
            sunlight_hours = #{updated.sunlightHours},
            number_of_foggy_days = #{updated.numberOfFoggyDays},
            number_of_hail_days = #{updated.numberOfHailDays},
            number_of_thunderstorm_days = #{updated.numberOfThunderstormDays},
            number_of_snowfall_days = #{updated.numberOfSnowfallDays},
            number_of_snow_covered_days = #{updated.numberOfSnowCoveredDays},
            evaporation_capacity = #{updated.evaporationCapacity},
            version = #{updated.version},
            latest = #{updated.latest},
            update_time = #{updated.updateTime},
            updater = #{updated.updater}

        where id=#{updated.id}
        </foreach>
    </update>
</mapper>