<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.project.ProjectMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->


    <sql id="Base_Column_List">
        id,
        power_station_name,
        power_code,
        province,
        city,
        county,
        address,
        design_stage,
        upload_file,
        file_url,
        develop_way,
        longitude,
        latitude,
        total_capacity,
        storage_capacity,
        rated_head,
        full_shipping_hours,
        earthquake_intensity,
        evaporation,
        leakage,
        water_loss_reserve_storage,
        frozen_storage_capacity,
        wheel_radius,
        rated_speed,
        sucking_height,
        up_anti_seepage_form,
        up_maximum_dam_height,
        up_normal_water_storage_level,
        up_dead_water_level,
        up_dead_storage_capacity,
        up_dam_length,
        down_anti_seepage_form,
        down_maximum_dam_height,
        down_normal_water_storage_level,
        down_dead_water_level,
        down_dead_storage_capacity,
        down_dam_length,
        adjusting_storage_capacity,
        length_of_water_conveyance_system,
        distance_to_height_ratio,
        duration,
        engineering_investment,
        static_investment,
        coal_saving_amount,
        economic_internal_rate_of_return,
        capital_ratio,
        capacity_payment,
        annual_capacity_electricity_fee,
        under_construction_capacity,
        approval_time,
        approval_authority,
        start_time,
        built_capacity,
        first_production_time,
        all_production_time,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap" type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.project.ProjectDO">
        <result column="id" property="id"/>
        <result column="power_station_name" property="powerStationName"/>
        <result column="power_code" property="powerCode"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="county" property="county"/>
        <result column="address" property="address"/>
        <result column="design_stage" property="designStage"/>
        <result column="upload_file" property="uploadFile"/>
        <result column="file_url" property="fileUrl"/>
        <result column="develop_way" property="developWay"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="total_capacity" property="totalCapacity"/>
        <result column="storage_capacity" property="storageCapacity"/>
        <result column="rated_head" property="ratedHead"/>
        <result column="full_shipping_hours" property="fullShippingHours"/>
        <result column="earthquake_intensity" property="earthquakeIntensity"/>
        <result column="evaporation" property="evaporation"/>
        <result column="leakage" property="leakage"/>
        <result column="water_loss_reserve_storage" property="waterLossReserveStorage"/>
        <result column="frozen_storage_capacity" property="frozenStorageCapacity"/>
        <result column="wheel_radius" property="wheelRadius"/>
        <result column="rated_speed" property="ratedSpeed"/>
        <result column="sucking_height" property="suckingHeight"/>
        <result column="up_anti_seepage_form" property="upAntiSeepageForm"/>
        <result column="up_maximum_dam_height" property="upMaximumDamHeight"/>
        <result column="up_normal_water_storage_level" property="upNormalWaterStorageLevel"/>
        <result column="up_dead_water_level" property="upDeadWaterLevel"/>
        <result column="up_dead_storage_capacity" property="upDeadStorageCapacity"/>
        <result column="up_dam_length" property="upDamLength"/>
        <result column="down_anti_seepage_form" property="downAntiSeepageForm"/>
        <result column="down_maximum_dam_height" property="downMaximumDamHeight"/>
        <result column="down_normal_water_storage_level" property="downNormalWaterStorageLevel"/>
        <result column="down_dead_water_level" property="downDeadWaterLevel"/>
        <result column="down_dead_storage_capacity" property="downDeadStorageCapacity"/>
        <result column="down_dam_length" property="downDamLength"/>
        <result column="adjusting_storage_capacity" property="adjustingStorageCapacity"/>
        <result column="length_of_water_conveyance_system" property="lengthOfWaterConveyanceSystem"/>
        <result column="distance_to_height_ratio" property="distanceToHeightRatio"/>
        <result column="duration" property="duration"/>
        <result column="engineering_investment" property="engineeringInvestment"/>
        <result column="static_investment" property="staticInvestment"/>
        <result column="coal_saving_amount" property="coalSavingAmount"/>
        <result column="economic_internal_rate_of_return" property="economicInternalRateOfReturn"/>
        <result column="capital_ratio" property="capitalRatio"/>
        <result column="capacity_payment" property="capacityPayment"/>
        <result column="annual_capacity_electricity_fee" property="annualCapacityElectricityFee"/>
        <result column="under_construction_capacity" property="underConstructionCapacity"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="approval_authority" property="approvalAuthority"/>
        <result column="start_time" property="startTime"/>
        <result column="built_capacity" property="builtCapacity"/>
        <result column="first_production_time" property="firstProductionTime"/>
        <result column="all_production_time" property="allProductionTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="selectListByStationNames"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.project.ProjectDO">
        select * from hydrologic_project where power_station_name in
        <foreach collection="stationNames" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_project(
        id,
        power_station_name,
        power_code,
        province,
        city,
        county,
        address,
        design_stage,
        upload_file,
        file_url,
        develop_way,
        longitude,
        latitude,
        total_capacity,
        storage_capacity,
        rated_head,
        full_shipping_hours,
        earthquake_intensity,
        evaporation,
        leakage,
        water_loss_reserve_storage,
        frozen_storage_capacity,
        wheel_radius,
        rated_speed,
        sucking_height,
        up_anti_seepage_form,
        up_maximum_dam_height,
        up_normal_water_storage_level,
        up_dead_water_level,
        up_dead_storage_capacity,
        up_dam_length,
        down_anti_seepage_form,
        down_maximum_dam_height,
        down_normal_water_storage_level,
        down_dead_water_level,
        down_dead_storage_capacity,
        down_dam_length,
        adjusting_storage_capacity,
        length_of_water_conveyance_system,
        distance_to_height_ratio,
        duration,
        engineering_investment,
        static_investment,
        coal_saving_amount,
        economic_internal_rate_of_return,
        capital_ratio,
        capacity_payment,
        annual_capacity_electricity_fee,
        under_construction_capacity,
        approval_time,
        approval_authority,
        start_time,
        built_capacity,
        first_production_time,
        all_production_time,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.powerStationName},
            #{element.powerCode},
            #{element.province},
            #{element.city},
            #{element.county},
            #{element.address},
            #{element.designStage},
            #{element.uploadFile},
            #{element.fileUrl},
            #{element.developWay},
            #{element.longitude},
            #{element.latitude},
            #{element.totalCapacity},
            #{element.storageCapacity},
            #{element.ratedHead},
            #{element.fullShippingHours},
            #{element.earthquakeIntensity},
            #{element.evaporation},
            #{element.leakage},
            #{element.waterLossReserveStorage},
            #{element.frozenStorageCapacity},
            #{element.wheelRadius},
            #{element.ratedSpeed},
            #{element.suckingHeight},
            #{element.upAntiSeepageForm},
            #{element.upMaximumDamHeight},
            #{element.upNormalWaterStorageLevel},
            #{element.upDeadWaterLevel},
            #{element.upDeadStorageCapacity},
            #{element.upDamLength},
            #{element.downAntiSeepageForm},
            #{element.downMaximumDamHeight},
            #{element.downNormalWaterStorageLevel},
            #{element.downDeadWaterLevel},
            #{element.downDeadStorageCapacity},
            #{element.downDamLength},
            #{element.adjustingStorageCapacity},
            #{element.lengthOfWaterConveyanceSystem},
            #{element.distanceToHeightRatio},
            #{element.duration},
            #{element.engineeringInvestment},
            #{element.staticInvestment},
            #{element.coalSavingAmount},
            #{element.economicInternalRateOfReturn},
            #{element.capitalRatio},
            #{element.capacityPayment},
            #{element.annualCapacityElectricityFee},
            #{element.underConstructionCapacity},
            #{element.approvalTime},
            #{element.approvalAuthority},
            #{element.startTime},
            #{element.builtCapacity},
            #{element.firstProductionTime},
            #{element.allProductionTime},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <delete id="deleteTemporaryData">
        delete from hydrologic_project where id &lt; 1;
        delete from hydrologic_file_tree where id &lt; 1;
        delete from hydrologic_file_document where id &lt; 1;
        delete from hydrologic_station where id &lt; 1;
        delete from hydrologic_station_photo where id &lt; 1;
        delete from hydrologic_statistical_info where id &lt; 1;
        delete from hydrologic_station_day where id &lt; 1;
        delete from hydrologic_station_month where id &lt; 1;
        delete from hydrologic_station_year where id &lt; 1;
        delete from hydrologic_station_result where id &lt; 1;
        delete from hydrologic_station_extract where id &lt; 1;
        delete from hydrologic_station_rain_period_day where id &lt; 1;
        delete from hydrologic_station_rain_period_hour where id &lt; 1;
        delete from hydrologic_station_rain_period_minute where id &lt; 1;
        delete from hydrologic_ice_statistics   where id &lt; 1;
        delete from hydrologic_water_level_guarantee  where id &lt; 1;
        delete from hydrologic_weather_data   where id &lt; 1;
        delete from hydrologic_station_data_log where id &lt; 1;
    </delete>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_project where id &lt; 1 and creator='1';
    </select>

    <update id="updateCreator">
        update hydrologic_project set creator='1' where id &lt; 1 and creator='0';
    </update>
</mapper>