<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.filedocument.FileDocumentMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->


    <sql id="Base_Column_List">
        id,
        tree_id,
        file_name,
        file_code,
        file_path,
        creator_name,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filedocument.FileDocumentDO">
        <result column="id" property="id"/>
        <result column="tree_id" property="treeId"/>
        <result column="file_name" property="fileName"/>
        <result column="file_code" property="fileCode"/>
        <result column="file_path" property="filePath"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="selectListByTreeIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.filedocument.FileDocumentDO">
        select *
        from hydrologic_file_document where tree_id in
        <foreach collection="treeIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted = 0
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_file_document(id,
                                             tree_id,
                                             file_name,
                                             file_code,
                                             file_path,
                                             creator_name,
                                             create_time,
                                             update_time,
                                             creator,
                                             updater,
                                             deleted)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.treeId},
             #{element.fileName},
             #{element.fileCode},
             #{element.filePath},
             #{element.creatorName},
             now(),
             now(),
             0,
             0,
             1)
        </foreach>
    </insert>

    <update id="updateTreeId">
        update hydrologic_file_document
        set tree_id=#{newFileTreeId}
        where tree_id = #{oldFileTreeId}
    </update>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_file_document where id&lt; 1 and creator='1' and tree_id=#{treeId}
    </select>
</mapper>