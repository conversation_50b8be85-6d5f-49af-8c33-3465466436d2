<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationextract.StationExtractMapper">
    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        `day`,
        hours_minute,
        ice_situation,
        ice_thickness,
        deep_snow_on_ice,
        shore_temperature,
        water_level,
        flow,
        sand_value,
        `value`,
        start_hour_minute,
        end_hour_minute,
        version,
        latest,
        current_day,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationextract.StationExtractDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="day" property="day"/>
        <result column="hours_minute" property="hoursMinute"/>
        <result column="ice_situation" property="iceSituation"/>
        <result column="ice_thickness" property="iceThickness"/>
        <result column="deep_snow_on_ice" property="deepSnowOnIce"/>
        <result column="shore_temperature" property="shoreTemperature"/>
        <result column="water_level" property="waterLevel"/>
        <result column="flow" property="flow"/>
        <result column="sand_value" property="sandValue"/>
        <result column="value" property="value"/>
        <result column="start_hour_minute" property="startHourMinute"/>
        <result column="end_hour_minute" property="endHourMinute"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="current_day" property="currentDay"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <update id="updateHistoryVersion">
        update hydrologic_station_extract set latest = 0 where station_id = #{stationId} and station_type = #{stationType} and data_type = #{dataType} and latest!=0
    </update>

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <select id="selectMaxVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_station_extract
        where deleted = 0
          and station_id = #{stationId}
          and station_type = #{stationType}
          and data_type = #{dataType}
    </select>
    <select id="listYear">
        select year
        from hydrologic_station_extract
        where deleted = 0
          and station_id = #{stationId}
          and data_type = #{dataType}
          and latest = 1
        group by year order by year asc
    </select>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationextract.StationExtractDO">
        select * from hydrologic_station_extract
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0 and latest=1
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_station_extract(
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        `day`,
        hours_minute,
        ice_situation,
        ice_thickness,
        deep_snow_on_ice,
        shore_temperature,
        water_level,
        flow,
        sand_value,
        `value`,
        start_hour_minute,
        end_hour_minute,
        version,
        latest,
        current_day,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.month},
            #{element.day},
            #{element.hoursMinute},
            #{element.iceSituation},
            #{element.iceThickness},
            #{element.deepSnowOnIce},
            #{element.shoreTemperature},
            #{element.waterLevel},
            #{element.flow},
            #{element.sandValue},
            #{element.value},
            #{element.startHourMinute},
            #{element.endHourMinute},
            #{element.version},
            #{element.latest},
            #{element.currentDay},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_station_extract where id &lt; 1 and station_id=#{stationId} and creator='1';
    </select>

    <select id="getLatestVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_station_extract
        where deleted = 0
        and latest = 1
        and station_id = #{stationId}
        and data_type = #{dataType}
    </select>

    <insert id="batchInsert">
        INSERT INTO hydrologic_station_extract(
        station_id,
        station_type,
        data_type,
        `year`,
        `month`,
        `day`,
        hours_minute,
        ice_situation,
        ice_thickness,
        deep_snow_on_ice,
        shore_temperature,
        water_level,
        flow,
        sand_value,
        `value`,
        start_hour_minute,
        end_hour_minute,
        version,
        latest,
        current_day,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.month},
            #{element.day},
            #{element.hoursMinute},
            #{element.iceSituation},
            #{element.iceThickness},
            #{element.deepSnowOnIce},
            #{element.shoreTemperature},
            #{element.waterLevel},
            #{element.flow},
            #{element.sandValue},
            #{element.value},
            #{element.startHourMinute},
            #{element.endHourMinute},
            #{element.version},
            #{element.latest},
            #{element.currentDay},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0
            )
        </foreach>
    </insert>


    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
        update hydrologic_station_extract set

            ice_situation = #{updated.iceSituation},
            ice_thickness = #{updated.iceThickness},
            deep_snow_on_ice = #{updated.deepSnowOnIce},
            shore_temperature = #{updated.shoreTemperature},
            water_level = #{updated.waterLevel},
            flow = #{updated.flow},
            sand_value = #{updated.sandValue},
            value = #{updated.value},
            start_hour_minute = #{updated.startHourMinute},
            end_hour_minute = #{updated.endHourMinute},
            version = #{updated.version},
            latest = #{updated.latest},
            update_time = #{updated.updateTime},
            updater = #{updated.updater}

        where id=#{updated.id}
        </foreach>
    </update>
</mapper>