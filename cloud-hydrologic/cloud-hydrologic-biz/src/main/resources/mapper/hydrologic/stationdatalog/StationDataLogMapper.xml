<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationdatalog.StationDataLogMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->


    <sql id="Base_Column_List">
        id,
        station_type,
        `year`,
        year_remark,
        station_id,
        data_type,
        current_version,
        day_version,
        month_version,
        year_version,
        period_day_version,
        period_hour_version,
        period_minute_version,
        creator_name,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO">
        <result column="id" property="id"/>
        <result column="station_type" property="stationType"/>
        <result column="year" property="year"/>
        <result column="year_remark" property="yearRemark"/>
        <result column="station_id" property="stationId"/>
        <result column="data_type" property="dataType"/>
        <result column="current_version" property="currentVersion"/>
        <result column="day_version" property="dayVersion"/>
        <result column="month_version" property="monthVersion"/>
        <result column="year_version" property="yearVersion"/>
        <result column="period_day_version" property="periodDayVersion"/>
        <result column="period_hour_version" property="periodHourVersion"/>
        <result column="period_minute_version" property="periodMinuteVersion"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="listYearLogByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationdatalog.StationDataLogDO">
        select * from hydrologic_station_data_log
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and year is not null
        and deleted=0
    </select>

    <insert id="insertList">
        INSERT INTO hydrologic_station_data_log(
        id,
        station_type,
        `year`,
        year_remark,
        station_id,
        data_type,
        current_version,
        day_version,
        month_version,
        year_version,
        period_day_version,
        period_hour_version,
        period_minute_version,
        creator_name,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationType},
            #{element.year},
            #{element.yearRemark},
            #{element.stationId},
            #{element.dataType},
            #{element.currentVersion},
            #{element.dayVersion},
            #{element.monthVersion},
            #{element.yearVersion},
            #{element.periodDayVersion},
            #{element.periodHourVersion},
            #{element.periodMinuteVersion},
            #{element.creatorName},
            #{element.createTime},
            #{element.updateTime},
            #{element.creator},
            #{element.updater},
            #{element.deleted}
            )
        </foreach>
    </insert>

    <select id="listByStationId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hydrologic_station_data_log
        where station_id=#{stationId}
    </select>

    <select id="getLatest" resultMap="BaseResultMap">
        select * from hydrologic_station_data_log where station_id=#{stationId} and data_type=#{dataType} order by current_version desc limit 1
    </select>
</mapper>