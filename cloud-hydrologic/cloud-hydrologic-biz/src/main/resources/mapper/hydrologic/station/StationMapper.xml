<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.station.StationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <sql id="Base_Column_List">
        id,
        station_type,
        hydrologic_code,
        hydrologic_name,
        hydrologic_type,
        river_system,
        river_name,
        province,
        city,
        county,
        address,
        longitude,
        latitude,
        catchment_area,
        distance_from_the_river_mouth,
        `year`,
        `month`,
        data_project,
        leadership_organization,
        historical_evolution,
        control_conditions,
        absolute_height,
        ground_height,
        `type`,
        altitude,
        site_relocation,
        start_observation_year,
        start_observation_month,
        missing_testing_time_period,
        observation_method,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap" type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.station.StationDO">
        <result column="id" property="id"/>
        <result column="station_type" property="stationType"/>
        <result column="hydrologic_code" property="hydrologicCode"/>
        <result column="hydrologic_name" property="hydrologicName"/>
        <result column="hydrologic_type" property="hydrologicType"/>
        <result column="river_system" property="riverSystem"/>
        <result column="river_name" property="riverName"/>
        <result column="country" property="country"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="county" property="county"/>
        <result column="address" property="address"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="catchment_area" property="catchmentArea"/>
        <result column="distance_from_the_river_mouth" property="distanceFromTheRiverMouth"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="data_project" property="dataProject"/>
        <result column="leadership_organization" property="leadershipOrganization"/>
        <result column="historical_evolution" property="historicalEvolution"/>
        <result column="control_conditions" property="controlConditions"/>
        <result column="absolute_height" property="absoluteHeight"/>
        <result column="ground_height" property="groundHeight"/>
        <result column="type" property="type"/>
        <result column="altitude" property="altitude"/>
        <result column="site_relocation" property="siteRelocation"/>
        <result column="start_observation_year" property="startObservationYear"/>
        <result column="start_observation_month" property="startObservationMonth"/>
        <result column="missing_testing_time_period" property="missingTestingTimePeriod"/>
        <result column="observation_method" property="observationMethod"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="selectListAll" parameterType="cn.powerchina.bjy.cloud.institute.hydrologic.controller.admin.index.vo.IndexReqVO"
            resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM hydrologic_station
    <where>
        deleted=0
        <if test="name!= null and name!=''" >
            and hydrologic_name like concat('%', #{name}, '%')
        </if>
        <if test="country!= null and country!=''" >
            and country=#{country}
        </if>

        <if test="province!= null and province!=''" >
            and province=#{province}
        </if>

        <if test="city!= null and city!=''" >
            and city=#{city}
        </if>
        <if test="stationType!= null" >
            and station_type=#{stationType}
        </if>
        <if test="monitoringItems!= null and monitoringItems!=''" >
            and data_project like concat('%', #{monitoringItems}, '%')
        </if>
        <if test="stationIdList!=null and stationIdList.size()>0">
            and id NOT IN
            <foreach collection="stationIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </where>
    </select>
    <select id="selectByStationId" resultMap="BaseResultMap">
        SELECT x.* FROM hydrologic_station x where x.id=#{stationId}
    </select>
    <select id="getAllCountry" resultType="java.lang.Long">
        SELECT x.country FROM hydrologic_station x where x.station_type=#{stationType} and x.deleted=0 group by x.country
    </select>
    <select id="getAllProvince" resultType="java.lang.Long">
        SELECT x.province FROM hydrologic_station x where x.station_type=#{stationType} and x.deleted=0 group by x.province
    </select>
    <select id="getCityByProvinceId" resultType="java.lang.Long">
        SELECT x.city FROM hydrologic_station x where x.station_type =#{stationType} and x.province =#{provinceId} and x.deleted=0 GROUP by x.city
    </select>
    <select id="getStationByCountryId" resultType="java.lang.String" resultMap="BaseResultMap">
        SELECT x.* FROM hydrologic_station x where x.station_type =#{stationType} and x.country =#{countryId} and x.deleted=0
    </select>
    <select id="getStationById" resultType="java.lang.String" resultMap="BaseResultMap">
        SELECT x.* FROM hydrologic_station x where x.station_type =#{stationType} and x.province =#{provinceId} and  x.city=#{cityId} and x.deleted=0
    </select>
    <select id="selectByNameAndType" resultType="java.lang.Long">
        select id from hydrologic_station where station_type=#{type} and hydrologic_name=#{name} and deleted=0
    </select>
    <select id="selectByCodeAndType" resultType="java.lang.Long">
        select id from hydrologic_station where station_type=#{type} and hydrologic_code=#{code} and deleted=0
    </select>

    <insert id="insertList">
        INSERT INTO hydrologic_station(
        id,
        station_type,
        hydrologic_code,
        hydrologic_name,
        hydrologic_type,
        river_system,
        river_name,
        province,
        city,
        county,
        address,
        longitude,
        latitude,
        catchment_area,
        distance_from_the_river_mouth,
        `year`,
        `month`,
        data_project,
        leadership_organization,
        historical_evolution,
        control_conditions,
        absolute_height,
        ground_height,
        `type`,
        altitude,
        site_relocation,
        start_observation_year,
        start_observation_month,
        missing_testing_time_period,
        observation_method,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationType},
            #{element.hydrologicCode},
            #{element.hydrologicName},
            #{element.hydrologicType},
            #{element.riverSystem},
            #{element.riverName},
            #{element.province},
            #{element.city},
            #{element.county},
            #{element.address},
            #{element.longitude},
            #{element.latitude},
            #{element.catchmentArea},
            #{element.distanceFromTheRiverMouth},
            #{element.year},
            #{element.month},
            #{element.dataProject},
            #{element.leadershipOrganization},
            #{element.historicalEvolution},
            #{element.controlConditions},
            #{element.absoluteHeight},
            #{element.groundHeight},
            #{element.type},
            #{element.altitude},
            #{element.siteRelocation},
            #{element.startObservationYear},
            #{element.startObservationMonth},
            #{element.missingTestingTimePeriod},
            #{element.observationMethod},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_station where id &lt; 1 and creator='1';
    </select>

    <update id="updateStationId">
        update  hydrologic_station_photo set station_id=#{newStationId} where station_id=#{oldStationId};
<!--        update  hydrologic_statistical_info set station_id=#{newStationId} where station_id=#{oldStationId};-->
        update  hydrologic_station_day set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_station_month set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_station_year set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_station_result set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_station_extract set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_station_rain_period_day set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_station_rain_period_hour set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_station_rain_period_minute set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_ice_statistics   set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_water_level_guarantee  set station_id=#{newStationId} where station_id=#{oldStationId};
        update  hydrologic_weather_data   set station_id=#{newStationId} where station_id=#{oldStationId};
    </update>

    <update id="updateCreator">
        update hydrologic_station set creator='1' where id &lt; 1 and creator='0';
    </update>

    <update id="updateMonitoringDataCreator">
        update  hydrologic_station_photo set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_statistical_info set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_station_day set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_station_month set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_station_year set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_station_result set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_station_extract set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_station_rain_period_day set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_station_rain_period_hour set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_station_rain_period_minute set creator='1' where station_type=#{stationType} and  id &lt; 1 and creator='0';
        update  hydrologic_ice_statistics   set creator='1' where  id &lt; 1 and creator='0';
        update  hydrologic_water_level_guarantee  set creator='1' where  id &lt; 1 and creator='0';
        update  hydrologic_weather_data   set creator='1' where  id &lt; 1 and creator='0';
    </update>
</mapper>