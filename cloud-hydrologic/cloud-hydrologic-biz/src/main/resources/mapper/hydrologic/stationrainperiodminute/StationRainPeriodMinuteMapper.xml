<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationrainperiodminute.StationRainPeriodMinuteMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        value_day10,
        month_day_start10,
        value_day20,
        month_day_start20,
        value_day30,
        month_day_start30,
        value_day45,
        month_day_start45,
        value_day60,
        month_day_start60,
        value_day90,
        month_day_start90,
        value_day120,
        month_day_start120,
        value_day180,
        month_day_start180,
        value_day240,
        month_day_start240,
        value_day360,
        month_day_start360,
        value_day540,
        month_day_start540,
        value_day720,
        month_day_start720,
        value_day1440,
        month_day_start1440,
        remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodminute.StationRainPeriodMinuteDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="value_day10" property="valueDay10"/>
        <result column="month_day_start10" property="monthDayStart10"/>
        <result column="value_day20" property="valueDay20"/>
        <result column="month_day_start20" property="monthDayStart20"/>
        <result column="value_day30" property="valueDay30"/>
        <result column="month_day_start30" property="monthDayStart30"/>
        <result column="value_day45" property="valueDay45"/>
        <result column="month_day_start45" property="monthDayStart45"/>
        <result column="value_day60" property="valueDay60"/>
        <result column="month_day_start60" property="monthDayStart60"/>
        <result column="value_day90" property="valueDay90"/>
        <result column="month_day_start90" property="monthDayStart90"/>
        <result column="value_day120" property="valueDay120"/>
        <result column="month_day_start120" property="monthDayStart120"/>
        <result column="value_day180" property="valueDay180"/>
        <result column="month_day_start180" property="monthDayStart180"/>
        <result column="value_day240" property="valueDay240"/>
        <result column="month_day_start240" property="monthDayStart240"/>
        <result column="value_day360" property="valueDay360"/>
        <result column="month_day_start360" property="monthDayStart360"/>
        <result column="value_day540" property="valueDay540"/>
        <result column="month_day_start540" property="monthDayStart540"/>
        <result column="value_day720" property="valueDay720"/>
        <result column="month_day_start720" property="monthDayStart720"/>
        <result column="value_day1440" property="valueDay1440"/>
        <result column="month_day_start1440" property="monthDayStart1440"/>
        <result column="remark" property="remark"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodminute.StationRainPeriodMinuteDO">
        select * from hydrologic_station_rain_period_minute
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0 and latest=1
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_station_rain_period_minute(
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        value_day10,
        month_day_start10,
        value_day20,
        month_day_start20,
        value_day30,
        month_day_start30,
        value_day45,
        month_day_start45,
        value_day60,
        month_day_start60,
        value_day90,
        month_day_start90,
        value_day120,
        month_day_start120,
        value_day180,
        month_day_start180,
        value_day240,
        month_day_start240,
        value_day360,
        month_day_start360,
        value_day540,
        month_day_start540,
        value_day720,
        month_day_start720,
        value_day1440,
        month_day_start1440,
        remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.valueDay10},
            #{element.monthDayStart10},
            #{element.valueDay20},
            #{element.monthDayStart20},
            #{element.valueDay30},
            #{element.monthDayStart30},
            #{element.valueDay45},
            #{element.monthDayStart45},
            #{element.valueDay60},
            #{element.monthDayStart60},
            #{element.valueDay90},
            #{element.monthDayStart90},
            #{element.valueDay120},
            #{element.monthDayStart120},
            #{element.valueDay180},
            #{element.monthDayStart180},
            #{element.valueDay240},
            #{element.monthDayStart240},
            #{element.valueDay360},
            #{element.monthDayStart360},
            #{element.valueDay540},
            #{element.monthDayStart540},
            #{element.valueDay720},
            #{element.monthDayStart720},
            #{element.valueDay1440},
            #{element.monthDayStart1440},
            #{element.remark},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            0,
            0,
            1
            )
        </foreach>
    </insert>

    <select id="listYear" resultType="java.lang.Integer">
        select year
        from hydrologic_station_rain_period_minute
        where deleted = 0
        and station_id = #{stationId}
        and data_type = #{dataType}
        and latest = 1
        group by year order by year asc
    </select>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_station_rain_period_minute where id &lt; 1  and station_id=#{stationId} and creator='1';
    </select>

    <insert id="batchInsert">
        INSERT INTO hydrologic_station_rain_period_minute(
        station_id,
        station_type,
        data_type,
        `year`,
        value_day10,
        month_day_start10,
        value_day20,
        month_day_start20,
        value_day30,
        month_day_start30,
        value_day45,
        month_day_start45,
        value_day60,
        month_day_start60,
        value_day90,
        month_day_start90,
        value_day120,
        month_day_start120,
        value_day180,
        month_day_start180,
        value_day240,
        month_day_start240,
        value_day360,
        month_day_start360,
        value_day540,
        month_day_start540,
        value_day720,
        month_day_start720,
        value_day1440,
        month_day_start1440,
        remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.valueDay10},
            #{element.monthDayStart10},
            #{element.valueDay20},
            #{element.monthDayStart20},
            #{element.valueDay30},
            #{element.monthDayStart30},
            #{element.valueDay45},
            #{element.monthDayStart45},
            #{element.valueDay60},
            #{element.monthDayStart60},
            #{element.valueDay90},
            #{element.monthDayStart90},
            #{element.valueDay120},
            #{element.monthDayStart120},
            #{element.valueDay180},
            #{element.monthDayStart180},
            #{element.valueDay240},
            #{element.monthDayStart240},
            #{element.valueDay360},
            #{element.monthDayStart360},
            #{element.valueDay540},
            #{element.monthDayStart540},
            #{element.valueDay720},
            #{element.monthDayStart720},
            #{element.valueDay1440},
            #{element.monthDayStart1440},
            #{element.remark},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0
            )
        </foreach>
    </insert>
    <select id="selectLatestData" resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodminute.StationRainPeriodMinuteDO">
        select *
        from hydrologic_station_rain_period_minute
        where station_id = #{stationId} and data_type=#{dataType} and latest = 1 and deleted=0
    </select>

    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
        update hydrologic_station_rain_period_minute set

            value_day10 = #{updated.valueDay10},
            month_day_start10 = #{updated.monthDayStart10},
            value_day20 = #{updated.valueDay20},
            month_day_start20 = #{updated.monthDayStart20},
            value_day30 = #{updated.valueDay30},
            month_day_start30 = #{updated.monthDayStart30},
            value_day45 = #{updated.valueDay45},
            month_day_start45 = #{updated.monthDayStart45},
            value_day60 = #{updated.valueDay60},
            month_day_start60 = #{updated.monthDayStart60},
            value_day90 = #{updated.valueDay90},
            month_day_start90 = #{updated.monthDayStart90},
            value_day120 = #{updated.valueDay120},
            month_day_start120 = #{updated.monthDayStart120},
            value_day180 = #{updated.valueDay180},
            month_day_start180 = #{updated.monthDayStart180},
            value_day240 = #{updated.valueDay240},
            month_day_start240 = #{updated.monthDayStart240},
            value_day360 = #{updated.valueDay360},
            month_day_start360 = #{updated.monthDayStart360},
            value_day540 = #{updated.valueDay540},
            month_day_start540 = #{updated.monthDayStart540},
            value_day720 = #{updated.valueDay720},
            month_day_start720 = #{updated.monthDayStart720},
            value_day1440 = #{updated.valueDay1440},
            month_day_start1440 = #{updated.monthDayStart1440},
            remark = #{updated.remark},
            version = #{updated.version},
            latest = #{updated.latest},
            update_time = #{updated.updateTime},
            updater = #{updated.updater}

        where id=#{updated.id}
        </foreach>
    </update>

    <select id="listLatestDataByStationIdAndDataType" resultMap="BaseResultMap">
        select *
        from hydrologic_station_rain_period_minute
        where station_id = #{stationId} and data_type=#{dataType} and latest = 1 and deleted=0;
    </select>
</mapper>