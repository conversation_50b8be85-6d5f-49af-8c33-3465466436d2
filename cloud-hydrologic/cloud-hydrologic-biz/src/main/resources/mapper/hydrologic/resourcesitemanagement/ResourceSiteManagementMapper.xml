<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.resourcesitemanagement.ResourceSiteManagementMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->


    <sql id="Base_Column_List">
        id,
        power_station_name,
        power_station_type,
        power_code,
        country,
        province,
        city,
        county,
        address,
        design_stage,
        upload_file,
        file_url,
        compliance_regulations,
        develop_way,
        longitude,
        latitude,
        total_capacity,
        rated_head,
        full_shipping_hours,
        distance_to_height_ratio,
        static_investment,
        water_source_conditions,
        significant_sensitive_factors,
        design_unit,
        lnvestment_unit,
        data_sources,
        remarks,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap" type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.resourcesitemanagement.ResourceSiteManagementDO">
        <result column="id" property="id"/>
        <result column="power_station_name" property="powerStationName"/>
        <result column="power_station_type" property="powerStationType"/>
        <result column="power_code" property="powerCode"/>
        <result column="country" property="country"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="county" property="county"/>
        <result column="address" property="address"/>
        <result column="design_stage" property="designStage"/>
        <result column="upload_file" property="uploadFile"/>
        <result column="file_url" property="fileUrl"/>
        <result column="compliance_regulations" property="complianceRegulations"/>
        <result column="develop_way" property="developWay"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="total_capacity" property="totalCapacity"/>
        <result column="rated_head" property="ratedHead"/>
        <result column="full_shipping_hours" property="fullShippingHours"/>
        <result column="distance_to_height_ratio" property="distanceToHeightRatio"/>
        <result column="static_investment" property="staticInvestment"/>
        <result column="water_source_conditions" property="waterSourceConditions"/>
        <result column="significant_sensitive_factors" property="significantSensitiveFactors"/>
        <result column="design_unit" property="designUnit"/>
        <result column="lnvestment_unit" property="lnvestmentUnit"/>
        <result column="data_sources" property="dataSources"/>
        <result column="remarks" property="remarks"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="selectListByStationNames" resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.resourcesitemanagement.ResourceSiteManagementDO">
        select * from hydrologic_resource_site_management where power_station_name in
        <foreach collection="stationNames" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted=0
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_resource_site_management(
        id,
        power_station_name,
        power_station_type,
        power_code,
        country,
        province,
        city,
        county,
        address,
        design_stage,
        upload_file,
        file_url,
        compliance_regulations,
        develop_way,
        longitude,
        latitude,
        total_capacity,
        full_shipping_hours,
        rated_head,
        distance_to_height_ratio,
        static_investment,
        water_source_conditions,
        significant_sensitive_factors,
        design_unit,
        lnvestment_unit,
        data_sources,
        remarks,
        create_time,
        update_time,
        creator,
        updater
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.powerStationName},
            #{element.powerStationType},
            #{element.powerCode},
            #{element.country},
            #{element.province},
            #{element.city},
            #{element.county},
            #{element.address},
            #{element.designStage},
            #{element.uploadFile},
            #{element.fileUrl},
            #{element.complianceRegulations},
            #{element.developWay},
            #{element.longitude},
            #{element.latitude},
            #{element.totalCapacity},
            #{element.fullShippingHours},
            #{element.ratedHead},
            #{element.distanceToHeightRatio},
            #{element.staticInvestment},
            #{element.waterSourceConditions},
            #{element.significantSensitiveFactors},
            #{element.designUnit},
            #{element.lnvestmentUnit},
            #{element.dataSources},
            #{element.remarks},
            now(),
            now(),
            0,
            0
            )
        </foreach>
    </insert>

    <delete id="deleteTemporaryData">
        delete from hydrologic_resource_site_management where id &lt; 1;
    </delete>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select * from hydrologic_resource_site_management where id &lt; 1 and creator='1';
    </select>


    <update id="updateCreator">
        update hydrologic_resource_site_management set creator='1' where id &lt; 1 and creator='0';
    </update>


    <select id="selectResourceSitePage"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.resourcesitemanagement.ResourceSiteManagementDO">
        SELECT *
        FROM hydrologic_resource_site_management
        <where>
            deleted = 0
            <if test="reqVO.powerStationName != null and reqVO.powerStationName != ''">
                AND power_station_name LIKE CONCAT('%', #{reqVO.powerStationName}, '%')
            </if>
            <if test="reqVO.powerStationType != null">
                AND power_station_type = #{reqVO.powerStationType}
            </if>
            <if test="reqVO.country != null and reqVO.country != ''">
                AND country = #{reqVO.country}
            </if>
            <if test="reqVO.province != null and reqVO.province != ''">
                AND province = #{reqVO.province}
            </if>
            <if test="reqVO.city != null and reqVO.city != ''">
                AND city = #{reqVO.city}
            </if>
            <if test="reqVO.county != null and reqVO.county != ''">
                AND county = #{reqVO.county}
            </if>
            <if test="reqVO.designStage != null and reqVO.designStage != ''">
                <choose>
                    <when test="reqVO.designStage == 'QT'">
                        AND design_stage NOT IN ('已建', '在建', '可研', '初设', '规划选点')
                    </when>
                    <otherwise>
                        AND design_stage = #{reqVO.designStage}
                    </otherwise>
                </choose>
            </if>
            <if test="reqVO.developWay != null and reqVO.developWay != ''">
                AND develop_way = #{reqVO.developWay}
            </if>
            <if test="reqVO.complianceRegulations != null and reqVO.complianceRegulations != ''">
                AND compliance_regulations = #{reqVO.complianceRegulations}
            </if>
            <!-- 总装机容量区间查询 -->
            <if test="reqVO.lowerLimitValue != null">
                AND CAST(total_capacity AS DECIMAL) >= #{reqVO.lowerLimitValue}
            </if>
            <if test="reqVO.upperLimitValue != null">
                AND CAST(total_capacity AS DECIMAL) &lt;= #{reqVO.upperLimitValue}
            </if>

            <!-- 连续满发小时数 -->
            <if test="reqVO.fullShippingHours != null and reqVO.fullShippingHours != ''">
                AND full_shipping_hours = #{reqVO.fullShippingHours}
            </if>

            <if test="reqVO.bizIdSet != null and reqVO.bizIdSet.size() > 0">
                AND id IN
                <foreach collection="reqVO.bizIdSet" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY id DESC
        limit #{offset}, #{limit}
    </select>


    <!-- 添加计数查询 -->
    <select id="selectResourceSiteTotal" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM hydrologic_resource_site_management
        <where>
            deleted = 0
            <if test="reqVO.powerStationName != null and reqVO.powerStationName != ''">
                AND power_station_name LIKE CONCAT('%', #{reqVO.powerStationName}, '%')
            </if>
            <if test="reqVO.powerStationType != null">
                AND power_station_type = #{reqVO.powerStationType}
            </if>
            <if test="reqVO.country != null and reqVO.country != ''">
                AND country = #{reqVO.country}
            </if>
            <if test="reqVO.province != null and reqVO.province != ''">
                AND province = #{reqVO.province}
            </if>
            <if test="reqVO.city != null and reqVO.city != ''">
                AND city = #{reqVO.city}
            </if>
            <if test="reqVO.county != null and reqVO.county != ''">
                AND county = #{reqVO.county}
            </if>
            <if test="reqVO.designStage != null and reqVO.designStage != ''">
                <choose>
                    <when test="reqVO.designStage == 'QT'">
                        AND design_stage NOT IN ('已建', '在建', '可研', '初设', '规划选点')
                    </when>
                    <otherwise>
                        AND design_stage = #{reqVO.designStage}
                    </otherwise>
                </choose>
            </if>
            <if test="reqVO.developWay != null and reqVO.developWay != ''">
                AND develop_way = #{reqVO.developWay}
            </if>
            <if test="reqVO.complianceRegulations != null and reqVO.complianceRegulations != ''">
                AND compliance_regulations = #{reqVO.complianceRegulations}
            </if>
            <!-- 总装机容量区间查询 -->
            <if test="reqVO.lowerLimit != null">
                AND CAST(total_capacity AS DECIMAL) >= #{reqVO.lowerLimit}
            </if>
            <if test="reqVO.upperLimit != null">
                AND CAST(total_capacity AS DECIMAL) &lt;= #{reqVO.upperLimit}
            </if>
            <!-- 连续满发小时数 -->
            <if test="reqVO.fullShippingHours != null and reqVO.fullShippingHours != ''">
                AND full_shipping_hours = #{reqVO.fullShippingHours}
            </if>

            <if test="reqVO.bizIdSet != null and reqVO.bizIdSet.size() > 0">
                AND id IN
                <foreach collection="reqVO.bizIdSet" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>


</mapper>