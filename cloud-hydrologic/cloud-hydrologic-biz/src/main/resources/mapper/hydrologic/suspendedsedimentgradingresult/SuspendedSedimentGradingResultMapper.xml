<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.suspendedsedimentgradingresult.SuspendedSedimentGradingResultMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <update id="updateHistoryVersion">
        update hydrologic_suspended_sediment_grading_result
        set latest = 0
        where station_id = #{stationId}
          and station_type = #{stationType}
          and data_type = #{dataType}
          and latest != 0
    </update>

    <insert id="batchInsert">
        INSERT INTO hydrologic_suspended_sediment_grading_result (
        station_id,
        station_type,
        data_type,
        year,
        month,
        day,
        value008,
        value016,
        value031,
        value062,
        value125,
        value250,
        value500,
        value1000,
        value2000,
        value4000,
        value8000,
        value1600,
        value3200,
        median_diameter,
        mean_diameter,
        max_diameter,
        sample_method,
        analysis_method,
        remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.stationId},
            #{item.stationType},
            #{item.dataType},
            #{item.year},
            #{item.month},
            #{item.day},
            #{item.value008},
            #{item.value016},
            #{item.value031},
            #{item.value062},
            #{item.value125},
            #{item.value250},
            #{item.value500},
            #{item.value1000},
            #{item.value2000},
            #{item.value4000},
            #{item.value8000},
            #{item.value1600},
            #{item.value3200},
            #{item.medianDiameter},
            #{item.meanDiameter},
            #{item.maxDiameter},
            #{item.sampleMethod},
            #{item.analysisMethod},
            #{item.remark},
            #{item.version},
            #{item.latest},
            now(),
            now(),
            #{item.creator},
            #{item.updater},
            0
            )
        </foreach>
    </insert>
</mapper>