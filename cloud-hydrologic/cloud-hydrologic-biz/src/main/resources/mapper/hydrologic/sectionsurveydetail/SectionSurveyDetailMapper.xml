<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.sectionsurveydetail.SectionSurveyDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->


    <select id="listYear" resultType="java.lang.Integer">
        select year
        from hydrologic_section_survey_detail
        where deleted = 0
          and station_id = #{stationId}
          and data_type = #{dataType}
          and latest = 1
        group by year
        order by year asc
    </select>

    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        period_type,
        vertical_no,
        start_distance,
        riverbed_elevation,
        remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>



    <resultMap id="BaseResultMap" type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.sectionsurveydetail.SectionSurveyDetailDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="period_type" property="periodType"/>
        <result column="vertical_no" property="verticalNo"/>
        <result column="start_distance" property="startDistance"/>
        <result column="riverbed_elevation" property="riverbedElevation"/>
        <result column="remark" property="remark"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 获取大断面垂线数据列表（带版本控制）-->
    <select id="getDetailListWithVersion" resultMap="BaseResultMap">
        SELECT
            d.*
        FROM
            hydrologic_section_survey_detail d
        WHERE
            d.station_id = #{stationId}
          AND (#{year} IS NULL OR d.year = #{year})
          AND d.station_type = #{stationType}
          AND d.data_type = #{dataType}
          AND d.period_type = #{periodType}
          AND (deleted = 0 OR deleted IS NULL)
          AND (
            (#{version} IS NOT NULL AND d.version = #{version})
                OR
            (#{version} IS NULL AND d.latest = #{latest})
            )
        ORDER BY
            d.vertical_no ASC
    </select>
</mapper>