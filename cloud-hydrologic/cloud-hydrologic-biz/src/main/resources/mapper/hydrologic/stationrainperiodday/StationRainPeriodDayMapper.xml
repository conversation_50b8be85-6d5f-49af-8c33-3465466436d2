<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.stationrainperiodday.StationRainPeriodDayMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        value_day1,
        month_day_start1,
        value_day3,
        month_day_start3,
        value_day7,
        month_day_start7,
        value_day15,
        month_day_start15,
        value_day30,
        month_day_start30,
        remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodday.StationRainPeriodDayDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="value_day1" property="valueDay1"/>
        <result column="month_day_start1" property="monthDayStart1"/>
        <result column="value_day3" property="valueDay3"/>
        <result column="month_day_start3" property="monthDayStart3"/>
        <result column="value_day7" property="valueDay7"/>
        <result column="month_day_start7" property="monthDayStart7"/>
        <result column="value_day15" property="valueDay15"/>
        <result column="month_day_start15" property="monthDayStart15"/>
        <result column="value_day30" property="valueDay30"/>
        <result column="month_day_start30" property="monthDayStart30"/>
        <result column="remark" property="remark"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodday.StationRainPeriodDayDO">
        select *
        from hydrologic_station_rain_period_day
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted = 0
        and latest = 1
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_station_rain_period_day(id,
                                                       station_id,
                                                       station_type,
                                                       data_type,
                                                       `year`,
                                                       value_day1,
                                                       month_day_start1,
                                                       value_day3,
                                                       month_day_start3,
                                                       value_day7,
                                                       month_day_start7,
                                                       value_day15,
                                                       month_day_start15,
                                                       value_day30,
                                                       month_day_start30,
                                                       remark,
                                                       version,
                                                       latest,
                                                       create_time,
                                                       update_time,
                                                       creator,
                                                       updater,
                                                       deleted)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.stationId},
             #{element.stationType},
             #{element.dataType},
             #{element.year},
             #{element.valueDay1},
             #{element.monthDayStart1},
             #{element.valueDay3},
             #{element.monthDayStart3},
             #{element.valueDay7},
             #{element.monthDayStart7},
             #{element.valueDay15},
             #{element.monthDayStart15},
             #{element.valueDay30},
             #{element.monthDayStart30},
             #{element.remark},
             #{element.version},
             #{element.latest},
             now(),
             now(),
             0,
             0,
             1)
        </foreach>
    </insert>

    <select id="listYear" resultType="java.lang.Integer">
        select year
        from hydrologic_station_rain_period_day
        where deleted = 0
        and station_id = #{stationId}
        and data_type = #{dataType}
        and latest = 1
        group by year
        order by year asc
    </select>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select *
        from hydrologic_station_rain_period_day
        where id &lt; 1 and station_id=#{stationId}
          and creator = '1';
    </select>

    <select id="selectLatestData" resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.stationrainperiodday.StationRainPeriodDayDO">
        select *
        from hydrologic_station_rain_period_day
        where station_id = #{stationId} and data_type = #{dataType} and latest = 1 and deleted=0
    </select>

    <insert id="batchInsert">
        INSERT INTO hydrologic_station_rain_period_day(station_id,
                                                       station_type,
                                                        data_type,
                                                       `year`,
                                                       value_day1,
                                                       month_day_start1,
                                                       value_day3,
                                                       month_day_start3,
                                                       value_day7,
                                                       month_day_start7,
                                                       value_day15,
                                                       month_day_start15,
                                                       value_day30,
                                                       month_day_start30,
                                                       remark,
                                                       version,
                                                       latest,
                                                       create_time,
                                                       update_time,
                                                       creator,
                                                       updater,
                                                       deleted)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.stationId},
             #{element.stationType},
             #{element.dataType},
             #{element.year},
             #{element.valueDay1},
             #{element.monthDayStart1},
             #{element.valueDay3},
             #{element.monthDayStart3},
             #{element.valueDay7},
             #{element.monthDayStart7},
             #{element.valueDay15},
             #{element.monthDayStart15},
             #{element.valueDay30},
             #{element.monthDayStart30},
             #{element.remark},
             #{element.version},
             #{element.latest},
             now(),
             now(),
             #{element.creator},
             #{element.updater},
             0)
        </foreach>
    </insert>


    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
            update hydrologic_station_rain_period_day set

            value_day1 = #{updated.valueDay1},
            month_day_start1 = #{updated.monthDayStart1},
            value_day3 = #{updated.valueDay3},
            month_day_start3 = #{updated.monthDayStart3},
            value_day7 = #{updated.valueDay7},
            month_day_start7 = #{updated.monthDayStart7},
            value_day15 = #{updated.valueDay15},
            month_day_start15 = #{updated.monthDayStart15},
            value_day30 = #{updated.valueDay30},
            month_day_start30 = #{updated.monthDayStart30},
            remark = #{updated.remark},
            version = #{updated.version},
            latest = #{updated.latest},
            update_time = #{updated.updateTime},
            updater = #{updated.updater}

            where id = #{updated.id}
        </foreach>
    </update>
</mapper>