<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.cloud.institute.hydrologic.dal.mysql.waterlevelguarantee.WaterLevelGuaranteeMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <sql id="Base_Column_List">
        id,
        station_id,
        station_type,
        data_type,
        `year`,
        max_value,
        day15,
        day30,
        day90,
        day180,
        day270,
        min_value,
        remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted
    </sql>
    <resultMap id="BaseResultMap"
               type="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.waterlevelguarantee.WaterLevelGuaranteeDO">
        <result column="id" property="id"/>
        <result column="station_id" property="stationId"/>
        <result column="station_type" property="stationType"/>
        <result column="data_type" property="dataType"/>
        <result column="year" property="year"/>
        <result column="max_value" property="maxValue"/>
        <result column="day15" property="day15"/>
        <result column="day30" property="day30"/>
        <result column="day90" property="day90"/>
        <result column="day180" property="day180"/>
        <result column="day270" property="day270"/>
        <result column="min_value" property="minValue"/>
        <result column="remark" property="remark"/>
        <result column="version" property="version"/>
        <result column="latest" property="latest"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <select id="listByStationIds"
            resultType="cn.powerchina.bjy.cloud.institute.hydrologic.dal.dataobject.waterlevelguarantee.WaterLevelGuaranteeDO">
        select *
        from hydrologic_water_level_guarantee
        where station_id in
        <foreach collection="stationIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and deleted = 0
        and latest = 1
    </select>


    <insert id="insertList">
        INSERT INTO hydrologic_water_level_guarantee(id,
        station_id,
        station_type,
        data_type,
        `year`,
        max_value,
        day15,
        day30,
        day90,
        day180,
        day270,
        min_value,
        remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.maxValue},
            #{element.day15},
            #{element.day30},
            #{element.day90},
            #{element.day180},
            #{element.day270},
            #{element.minValue},
            #{element.remark},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            0,
            0,
            1)
        </foreach>
    </insert>

    <select id="listYear" resultType="java.lang.Integer">
        select year
        from hydrologic_water_level_guarantee
        where deleted = 0
        and station_id = #{stationId}
        and data_type = #{dataType}
        and latest = 1
        group by year
        order by year asc
    </select>

    <select id="selectListTemporaryData" resultMap="BaseResultMap">
        select *
        from hydrologic_water_level_guarantee
        where id &lt; 1 and station_id=#{stationId}
        and creator = '1';
    </select>

    <select id="getLatestVersion" resultType="java.lang.Integer">
        select max(version)
        from hydrologic_water_level_guarantee
        where deleted = 0
        and latest = 1
        and station_id = #{stationId}
    </select>

    <insert id="batchInsert">
        INSERT INTO hydrologic_water_level_guarantee(
        station_id,
        station_type,
        data_type,
        `year`,
        max_value,
        day15,
        day30,
        day90,
        day180,
        day270,
        min_value,
        remark,
        version,
        latest,
        create_time,
        update_time,
        creator,
        updater,
        deleted)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.stationId},
            #{element.stationType},
            #{element.dataType},
            #{element.year},
            #{element.maxValue},
            #{element.day15},
            #{element.day30},
            #{element.day90},
            #{element.day180},
            #{element.day270},
            #{element.minValue},
            #{element.remark},
            #{element.version},
            #{element.latest},
            now(),
            now(),
            #{element.creator},
            #{element.updater},
            0)
        </foreach>
    </insert>


    <update id="updateBatchById">
        <foreach collection="list" index="index" item="updated" separator=";">
            update hydrologic_water_level_guarantee set

            max_value = #{updated.maxValue},
            day15 = #{updated.day15},
            day30 = #{updated.day30},
            day90 = #{updated.day90},
            day180 = #{updated.day180},
            day270 = #{updated.day270},
            min_value = #{updated.minValue},
            remark = #{updated.remark},
            version = #{updated.version},
            latest = #{updated.latest},
            update_time = #{updated.updateTime},
            updater = #{updated.updater}

            where id = #{updated.id}
        </foreach>
    </update>
</mapper>