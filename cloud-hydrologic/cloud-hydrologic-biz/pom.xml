<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.powerchina.bjy</groupId>
        <artifactId>cloud-hydrologic</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>cloud-hydrologic-biz</artifactId>
    <packaging>jar</packaging>
    <properties>
        <!--framework版本-->
        <revision.system>202051201-SNAPSHOT</revision.system>
    </properties>
    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-env</artifactId>
            <version>${revision.framework}</version>
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-system-api</artifactId>
            <version>${revision.system}</version>
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-infra-api</artifactId>
            <version>${revision.infra}</version>
        </dependency>

        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-hydrologic-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-biz-data-permission</artifactId>
            <version>${revision.framework}</version>
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-biz-tenant</artifactId>
            <version>${revision.framework}</version>
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-biz-ip</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-security</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-mybatis</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-redis</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-rpc</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>

        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-job</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-excel</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-monitor</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-extra</artifactId> <!-- 邮件 -->
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-system-api</artifactId>
            <version>202051201-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
<!--            <plugin>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--                <version>${spring.boot.version}</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>repackage</goal> &lt;!&ndash; 将引入的 jar 打入其中 &ndash;&gt;-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
            <!--打包时候不要压缩模板文件，排除扩展名为下xlsx的文件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://10.216.3.8:8083/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://10.216.3.8:8083/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>