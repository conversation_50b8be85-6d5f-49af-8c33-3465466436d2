package cn.powerchina.bjy.cloud.institute.essz.service.temp;

import cn.powerchina.bjy.cloud.institute.essz.controller.admin.keydatadisplay.vo.CalculationCapacityReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.keydatadisplay.vo.CalculationCapacityRespVO;

import java.util.List;

/**
 * 临时数据
 */
public interface TempDataService {


    /**
     * 查询检修安排数据
     * @return
     */
    public List<CalculationCapacityRespVO> loadAndInstalledCapacityEcharts(CalculationCapacityReqVO reqVO);

}
