package cn.powerchina.bjy.cloud.institute.essz.service.stationschedulehour;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.QueryWrapperX;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.stationschedulehour.vo.StationScheduleHourPageReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.stationschedulehour.vo.StationScheduleHourSaveReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.stationschedulehour.vo.StationScheduleHourSequenceReqVO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.station.EssStationDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.stationschedulehour.StationScheduleHourDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.station.EssStationMapper;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.stationschedulehour.StationScheduleHourMapper;
import cn.powerchina.bjy.cloud.institute.essz.enums.StationScaleCodeEnum;
import cn.powerchina.bjy.cloud.institute.essz.service.project.EssProjectService;
import cn.powerchina.bjy.cloud.institute.essz.service.station.EssStationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.essz.constants.ErrorCodeConstants.ESS_STATION_SCHEDULE_HOUR_NOT_EXISTS;
import static cn.powerchina.bjy.cloud.institute.essz.enums.StationScaleCodeEnum.SCALE_LOAD_MONTH;
import static cn.powerchina.bjy.cloud.institute.essz.enums.StationScaleCodeEnum.SCALE_LOAD_WEEK;

/**
 * 电源-小时-出力计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class StationScheduleHourServiceImpl implements StationScheduleHourService {

    private static final Logger log = LoggerFactory.getLogger(StationScheduleHourServiceImpl.class);
    @Resource
    private StationScheduleHourMapper stationScheduleHourMapper;
    @Resource
    private EssStationMapper essStationMapper;
    @Resource
    private EssProjectService essProjectService;
    @Resource
    private EssStationService essStationService;


    @Override
    public Long createStationScheduleHour(StationScheduleHourSaveReqVO createReqVO) {
        // 插入
        StationScheduleHourDO stationScheduleHour = BeanUtils.toBean(createReqVO, StationScheduleHourDO.class);
        stationScheduleHourMapper.insert(stationScheduleHour);
        // 返回
        return stationScheduleHour.getId();
    }

    @Override
    public void updateStationScheduleHour(StationScheduleHourSaveReqVO[] updateReqVOArray) {
        for (StationScheduleHourSaveReqVO hourSaveReqVO : updateReqVOArray) {
            StationScheduleHourDO updateObj = new StationScheduleHourDO();
            updateObj.setId(hourSaveReqVO.getId());
            updateObj.setFactor(hourSaveReqVO.getFactor());

            // 更新
            stationScheduleHourMapper.updateById(updateObj);
        }
    }

    @Override
    public void deleteStationScheduleHour(Long id) {
        // 校验存在
        validateStationScheduleHourExists(id);
        // 删除
        stationScheduleHourMapper.deleteById(id);
    }

    private void validateStationScheduleHourExists(Long id) {
        if (stationScheduleHourMapper.selectById(id) == null) {
            throw exception(ESS_STATION_SCHEDULE_HOUR_NOT_EXISTS);
        }
    }

    @Override
    public StationScheduleHourDO getStationScheduleHour(Long id) {
        return stationScheduleHourMapper.selectById(id);
    }

    @Override
    public PageResult<StationScheduleHourDO> getStationScheduleHourPage(StationScheduleHourPageReqVO pageReqVO) {
        if (StrUtil.isNotEmpty(pageReqVO.getTimeScale()) && pageReqVO.getStationIds() != null) {
            // 月尺度
            if (StationScaleCodeEnum.SCALE_MONTH.getCode().equals(pageReqVO.getTimeScale())) {
                stationScheduleHourMapper.selectPage(pageReqVO);
            }
        }
        return stationScheduleHourMapper.selectPage(pageReqVO);
    }

    @Override
    public Map<String, Object> getStationScheduleHourMap(StationScheduleHourSequenceReqVO pageReqVO) {
        //小时集合
        List<StationScheduleHourDO> stationScheduleHourList = getStationScheduleHourDOS(pageReqVO);
        //无数据时
        if (CollectionUtil.isEmpty(stationScheduleHourList)) {
            return null;
        }
        //按时间点分组  每组为一行数据
        Map<String, List<StationScheduleHourDO>> collect = null;
        if (SCALE_LOAD_MONTH.getCode().equals(pageReqVO.getTimeScale())) {
            collect = stationScheduleHourList.stream().collect(Collectors.groupingBy(o -> o.getMonth().toString()));
        } else if (SCALE_LOAD_WEEK.getCode().equals(pageReqVO.getTimeScale())) {
            collect = stationScheduleHourList.stream().collect(Collectors.groupingBy(o -> o.getWeek().toString()));
        } else {
            collect = stationScheduleHourList.stream().collect(Collectors.groupingBy(StationScheduleHourDO::getOutputDate));
        }

        long total = 8760;
        if (StationScaleCodeEnum.SCALE_MONTH.getCode().equals(pageReqVO.getTimeScale()) && ArrayUtils.isNotEmpty(pageReqVO.getMonths())) {
            total = collect.size();
            collect = stationScheduleHourList.stream().filter(item -> item.getMonth().equals(pageReqVO.getPageNo() + pageReqVO.getMonths()[0] - 1)).collect(Collectors.groupingBy(StationScheduleHourDO::getOutputDate));
        } else if (StationScaleCodeEnum.SCALE_WEEK.getCode().equals(pageReqVO.getTimeScale()) && ArrayUtils.isNotEmpty(pageReqVO.getWeeks())) {
            total = collect.size();
            collect = stationScheduleHourList.stream().filter(item -> item.getWeek().equals(pageReqVO.getPageNo() + pageReqVO.getWeeks()[0] - 1)).collect(Collectors.groupingBy(StationScheduleHourDO::getOutputDate));
        }

        //通道列表 (多选)
        LambdaQueryWrapper<EssStationDO> queryWrapper = new LambdaQueryWrapperX<EssStationDO>().inIfPresent(EssStationDO::getId, pageReqVO.getStationIds()).orderByAsc(EssStationDO::getServiceYear).orderByAsc(EssStationDO::getId);
        List<EssStationDO> stationDOS = essStationMapper.selectList(queryWrapper);
        List<Map<String, String>> columns = new ArrayList<>();
        for (EssStationDO stationDO : stationDOS) {
            //每个通道都装入columns作为表头
            Map<String, String> column = new HashMap<>();
            column.put("key", String.valueOf(stationDO.getId()));
            column.put("code", "code" + stationDO.getId());
            column.put("name", stationDO.getName());
            columns.add(column);
        }

        //组装返回结果的每行数据
        List<Map<String, Object>> rows = new ArrayList<>();
        for (String s : collect.keySet()) {
            Map<String, Object> row = new HashMap<>();
            row.put("levelDate", s);
            for (StationScheduleHourDO stationScheduleHourDO : collect.get(s)) {
                row.put("id" + stationScheduleHourDO.getStationId(), stationScheduleHourDO.getId());
                row.put(String.valueOf(stationScheduleHourDO.getStationId()), stationScheduleHourDO.getFactor().setScale(3, RoundingMode.HALF_UP));
            }
            rows.add(row);
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //时间排序
        rows = rows.stream().sorted(Comparator.comparingLong(map -> {
            try {
                if (SCALE_LOAD_MONTH.getCode().equals(pageReqVO.getTimeScale()) || SCALE_LOAD_WEEK.getCode().equals(pageReqVO.getTimeScale())) {
                    return Long.parseLong(map.get("levelDate").toString());
                } else {
                    return format.parse(map.get("levelDate").toString()).getTime();
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        })).toList();

        //分页
        int toIndex = pageReqVO.getPageNo() * 24;
        if (SCALE_LOAD_WEEK.getCode().equals(pageReqVO.getTimeScale())) {
            total = 52;
            rows = rows.subList(0, 52);
        } else if (SCALE_LOAD_MONTH.getCode().equals(pageReqVO.getTimeScale())) {
            total = 12;
            rows = rows.subList(0, 12);
        } else if (StationScaleCodeEnum.SCALE_LOAD_DAY.getCode().equals(pageReqVO.getTimeScale()) || StationScaleCodeEnum.SCALE_DAY.getCode().equals(pageReqVO.getTimeScale())) {
            total = rows.size();
            rows = rows.subList(toIndex - 24, toIndex);
        }

        //封装结果
        Map<String, Object> map = new HashMap<>();
        map.put("total", total);
        map.put("data", rows);
        map.put("columns", columns);
        return map;
    }
   /* {



        Map<String,Object> map = new HashMap<>();
        if (StrUtil.isNotEmpty(pageReqVO.getTimeScale())
            && pageReqVO.getStationId() != null) {
            // 查询电站
            List<EssStationDO> stationDOList =  essStationMapper.selectList(new LambdaQueryWrapperX<EssStationDO>()
                    .inIfPresent(EssStationDO::getId, Arrays.stream(pageReqVO.getStationId()).toList()));
            Map[] mapArray = new HashMap[stationDOList.size()];
            for (int i=0; i < stationDOList.size(); i++) {
                Map<String,Object> tempMap =  new HashMap<>();
                tempMap.put("key",stationDOList.get(i).getId());
                tempMap.put("name",stationDOList.get(i).getName());
                tempMap.put("code","code"+stationDOList.get(i).getId());

                mapArray[i] = tempMap;
            }

            // 结果集
            List<StationScheduleHourDO> list = null;
            // 数据总条数
            long total = 8760L;
            // 月份为空，默认查询1月份的数据（条件为空，时、周默认都属于1月）
            if (pageReqVO.getMonth() == null) {
                pageReqVO.setMonth(1);
                // 月尺度-使用分页参数查询来模拟分页查询
                if (StationScaleCodeEnum.SCALE_MONTH.getCode().equals(pageReqVO.getTimeScale())
                        && pageReqVO.getPageNo() > 1) {
                    pageReqVO.setMonth(pageReqVO.getPageNo());
                }
            }
            // 时、日尺度-查询条件为空，默认查询1/1的数据
            if ((StationScaleCodeEnum.SCALE_HOUR.getCode().equals(pageReqVO.getTimeScale())
                    || StationScaleCodeEnum.SCALE_DAY.getCode().equals(pageReqVO.getTimeScale()))
                    && pageReqVO.getDay() == null) {
                pageReqVO.setDay(1);
            }

            // 周尺度-查询条件为空，默认查询第一周的数据
            if (StationScaleCodeEnum.SCALE_WEEK.getCode().equals(pageReqVO.getTimeScale())
                    && pageReqVO.getWeek() == null) {
                pageReqVO.setWeek(1);
                //使用分页参数查询来模拟分页查询
                if (pageReqVO.getPageNo() > 1) {
                    pageReqVO.setWeek(pageReqVO.getPageNo());
                }
            }

            // 时尺度、日尺度、周尺度、月尺度
            // 典型日负荷特性
            if (StationScaleCodeEnum.SCALE_HOUR.getCode().equals(pageReqVO.getTimeScale())
                || StationScaleCodeEnum.SCALE_DAY.getCode().equals(pageReqVO.getTimeScale())
                || StationScaleCodeEnum.SCALE_WEEK.getCode().equals(pageReqVO.getTimeScale())
                || StationScaleCodeEnum.SCALE_MONTH.getCode().equals(pageReqVO.getTimeScale())
                || StationScaleCodeEnum.SCALE_LOAD_DAY.getCode().equals(pageReqVO.getTimeScale())) {
                // 日尺度-查询条件不为空
                if (StationScaleCodeEnum.SCALE_DAY.getCode().equals(pageReqVO.getTimeScale())
                        && pageReqVO.getDays() != null) {
                    // 查询设计水平年
                    EssStationDO stationDO = essStationMapper.selectById(pageReqVO.getStationIds()[0]);
                    if (ObjectUtil.isNull(stationDO)) {
                        // 电源为空，直接返回
                        return map;
                    }
                    EssProjectDO projectDO = essProjectService.getProject(stationDO.getProjectId());
                    if (ObjectUtil.isNull(projectDO)) {
                        // 项目为空，直接返回
                        return map;
                    }
                    pageReqVO.setMonth(null);
                    pageReqVO.setWeek(null);
                    pageReqVO.setDay(null);
                    String[] outputDate = new String[2];
                    outputDate[0] = projectDO.getLevelYear().concat("-").concat(pageReqVO.getDays()[0]).concat(" 01:00:00");
                    outputDate[1] = projectDO.getLevelYear().concat("-").concat(pageReqVO.getDays()[1]).concat(" 24:00:00");
                    pageReqVO.setOutputDate(outputDate);
                    list = new ArrayList<>(pageReqVO.getStationIds().length*24);
                    for (Long stationId : pageReqVO.getStationIds()){
                        pageReqVO.setStationId(stationId);
                        PageResult<StationScheduleHourDO> pageResult = stationScheduleHourMapper.selectPage(pageReqVO);
                        list.addAll(pageResult.getList());
                    }
                    long betweenDay = DateUtil.betweenDay(DateUtil.parse(projectDO.getLevelYear().concat("-").concat(pageReqVO.getDays()[1]),"yyyy-MM-dd"),
                            DateUtil.parse(projectDO.getLevelYear().concat("-").concat(pageReqVO.getDays()[0]),"yyyy-MM-dd"),
                                    true);
                    total = (betweenDay+1)*24;
                } else {
                    list = stationScheduleHourMapper.selectList(new LambdaQueryWrapperX<StationScheduleHourDO>()
                            .inIfPresent(StationScheduleHourDO::getStationId, Arrays.stream(pageReqVO.getStationIds()).toList())
                            .eqIfPresent(StationScheduleHourDO::getMonth,pageReqVO.getMonth())
                            .eqIfPresent(StationScheduleHourDO::getWeek,pageReqVO.getWeek())
                            .eqIfPresent(StationScheduleHourDO::getDay,pageReqVO.getDay())
                            .betweenIfPresent(StationScheduleHourDO::getHour,pageReqVO.getHours())
                            .orderByAsc(StationScheduleHourDO::getStationId,StationScheduleHourDO::getOutputDate)
                    );
                }
            }

            // 年负荷特性
            if (StationScaleCodeEnum.SCALE_LOAD_YEAR.getCode().equals(pageReqVO.getTimeScale())) {
                list = new ArrayList<>(pageReqVO.getStationIds().length*12);
                for (Long stationId : pageReqVO.getStationIds()){
                    List<StationScheduleHourDO> tempList = stationScheduleHourMapper.selectListGroupByMonth(stationId);
                    list.addAll(tempList);
                }
            }

            //周负荷特性
            if (StationScaleCodeEnum.SCALE_LOAD_WEEK.getCode().equals(pageReqVO.getTimeScale())) {
                list = new ArrayList<>(pageReqVO.getStationIds().length*53);
                for (Long stationId : pageReqVO.getStationIds()){
                    List<StationScheduleHourDO> tempList = stationScheduleHourMapper.selectListGroupByWeek(stationId);
                    list.addAll(tempList);
                }
            }

            Map<String,  Map<String,Object>> dataMap = new HashMap<>();
            for (int i=0; i < list.size(); i++) {
                Map<String,Object> tempMap = dataMap.get(list.get(i).getOutputDate());
                if (tempMap == null) {
                    tempMap  = new HashMap<>();
                    tempMap.put("levelDate",list.get(i).getOutputDate());
                }
                tempMap.put("id"+String.valueOf(list.get(i).getStationId()),list.get(i).getId());
                tempMap.put(String.valueOf(list.get(i).getStationId()),list.get(i).getFactor());

                dataMap.put(list.get(i).getOutputDate(),tempMap);
            }
            Map<String,Map<String,Object>> storedMap = new TreeMap<>(dataMap);;
            map.put("data",storedMap.values());
            map.put("columns",mapArray);
            map.put("total",total);
        }
        return map;
    }*/

    public List<List<String>> getStationScheduleHourCurve(StationScheduleHourSequenceReqVO pageReqVO) {

        //pageNO有默认值  导致时间区间参数查询时  查询第一周或第一个月,在此设置为null  使其查全年数据
        pageReqVO.setPageNo(null);

        //小时集合
        List<StationScheduleHourDO> stationScheduleHourList = getStationScheduleHourDOS(pageReqVO);
        //无数据时
        if (CollectionUtil.isEmpty(stationScheduleHourList)) {
            return null;
        }
        //按通道分组
        Map<Long, List<StationScheduleHourDO>> collect = stationScheduleHourList.stream().collect(Collectors.groupingBy(StationScheduleHourDO::getStationId));

        //结果集
        List<List<String>> stationScheduleHourCurvesRespVO = new ArrayList<>();

        //组装时间
        Long[] longs = collect.keySet().toArray(new Long[0]);
        List<StationScheduleHourDO> stationScheduleHourDOS1 = collect.get(longs[0]);
        List<String> time = new ArrayList<>();
        time.add("时间");
        int week = 1;
        for (StationScheduleHourDO stationScheduleHourDO : stationScheduleHourDOS1) {
            //time.add(stationScheduleHourDO.getOutputDate());
            if (SCALE_LOAD_WEEK.getCode().equals(pageReqVO.getTimeScale()) ) {
                time.add("第" + week + "周");
                week++;
                continue;
            }

            if (SCALE_LOAD_MONTH.getCode().equals(pageReqVO.getTimeScale()) ) {
                time.add(week + "月");
                week++;
                continue;
            }

            time.add(stationScheduleHourDO.getOutputDate());
        }
        stationScheduleHourCurvesRespVO.add(time);

        //组装系数
        for (Long l : collect.keySet()) {
            EssStationDO station = essStationService.getStation(l);
            List<StationScheduleHourDO> stationScheduleHourDOS = collect.get(l);
            List<String> list = new ArrayList<>();
            list.add(station.getName());
            for (StationScheduleHourDO stationScheduleHourDO : stationScheduleHourDOS) {
                list.add(String.valueOf(stationScheduleHourDO.getFactor().setScale(3, RoundingMode.HALF_UP)));
            }
            stationScheduleHourCurvesRespVO.add(list);
        }

        return stationScheduleHourCurvesRespVO;
    }

    @Override
    public List<StationScheduleHourDO> getStationScheduleHourByStationIdsGroupByOutputDate(Collection<Long> stationIds) {
        return stationScheduleHourMapper.getStationScheduleHourByStationIdsGroupByOutputDate(stationIds);
    }

    @Override
    public List<StationScheduleHourDO> findByStationIds(Collection<Long> stationIds) {
        LambdaQueryWrapperX<StationScheduleHourDO> queryWrapperX = new LambdaQueryWrapperX<StationScheduleHourDO>().in(StationScheduleHourDO::getStationId, stationIds);
        return stationScheduleHourMapper.selectList(queryWrapperX);
    }
  /*  {
        // 查询电站
        List<EssStationDO> stationList =  essStationMapper.selectList(new LambdaQueryWrapperX<EssStationDO>()
                .inIfPresent(EssStationDO::getId, Arrays.stream(pageReqVO.getStationId()).toList()));
        if (ObjectUtil.isNull(stationList)) {
            return null;
        }
        String[][] resultArray = new String[stationList.size()+1][];
        // 设计水平年
        String levelYear = null;
        for (int i=0;i<stationList.size();i++) {
            List<StationScheduleHourDO> list = null;
            String hour = "出力过程";
            // 出力系数
            String factor = stationList.get(i).getName();
            // 时尺度、日尺度、周尺度、月尺度
            // 典型日负荷特性
            if (StationScaleCodeEnum.SCALE_HOUR.getCode().equals(pageReqVO.getTimeScale())
                    || StationScaleCodeEnum.SCALE_DAY.getCode().equals(pageReqVO.getTimeScale())
                    || StationScaleCodeEnum.SCALE_WEEK.getCode().equals(pageReqVO.getTimeScale())
                    || StationScaleCodeEnum.SCALE_MONTH.getCode().equals(pageReqVO.getTimeScale())
                    || StationScaleCodeEnum.SCALE_LOAD_DAY.getCode().equals(pageReqVO.getTimeScale())) {
                // 日尺度-查询条件不为空
                if (StationScaleCodeEnum.SCALE_DAY.getCode().equals(pageReqVO.getTimeScale())
                        && pageReqVO.getDays() != null) {
                    if (ObjectUtil.isNull(levelYear)) {
                        EssProjectDO projectDO = essProjectService.getProject(stationList.get(i).getProjectId());
                        if (ObjectUtil.isNull(projectDO)) {
                            // 项目为空，直接返回
                            return resultArray;
                        }
                        levelYear = projectDO.getLevelYear();
                    }
                    String[] outputDate = new String[2];
                    outputDate[0] = levelYear.concat("-").concat(pageReqVO.getDays()[0]).concat(" 01:00:00");
                    outputDate[1] = levelYear.concat("-").concat(pageReqVO.getDays()[1]).concat(" 24:00:00");
                    pageReqVO.setOutputDate(outputDate);
                    list = stationScheduleHourMapper.selectList(new LambdaQueryWrapperX<StationScheduleHourDO>()
                            .eqIfPresent(StationScheduleHourDO::getStationId, stationList.get(i).getId())
                            .betweenIfPresent(StationScheduleHourDO::getOutputDate, pageReqVO.getOutputDate())
                            .orderByAsc(StationScheduleHourDO::getOutputDate));
                } else {
                    list = stationScheduleHourMapper.selectList(new LambdaQueryWrapperX<StationScheduleHourDO>()
                            .eqIfPresent(StationScheduleHourDO::getStationId, stationList.get(i).getId())
                            .betweenIfPresent(StationScheduleHourDO::getOutputDate, pageReqVO.getOutputDate())
                            .eqIfPresent(StationScheduleHourDO::getMonth,pageReqVO.getMonth())
                            .eqIfPresent(StationScheduleHourDO::getWeek,pageReqVO.getWeek())
                            .eqIfPresent(StationScheduleHourDO::getDay,pageReqVO.getDay())
                            .betweenIfPresent(StationScheduleHourDO::getHour,pageReqVO.getHours())
                            .orderByAsc(StationScheduleHourDO::getStationId,StationScheduleHourDO::getOutputDate)
                    );
                }
                for (StationScheduleHourDO hourDO : list) {
                    if (i == 0) {
                        hour = hour.concat(",").concat(hourDO.getOutputDate());
                    }
                    factor = factor.concat(",").concat(hourDO.getFactor().toString());
                }
            }

            if (StationScaleCodeEnum.SCALE_LOAD_YEAR.getCode().equals(pageReqVO.getTimeScale())
                    || StationScaleCodeEnum.SCALE_LOAD_WEEK.getCode().equals(pageReqVO.getTimeScale())){
                // 年负荷特性
                if (StationScaleCodeEnum.SCALE_LOAD_YEAR.getCode().equals(pageReqVO.getTimeScale())) {
                    list = stationScheduleHourMapper.selectListGroupByMonth(stationList.get(i).getId());
                }
                //周负荷特性
                if (StationScaleCodeEnum.SCALE_LOAD_WEEK.getCode().equals(pageReqVO.getTimeScale())) {
                    list = stationScheduleHourMapper.selectListGroupByWeek(stationList.get(i).getId());
                }

                for (StationScheduleHourDO hourDO : list) {
                    factor = factor.concat(",").concat(hourDO.getFactor().toString());
                    if (StationScaleCodeEnum.SCALE_LOAD_YEAR.getCode().equals(pageReqVO.getTimeScale())) {
                        hour = hour.concat(",").concat(hourDO.getMonth().toString());
                    }
                    if (StationScaleCodeEnum.SCALE_LOAD_WEEK.getCode().equals(pageReqVO.getTimeScale())) {
                        hour = hour.concat(",").concat(hourDO.getWeek().toString());
                    }
                }
            }
            if (i == 0) {
                resultArray[0] = hour.split(",");
            }
            resultArray[i+1] = factor.split(",");
        }
        return resultArray;
    }*/


    /**
     * 获取长序列数据
     */
    private List<StationScheduleHourDO> getStationScheduleHourDOS(StationScheduleHourSequenceReqVO reqVO) {

        //查询条件
        QueryWrapperX<StationScheduleHourDO> wrapperX = new QueryWrapperX<>();
        wrapperX.lambda().in(ArrayUtils.isNotEmpty(reqVO.getStationIds()), StationScheduleHourDO::getStationId, reqVO.getStationIds());

        //月尺度
        if (StationScaleCodeEnum.SCALE_MONTH.getCode().equals(reqVO.getTimeScale())) {
            if (ArrayUtils.isNotEmpty(reqVO.getMonths())) {
                wrapperX.lambda().between(StationScheduleHourDO::getMonth, reqVO.getMonths()[0], reqVO.getMonths()[1]);
            } else {
                if (reqVO.getPageNo() != null) {
                    //无参数时   分十二页  每页查当月的
                    wrapperX.lambda().eq(StationScheduleHourDO::getMonth, reqVO.getPageNo());
                }
            }

        }
        //周尺度
        if (StationScaleCodeEnum.SCALE_WEEK.getCode().equals(reqVO.getTimeScale())) {
            if (ArrayUtils.isNotEmpty(reqVO.getWeeks())) {
                wrapperX.lambda().between(StationScheduleHourDO::getWeek, reqVO.getWeeks()[0], reqVO.getWeeks()[1]);
            } else {
                if (reqVO.getPageNo() != null) {
                    //无参数时   分52页
                    wrapperX.lambda().eq(StationScheduleHourDO::getWeek, reqVO.getPageNo());
                }
            }
        }
        //日尺度、日特性尺度
        if (StationScaleCodeEnum.SCALE_DAY.getCode().equals(reqVO.getTimeScale()) || StationScaleCodeEnum.SCALE_LOAD_DAY.getCode().equals(reqVO.getTimeScale())) {

            if (StationScaleCodeEnum.SCALE_DAY.getCode().equals(reqVO.getTimeScale()) && ArrayUtils.isNotEmpty(reqVO.getDays())) {
                reqVO.setStartDay(reqVO.getDays()[0] + " 00:00:00");
                reqVO.setEndDay(reqVO.getDays()[1] + " 23:59:59");
            }
            if (StationScaleCodeEnum.SCALE_LOAD_DAY.getCode().equals(reqVO.getTimeScale()) && reqVO.getDay() != null) {
                reqVO.setStartDay(reqVO.getDay() + " 00:00:00");
                reqVO.setEndDay(reqVO.getDay() + " 23:59:59");
            }
            wrapperX.ge(reqVO.getStartDay() != null, "CONCAT('2000', substr(output_date,5))", "2000-" + reqVO.getStartDay()).le(reqVO.getEndDay() != null, "CONCAT('2000', substr(output_date,5))", "2000-" + reqVO.getEndDay());
        }
        //获取数据
        List<StationScheduleHourDO> scheduleHourDOS = stationScheduleHourMapper.selectList(wrapperX);
        //周、月特性尺度
        List<StationScheduleHourDO> stationScheduleHourDOS = new ArrayList<>();
        if (SCALE_LOAD_WEEK.getCode().equals(reqVO.getTimeScale()) || SCALE_LOAD_MONTH.getCode().equals(reqVO.getTimeScale())) {
            Map<Long, Map<Integer, Optional<StationScheduleHourDO>>> collect = null;
            if (SCALE_LOAD_MONTH.getCode().equals(reqVO.getTimeScale())) {
                collect = scheduleHourDOS.stream().collect(Collectors.groupingBy(StationScheduleHourDO::getStationId, Collectors.groupingBy(StationScheduleHourDO::getMonth, Collectors.maxBy(Comparator.comparing(StationScheduleHourDO::getFactor)))));
            } else {
                collect = scheduleHourDOS.stream().collect(Collectors.groupingBy(StationScheduleHourDO::getStationId, Collectors.groupingBy(StationScheduleHourDO::getWeek, Collectors.maxBy(Comparator.comparing(StationScheduleHourDO::getFactor)))));
            }
            for (Map.Entry<Long, Map<Integer, Optional<StationScheduleHourDO>>> integerOptionalEntry : collect.entrySet()) {
                Map<Integer, Optional<StationScheduleHourDO>> value = integerOptionalEntry.getValue();
                for (Map.Entry<Integer, Optional<StationScheduleHourDO>> optionalEntry : value.entrySet()) {
                    if (optionalEntry.getValue().isPresent()) {
                        stationScheduleHourDOS.add(optionalEntry.getValue().get());
                    }
                }
            }
            scheduleHourDOS = stationScheduleHourDOS;
        }
        return scheduleHourDOS;
    }

}