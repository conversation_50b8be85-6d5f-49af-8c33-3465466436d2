package cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramteryear;

import cn.powerchina.bjy.cloud.framework.apilog.core.annotation.ApiAccessLog;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramteryear.vo.ParamterYearPageReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramteryear.vo.ParamterYearRespVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramteryear.vo.ParamterYearSaveReqVO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.paramteryear.ParamterYearDO;
import cn.powerchina.bjy.cloud.institute.essz.service.paramteryear.ParamterYearService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "8760 - 基础数据 - 电源经济性参数(投资-年度)")
@RestController
@RequestMapping("/plan/ess/paramter-year")
@Validated
public class ParamterYearController {

    @Resource
    private ParamterYearService paramterYearService;

    @PostMapping("/create")
    @Operation(summary = "创建电源经济性参数(投资-年度)")
    // @PreAuthorize("@ss.hasPermission('ess:paramter-year:create')")
    public CommonResult<Long> createParamterYear(@Valid @RequestBody ParamterYearSaveReqVO createReqVO) {
        return success(paramterYearService.createParamterYear(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新电源经济性参数(投资-年度)")
    // @PreAuthorize("@ss.hasPermission('ess:paramter-year:update')")
    public CommonResult<Boolean> updateParamterYear(@Valid @RequestBody ParamterYearSaveReqVO updateReqVO) {
        paramterYearService.updateParamterYear(updateReqVO);
        return success(true);
    }

    @PutMapping("/updateMany")
    @Operation(summary = "更新电源经济性参数(投资-年度)")
    // @PreAuthorize("@ss.hasPermission('ess:paramter-year:update')")
    public CommonResult<Boolean> updateParamterYearMany(@Valid @RequestBody List<ParamterYearSaveReqVO> updateReqs) {
        //使用频率比较低，还需要校验其他属性，暂时这样调用
        updateReqs.forEach(o -> paramterYearService.updateParamterYear(o));
        return success(true);
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除电源经济性参数(投资-年度)")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ess:paramter-year:delete')")
    public CommonResult<Boolean> deleteParamterYear(@RequestParam("id") Long id) {
        paramterYearService.deleteParamterYear(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得电源经济性参数(投资-年度)")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ess:paramter-year:query')")
    public CommonResult<ParamterYearRespVO> getParamterYear(@RequestParam("id") Long id) {
        ParamterYearDO paramterYear = paramterYearService.getParamterYear(id);
        return success(BeanUtils.toBean(paramterYear, ParamterYearRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得电源经济性参数(投资-年度)分页")
    @PreAuthorize("@ss.hasPermission('ess:paramter-year:query')")
    public CommonResult<PageResult<ParamterYearRespVO>> getParamterYearPage(@Valid ParamterYearPageReqVO pageReqVO) {
        PageResult<ParamterYearDO> pageResult = paramterYearService.getParamterYearPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ParamterYearRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出电源经济性参数(投资-年度) Excel")
    @PreAuthorize("@ss.hasPermission('ess:paramter-year:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportParamterYearExcel(@Valid ParamterYearPageReqVO pageReqVO,
                                        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ParamterYearDO> list = paramterYearService.getParamterYearPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "电源经济性参数(投资-年度).xls", "数据", ParamterYearRespVO.class,
                BeanUtils.toBean(list, ParamterYearRespVO.class));
    }

    @GetMapping("/all")
    @Operation(summary = "获得某项目所有电源经济性参数(投资-年度)")
    @Parameter(name = "projectId", description = "项目ID", required = true, example = "4")
    // @PreAuthorize("@ss.hasPermission('ess:paramter-year:query')")
    public CommonResult<List<ParamterYearRespVO>> listParamterYear(@RequestParam("projectId") Long projectId) {
        List<ParamterYearRespVO> paramterYearRespVOs = paramterYearService.listParamterYear(projectId);
        return success(paramterYearRespVOs);
    }
}