package cn.powerchina.bjy.cloud.institute.essz.controller.admin.pnlelectricitypower.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 电力市场参数复核表-电源数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PnlElectricityPowerPageReqVO extends PageParam {

    @Schema(description = "工程ID", example = "27545")
    private Long projectId;

    @Schema(description = "电力市场参数复核表Id", example = "2380")
    private Long parentId;

    @Schema(description = "电源类型 (1-煤电、2-气电、3-核电、4-常规水电、5-抽水蓄能、6-风电、7-光伏、8-光热、9-生物发电、10-新型储能、11-其他)", example = "2")
    private Integer powerType;

    @Schema(description = "供热期 (0=非供热期、1=供热期)")
    private Integer heatingSeason;

    @Schema(description = "系数")
    private BigDecimal coefficient;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}