package cn.powerchina.bjy.cloud.institute.essz.service.keydatadisplay;

import cn.powerchina.bjy.cloud.framework.common.util.ext.SafeConverter;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.institute.essz.constants.EssConstant;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.keydatadisplay.vo.*;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.pnlelectricitybase.vo.LoadParamsVo;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.pnlelectricitybase.vo.PnlElectricityBaseRespVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.pnlelectricitypower.vo.PnlElectricityPowerRespVO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.loadyear.LoadYearDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.station.EssStationDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.stationenergypercent.StationEnergyPercentDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.stationscheduleweek.StationScheduleWeekDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.transfer.TransferDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.loadyear.LoadYearMapper;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.project.EssProjectMapper;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.station.EssStationMapper;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.stationenergypercent.StationEnergyPercentMapper;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.stationscheduleweek.StationScheduleWeekMapper;
import cn.powerchina.bjy.cloud.institute.essz.dal.mysql.transfer.TransferMapper;
import cn.powerchina.bjy.cloud.institute.essz.enums.LoadYearTypeCodeEnum;
import cn.powerchina.bjy.cloud.institute.essz.enums.StationCategoryCodeEnum;
import cn.powerchina.bjy.cloud.institute.essz.enums.TransferTypeCodeEnum;
import cn.powerchina.bjy.cloud.institute.essz.model.EssStationEnhanceInfo;
import cn.powerchina.bjy.cloud.institute.essz.service.pnlelectricity.PnLElectricityServiceImpl;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.essz.constants.ErrorCodeConstants.*;

/**
 * 关键数据展示 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class KeyDataDisplayServiceImpl implements KeyDataDisplayService {
    @Resource
    private StationEnergyPercentMapper stationEnergyPercentMapper;
    @Resource
    private LoadYearMapper loadYearMapper;
    @Resource
    private EssProjectMapper essProjectMapper;
    @Resource
    private EssStationMapper essStationMapper;
    @Resource
    private TransferMapper transferMapper;
    @Resource
    private StationScheduleWeekMapper stationScheduleWeekMapper;
    @Resource
    private PnLElectricityServiceImpl pnLElectricityService;

    private Integer wrongYear = -1;

    private Integer provincePowerDevelopmentPlanDataType = 1;
    private Integer transferInPowerDevelopmentPlanDataType = 2;
    private Integer transferOutPowerDevelopmentPlanDataType = 3;

    private int heatingPeriod = 1;
    private int noHeatingPeriod = 2;

    private Map<Integer, BigDecimal> defaultStationParameter = new HashMap<>() {{
        put(1, BigDecimal.valueOf(100.0000000000));
        put(2, BigDecimal.valueOf(100.0000000000));
        put(3, BigDecimal.valueOf(100.0000000000));
        put(4, BigDecimal.valueOf(100.0000000000));
        put(5, BigDecimal.valueOf(100.0000000000));
        put(6, BigDecimal.valueOf(100.0000000000));
        put(7, BigDecimal.valueOf(100.0000000000));
        put(8, BigDecimal.valueOf(100.0000000000));
        put(9, BigDecimal.valueOf(100.0000000000));
        put(10, BigDecimal.valueOf(100.0000000000));
        put(11, BigDecimal.valueOf(100.0000000000));
    }};

    @Override
    public List<KeyDataRespVO> getKeyData(Long projectId) {
        List<LoadYearDO> loadYearDOs = loadYearMapper.selectList(new LambdaQueryWrapperX<LoadYearDO>().eqIfPresent(LoadYearDO::getProjectId, projectId).apply("deleted = {0}", 0));
        if (loadYearDOs.isEmpty()) {
            return List.of();
        }
        Integer currentYear = this.getCurrentYear(loadYearDOs, LoadYearTypeCodeEnum.LOAD_CURRENT);
        if (currentYear.compareTo(wrongYear) == 0) {
            throw exception(ESS_KEY_DATA_DISPLAY_CURRENT_YEAR_NOT_EXISTS);
        }
        Integer levelYear = this.getLevelYear(projectId);
        if (levelYear.compareTo(wrongYear) == 0) {
            throw exception(ESS_KEY_DATA_DISPLAY_LEVEL_YEAR_NOT_EXISTS);
        }

        Map<String, BigDecimal> currentYearDataInLoadYear = this.getKeyDataInLoadYear(loadYearDOs, currentYear);
        Map<String, BigDecimal> levelYearDataInLoadYear = this.getKeyDataInLoadYear(loadYearDOs, levelYear);
        List<EssStationDO> currentYearDataInStation = essStationMapper.selectByProjectIdAndLoadYear(projectId, currentYear);
        List<EssStationDO> levelYearDataInStation = essStationMapper.selectByProjectIdAndLoadYear(projectId, levelYear);

        List<KeyDataRespVO> keyDataRespVOs = new ArrayList<>();
        KeyDataRespVO currentYearKeyData = new KeyDataRespVO();
        currentYearKeyData.setMaxLoad(currentYearDataInLoadYear.get("maxLoad"));
        currentYearKeyData.setUsePower(currentYearDataInLoadYear.get("usePower"));
        currentYearKeyData.setYearTypeCode(LoadYearTypeCodeEnum.LOAD_CURRENT.getCode());
        currentYearKeyData.setStation(this.getKeyDataInStation(currentYearDataInStation));
        keyDataRespVOs.add(currentYearKeyData);
        KeyDataRespVO levelYearKeyData = new KeyDataRespVO();
        levelYearKeyData.setMaxLoad(levelYearDataInLoadYear.get("maxLoad"));
        levelYearKeyData.setUsePower(levelYearDataInLoadYear.get("usePower"));
        levelYearKeyData.setYearTypeCode(LoadYearTypeCodeEnum.LOAD_LEVEL.getCode());
        levelYearKeyData.setStation(this.getKeyDataInStation(levelYearDataInStation));
        keyDataRespVOs.add(levelYearKeyData);

        return keyDataRespVOs;
    }

    @Override
    public KeyDataPowerDevelopmentPlanRespVO getKeyDataPowerDevelopmentPlan(Long projectId) {
        List<LoadYearDO> loadYearDOs = loadYearMapper.selectList(new LambdaQueryWrapperX<LoadYearDO>().eqIfPresent(LoadYearDO::getProjectId, projectId).apply("deleted = {0}", 0));
        if (loadYearDOs.isEmpty()) {
            return null;
        }
        Integer currentYear = this.getCurrentYear(loadYearDOs, LoadYearTypeCodeEnum.LOAD_CURRENT);
        if (currentYear.compareTo(wrongYear) == 0) {
            throw exception(ESS_KEY_DATA_DISPLAY_CURRENT_YEAR_NOT_EXISTS);
        }
        Integer levelYear = this.getLevelYear(projectId);
        if (levelYear.compareTo(wrongYear) == 0) {
            throw exception(ESS_KEY_DATA_DISPLAY_LEVEL_YEAR_NOT_EXISTS);
        }

        List<KeyDataPowerDevelopmentPlan> keyDataPowerDevelopmentPlans = new ArrayList<>();
        List<EssStationDO> currentYearDataInStation = essStationMapper.selectByProjectIdAndLoadYear(projectId, currentYear);
        List<KeyDataStationVO> currentYearKeyDataStationVOs = this.getKeyDataInStation(currentYearDataInStation);
        KeyDataPowerDevelopmentPlan provinceCurrentYear = this.getKeyDataPowerDevelopmentPlan(provincePowerDevelopmentPlanDataType, currentYear, currentYearKeyDataStationVOs, LoadYearTypeCodeEnum.LOAD_CURRENT);
        keyDataPowerDevelopmentPlans.add(provinceCurrentYear);
        List<EssStationDO> levelYearDataInStation = essStationMapper.selectByProjectIdAndLoadYear(projectId, levelYear);
        List<KeyDataStationVO> levelYearKeyDataStationVO = this.getKeyDataInStation(levelYearDataInStation);
        KeyDataPowerDevelopmentPlan provinceLevelYear = this.getKeyDataPowerDevelopmentPlan(provincePowerDevelopmentPlanDataType, levelYear, levelYearKeyDataStationVO, LoadYearTypeCodeEnum.LOAD_LEVEL);
        keyDataPowerDevelopmentPlans.add(provinceLevelYear);

        List<TransferDO> currentYearDataInTransfer = transferMapper.selectByProjectIdAndLoadYear(projectId, currentYear);
        List<KeyDataStationVO> currentYearTransferInVOs = this.getPowerDevelopmentPlanInTransfer(currentYearDataInTransfer, TransferTypeCodeEnum.INTER_TSI);
        KeyDataPowerDevelopmentPlan transferInCurrentYear = this.getKeyDataPowerDevelopmentPlan(transferInPowerDevelopmentPlanDataType, currentYear, currentYearTransferInVOs, LoadYearTypeCodeEnum.LOAD_CURRENT);
        keyDataPowerDevelopmentPlans.add(transferInCurrentYear);
        List<KeyDataStationVO> currentYearTransferOutVOs = this.getPowerDevelopmentPlanInTransfer(currentYearDataInTransfer, TransferTypeCodeEnum.INTER_TSO);
        KeyDataPowerDevelopmentPlan transferOutCurrentYear = this.getKeyDataPowerDevelopmentPlan(transferOutPowerDevelopmentPlanDataType, currentYear, currentYearTransferOutVOs, LoadYearTypeCodeEnum.LOAD_CURRENT);
        keyDataPowerDevelopmentPlans.add(transferOutCurrentYear);

        List<TransferDO> levelYearDataInTransfer = transferMapper.selectByProjectIdAndLoadYear(projectId, levelYear);
        List<KeyDataStationVO> levelYearTransferInVOs = this.getPowerDevelopmentPlanInTransfer(levelYearDataInTransfer, TransferTypeCodeEnum.INTER_TSI);
        KeyDataPowerDevelopmentPlan transferInLevelYearPower = this.getKeyDataPowerDevelopmentPlan(transferInPowerDevelopmentPlanDataType, levelYear, levelYearTransferInVOs, LoadYearTypeCodeEnum.LOAD_LEVEL);
        keyDataPowerDevelopmentPlans.add(transferInLevelYearPower);
        List<KeyDataStationVO> levelYearTransferOutVOs = this.getPowerDevelopmentPlanInTransfer(levelYearDataInTransfer, TransferTypeCodeEnum.INTER_TSO);
        KeyDataPowerDevelopmentPlan transferOutLevelYear = this.getKeyDataPowerDevelopmentPlan(transferOutPowerDevelopmentPlanDataType, levelYear, levelYearTransferOutVOs, LoadYearTypeCodeEnum.LOAD_LEVEL);
        keyDataPowerDevelopmentPlans.add(transferOutLevelYear);

        return this.fillBaseDataInstalledCapacity(keyDataPowerDevelopmentPlans);
    }

    @Override
    public KeyDataPowerBalanceParameterRespVO getKeyDataPowerBalanceParameter(Long projectId) {
        EssStationEnhanceInfo enhanceInfo = pnLElectricityService.essStationEnhanceInfo(projectId, null, true);
        if (SafeConverter.toInt(enhanceInfo.getProject().getLevelYear()) == 0) {
            return null;
        }

        List<StationEnergyPercentDO> existedStationEnergyPercentDOs = stationEnergyPercentMapper.selectList(StationEnergyPercentDO::getProjectId, projectId);
        //有值直接返回
        if (existedStationEnergyPercentDOs.size() > 0) {
            KeyDataPowerBalanceParameter heatingPeriodData = this.convert2KeyDataPowerBalanceParameter(existedStationEnergyPercentDOs, enhanceInfo.getLoadYear(), heatingPeriod);
            KeyDataPowerBalanceParameter noHeatingPeriodData = this.convert2KeyDataPowerBalanceParameter(existedStationEnergyPercentDOs, enhanceInfo.getLoadYear(), noHeatingPeriod);
            return this.convert2KeyDataPowerBalanceParameterRespVO(heatingPeriodData, noHeatingPeriodData);
        }

        Map<Long, EssStationDO> essStationMap = enhanceInfo.getStationMap();
        Set<Long> stationIds = essStationMap.keySet();
        if (stationIds.size() <= 0) {
            KeyDataPowerBalanceParameterRespVO init = new KeyDataPowerBalanceParameterRespVO();
            init.setPowerTypeCode(0);
            init.setStation(Lists.newArrayList());
            List<KeyDataPowerBalanceParameterTable> powerBalanceParameter = new ArrayList<>();
            for (StationCategoryCodeEnum categoryCodeEnum : StationCategoryCodeEnum.values()) {
                KeyDataPowerBalanceParameterTable e = new KeyDataPowerBalanceParameterTable(categoryCodeEnum.getName());
                powerBalanceParameter.add(e);
            }
            init.setPowerBalanceParameter(powerBalanceParameter);
            return init;
        }

        LoadParamsVo paramsVo = pnLElectricityService.calcElectricity(enhanceInfo);
        KeyDataPowerBalanceParameterRespVO powerBalanceParameterRespVO = new KeyDataPowerBalanceParameterRespVO();
        Map<String, KeyDataPowerBalanceParameterTable> tableDataMap = new HashMap<>();
        PnlElectricityBaseRespVO base = paramsVo.getBase();
        powerBalanceParameterRespVO.setLoadSideFactor(base.getLoadSideManagement());
        powerBalanceParameterRespVO.setLoadReserveFactor(base.getLoadReserveRate());
        powerBalanceParameterRespVO.setOverhaulReserveFactor(base.getMaintenanceReserveRate());
        powerBalanceParameterRespVO.setAccidentReserveFactor(base.getOutageReserveRate());
        for (PnlElectricityPowerRespVO station : paramsVo.getPowerList()) {
            KeyDataPowerBalanceParameterTable tempTableData = tableDataMap.computeIfAbsent(station.getPowerTypeName(), k -> new KeyDataPowerBalanceParameterTable(station.getPowerTypeName(), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));


            tempTableData.setHeatingInstalledCapacity(station.getHeatingCapacity());
            tempTableData.setNoHeatingInstalledCapacity(station.getHeatingCapacity());
            tempTableData.setHeatingBlockedFactor(station.getHeatingResistCoeff() == null ? BigDecimal.ZERO : station.getHeatingResistCoeff().setScale(3, EssConstant.ROUNDING_MODE));
            tempTableData.setNoHeatingBlockedFactor(station.getNonHeatingResistCoeff() == null ? BigDecimal.ZERO : station.getNonHeatingResistCoeff().setScale(3, EssConstant.ROUNDING_MODE));
        }

        List<KeyDataPowerBalanceParameterTable> tableData = tableDataMap.values().stream().sorted((Comparator.comparingInt(o -> StationCategoryCodeEnum.NAME_CODE_MAP.getOrDefault(o.getPowerType(), 1)))).collect(Collectors.toList());
        powerBalanceParameterRespVO.setPowerBalanceParameter(tableData);

        this.insertStationEnergyPercent(projectId, heatingPeriod, paramsVo.getPowerList());
        this.insertStationEnergyPercent(projectId, noHeatingPeriod, paramsVo.getPowerList());
        return powerBalanceParameterRespVO;
    }

    @Override
    public void updatePowerBalanceParameter(@Valid KeyDataPowerBalanceParameterReqVO powerBalanceParameterReqVO) {
        validPowerBalanceParameterReqVO(powerBalanceParameterReqVO);

        List<KeyDataPowerBalanceParameter> keyDataPowerBalanceParameters = this.convert2KeyDataPowerBalanceParameters(powerBalanceParameterReqVO);
        for (KeyDataPowerBalanceParameter keyDataPowerBalanceParameter : keyDataPowerBalanceParameters) {
            StationEnergyPercentDO stationEnergyPercentOld = validateStationEnergyPercentExists(keyDataPowerBalanceParameter.getProjectId(), keyDataPowerBalanceParameter.getPowerTypeCode());

            Integer levelYear = this.getLevelYear(keyDataPowerBalanceParameter.getProjectId());
            if (levelYear.compareTo(wrongYear) == 0) {
                throw exception(ESS_KEY_DATA_DISPLAY_CURRENT_YEAR_NOT_EXISTS);
            }
            Long loadYearId = validateLoadYearExists(keyDataPowerBalanceParameter.getProjectId(), levelYear);

            StationEnergyPercentDO stationEnergyPercentDO = this.convert2StationEnergyPercentDO(keyDataPowerBalanceParameter.getStation());
            stationEnergyPercentDO.setId(stationEnergyPercentOld.getId());
            stationEnergyPercentDO.setProjectId(keyDataPowerBalanceParameter.getProjectId());
            stationEnergyPercentDO.setHeatingPeriodType(keyDataPowerBalanceParameter.getPowerTypeCode());
            stationEnergyPercentMapper.updateById(stationEnergyPercentDO);

            LoadYearDO loadYearDO = new LoadYearDO();
            loadYearDO.setId(loadYearId);
            loadYearDO.setLoadSideFactor(keyDataPowerBalanceParameter.getLoadSideFactor());
            loadYearDO.setLoadReserveFactor(keyDataPowerBalanceParameter.getLoadReserveFactor());
            loadYearDO.setOverhaulReserveFactor(keyDataPowerBalanceParameter.getOverhaulReserveFactor());
            loadYearDO.setAccidentReserveFactor(keyDataPowerBalanceParameter.getAccidentReserveFactor());
            loadYearMapper.updateIgnoreNull(loadYearDO);
        }
    }

    private void validPowerBalanceParameterReqVO(KeyDataPowerBalanceParameterReqVO powerBalanceParameterReqVO) {
        if (powerBalanceParameterReqVO.getLoadSideFactor().compareTo(BigDecimal.valueOf(100)) >= 0 || powerBalanceParameterReqVO.getLoadSideFactor().compareTo(BigDecimal.valueOf(0)) < 0) {
            throw exception(ESS_KEY_DATA_DISPLAY_LOAD_SIDE_FACTOR_ERROR);
        }
    }

    /**
     * 转换数据到 List<KeyDataPowerBalanceParameter> 类型
     *
     * @param data 供 内部处理 的 电力平衡参数 的 列表
     */
    private List<KeyDataPowerBalanceParameter> convert2KeyDataPowerBalanceParameters(KeyDataPowerBalanceParameterReqVO data) {
        KeyDataPowerBalanceParameter heatingPeriodData = new KeyDataPowerBalanceParameter();
        heatingPeriodData.setProjectId(data.getProjectId());
        heatingPeriodData.setLoadSideFactor(data.getLoadSideFactor());
        heatingPeriodData.setLoadReserveFactor(data.getLoadReserveFactor());
        heatingPeriodData.setOverhaulReserveFactor(data.getOverhaulReserveFactor());
        heatingPeriodData.setAccidentReserveFactor(data.getAccidentReserveFactor());
        heatingPeriodData.setPowerTypeCode(heatingPeriod);
        KeyDataPowerBalanceParameter noHeatingPeriodData = new KeyDataPowerBalanceParameter();
        noHeatingPeriodData.setProjectId(data.getProjectId());
        noHeatingPeriodData.setLoadSideFactor(data.getLoadSideFactor());
        noHeatingPeriodData.setLoadReserveFactor(data.getLoadReserveFactor());
        noHeatingPeriodData.setOverhaulReserveFactor(data.getOverhaulReserveFactor());
        noHeatingPeriodData.setAccidentReserveFactor(data.getAccidentReserveFactor());
        noHeatingPeriodData.setPowerTypeCode(noHeatingPeriod);

        Map<String, KeyDataStationPowerBalanceParameterVO> heatingPeriodStationData = new HashMap<>();
        Map<String, KeyDataStationPowerBalanceParameterVO> noHeatingPeriodStationData = new HashMap<>();
        for (KeyDataPowerBalanceParameterTable item : data.getPowerBalanceParameter()) {
            KeyDataStationPowerBalanceParameterVO tempHeatingPeriodStation = new KeyDataStationPowerBalanceParameterVO();
            KeyDataStationPowerBalanceParameterVO tempNoHeatingPeriodStation = new KeyDataStationPowerBalanceParameterVO();
            if (heatingPeriodStationData.get(item.getPowerType()) != null) {
                tempHeatingPeriodStation = heatingPeriodStationData.get(item.getPowerType());
            } else {
                heatingPeriodStationData.put(item.getPowerType(), tempHeatingPeriodStation);
            }
            if (noHeatingPeriodStationData.get(item.getPowerType()) != null) {
                tempNoHeatingPeriodStation = noHeatingPeriodStationData.get(item.getPowerType());
            } else {
                noHeatingPeriodStationData.put(item.getPowerType(), tempNoHeatingPeriodStation);
            }

            tempHeatingPeriodStation.setStationParameter(item.getHeatingPeriodData());
            tempHeatingPeriodStation.setInstalledCapacity(item.getHeatingInstalledCapacity());
            tempHeatingPeriodStation.setInstalledCapacity(item.getHeatingBlockedFactor());
            tempHeatingPeriodStation.setCategoryCode(StationCategoryCodeEnum.getCode(item.getPowerType()));
            tempHeatingPeriodStation.setStationTypeName(item.getPowerType());

            tempNoHeatingPeriodStation.setStationParameter(item.getNoHeatingPeriodData());
            tempNoHeatingPeriodStation.setInstalledCapacity(item.getNoHeatingBlockedFactor());
            tempNoHeatingPeriodStation.setInstalledCapacity(item.getNoHeatingBlockedFactor());
            tempNoHeatingPeriodStation.setCategoryCode(StationCategoryCodeEnum.getCode(item.getPowerType()));
            tempNoHeatingPeriodStation.setStationTypeName(item.getPowerType());

            heatingPeriodStationData.put(item.getPowerType(), tempHeatingPeriodStation);
            noHeatingPeriodStationData.put(item.getPowerType(), tempNoHeatingPeriodStation);
        }
        heatingPeriodData.setStation(new ArrayList<>(heatingPeriodStationData.values()));
        noHeatingPeriodData.setStation(new ArrayList<>(noHeatingPeriodStationData.values()));
        return List.of(heatingPeriodData, noHeatingPeriodData);
    }

    /**
     * 校验 StationEnergyPercent 表中数据是否存在
     *
     * @param projectId         项目ID
     * @param heatingPeriodType 供热期类型
     * @return 该条数据的 数据库主键
     */
    private StationEnergyPercentDO validateStationEnergyPercentExists(Long projectId, Integer heatingPeriodType) {
        StationEnergyPercentDO result = stationEnergyPercentMapper.selectOne(new LambdaQueryWrapperX<StationEnergyPercentDO>().eqIfPresent(StationEnergyPercentDO::getProjectId, projectId).eqIfPresent(StationEnergyPercentDO::getHeatingPeriodType, heatingPeriodType).apply("deleted = {0}", 0));
        if (result == null) {
            throw exception(ESS_KEY_DATA_DISPLAY_STATION_ENERGY_PERCENT_NOT_EXISTS);
        }

        return result;
    }

    /**
     * 校验 loadYear 表中数据是否存在
     *
     * @param projectId 项目ID
     * @return 该条数据的 数据库主键
     */
    private Long validateLoadYearExists(Long projectId, Integer levelYear) {
        LoadYearDO result = loadYearMapper.selectOne(new LambdaQueryWrapperX<LoadYearDO>().eqIfPresent(LoadYearDO::getProjectId, projectId).eqIfPresent(LoadYearDO::getYear, levelYear).apply("deleted = {0}", 0));
        if (result == null) {
            throw exception(ESS_LOAD_YEAR_NOT_EXISTS);
        }

        return result.getId();
    }

    /**
     * 获取 现状年
     *
     * @return 现状年的数字表示，如果为 -1 则表示没有通过 项目ID 查询到数据
     */
    private Integer getCurrentYear(List<LoadYearDO> loadYearDOs, LoadYearTypeCodeEnum currentYearTypeCodeEnum) {
        List<Integer> currentYears = loadYearDOs.stream().filter(loadYearDO -> loadYearDO.getTypeCode().compareTo(currentYearTypeCodeEnum.getCode()) == 0).map(LoadYearDO::getYear).map(Integer::parseInt).sorted((a, b) -> b - a).toList();

        if (currentYears.size() <= 0) {
            return wrongYear;
        }
        return currentYears.get(0);
    }

    /**
     * 获取 水平年
     *
     * @param projectId 项目ID
     * @return 水平年的数字表示，如果为 -1 则表示没有通过 项目ID 查询到数据
     */
    private Integer getLevelYear(Long projectId) {
        String levelYear = essProjectMapper.getLevelYear(projectId);
        if (levelYear == null) {
            return wrongYear;
        }

        return Integer.parseInt(levelYear);
    }

    /**
     * 获取 电站类别名称
     *
     * @param categoryCode 电站类别代码
     * @return 电站类别名称
     */
    private String getStationTypeName(Integer categoryCode) {
        String stationTypeName = StationCategoryCodeEnum.getName(categoryCode);
        if (stationTypeName == null) {
            throw exception(ESS_KEY_DATA_DISPLAY_CATEGORY_CODE_NOT_EXISTS);
        }

        return stationTypeName;
    }

    /**
     * 获取 关键数据展示 中存在于 LoadYear 表中的数据
     *
     * @param loadYearDOs 查询出来的 LoadYear 表中的数据
     * @param handledYear 要处理的年份
     * @return LoadYear 表中的 数据Map
     */
    private Map<String, BigDecimal> getKeyDataInLoadYear(List<LoadYearDO> loadYearDOs, Integer handledYear) {
        BigDecimal maxLoad = null;
        BigDecimal usePower = null;
        String handledYearStr = handledYear.toString();
        Map<String, BigDecimal> resultMap = new HashMap<>();
        for (LoadYearDO loadYearDO : loadYearDOs) {
            if (handledYearStr.equals(loadYearDO.getYear())) {
                maxLoad = loadYearDO.getMaxLoad();
                usePower = loadYearDO.getUsePower();
                resultMap.put("maxLoad", maxLoad);
                resultMap.put("usePower", usePower);
                break;
            }
        }

        return resultMap;
    }

    /**
     * 获取 关键数据展示 中存在于 EssStation 表中的符合 {@link KeyDataStationVO} 数据
     *
     * @param loadYearStationDOs 查询出来的 EssStation 表中的数据
     * @return EssStation 表中在 KeyDataStationVO 需要的数据
     */
    private List<KeyDataStationVO> getKeyDataInStation(List<EssStationDO> loadYearStationDOs) {
        List<KeyDataStationVO> keyDataStationVOs = new ArrayList<>();
        KeyDataStationVO keyDataStationData = null;
        int defaultCategoryCode = -1;
        int currentCategoryCode = defaultCategoryCode;
        BigDecimal allInstalledCapacity = BigDecimal.ZERO;
        BigDecimal allStationTypePercent = BigDecimal.ZERO;
        BigDecimal biggerStationTypePercent = BigDecimal.ZERO;

        for (EssStationDO stationDO : loadYearStationDOs) {
            if (stationDO.getCategoryCode().compareTo(currentCategoryCode) != 0) {
                if (currentCategoryCode != defaultCategoryCode) {
                    keyDataStationVOs.add(keyDataStationData);
                }
                currentCategoryCode = stationDO.getCategoryCode();
                keyDataStationData = new KeyDataStationVO();
                keyDataStationData.setCategoryCode(stationDO.getCategoryCode());
                keyDataStationData.setInstalledCapacity(BigDecimal.ZERO);
                keyDataStationData.setStationTypeName(this.getStationTypeName(stationDO.getCategoryCode()));
                keyDataStationData.setStationTypePercent(BigDecimal.ZERO);
            }
            BigDecimal installedCapacity = keyDataStationData.getInstalledCapacity();
            installedCapacity = installedCapacity.add(stationDO.getInstalledCapacity().setScale(2, RoundingMode.HALF_UP));
            allInstalledCapacity = allInstalledCapacity.add(stationDO.getInstalledCapacity().setScale(2, RoundingMode.HALF_UP));
            keyDataStationData.setInstalledCapacity(installedCapacity);
        }
        if (currentCategoryCode != defaultCategoryCode) {
            keyDataStationVOs.add(keyDataStationData);
        }
        for (KeyDataStationVO keyDataStationVO : keyDataStationVOs) {
            BigDecimal stationTypePercent = keyDataStationVO.getInstalledCapacity().divide(allInstalledCapacity, 4, RoundingMode.HALF_UP);
            keyDataStationVO.setStationTypePercent(stationTypePercent);
            biggerStationTypePercent = biggerStationTypePercent.max(stationTypePercent);
            allStationTypePercent = allStationTypePercent.add(stationTypePercent);
        }
        for (KeyDataStationVO keyDataStationVO : keyDataStationVOs) {
            keyDataStationVO.setInstalledCapacity(keyDataStationVO.getInstalledCapacity().stripTrailingZeros());
            if (allStationTypePercent.compareTo(BigDecimal.ONE) != 0 && biggerStationTypePercent.compareTo(keyDataStationVO.getStationTypePercent()) == 0) {
                BigDecimal newValue = keyDataStationVO.getStationTypePercent().subtract(allStationTypePercent.subtract(BigDecimal.ONE));
                keyDataStationVO.setStationTypePercent(newValue.multiply(BigDecimal.valueOf(100L)).stripTrailingZeros());
                allStationTypePercent = BigDecimal.ONE;
                continue;
            }

            keyDataStationVO.setStationTypePercent(keyDataStationVO.getStationTypePercent().multiply(BigDecimal.valueOf(100L)).stripTrailingZeros());
        }

        return keyDataStationVOs;
    }

    /**
     * 获取 电源发展规划中 中存在于 EssTransfer 表中的符合 {@link KeyDataStationVO} 数据
     *
     * @param loadYearTransferDOs  查询出来的 EssTransfer 表中的数据
     * @param transferTypeCodeEnum 送受电传输枚举
     * @return EssTransfer 表中在 KeyDataStationVO 需要的数据
     */
    private List<KeyDataStationVO> getPowerDevelopmentPlanInTransfer(List<TransferDO> loadYearTransferDOs, TransferTypeCodeEnum transferTypeCodeEnum) {
        List<KeyDataStationVO> keyDataStationVOs = new ArrayList<>();
        KeyDataStationVO keyDataStationData = null;

        Integer transferTypeCode = transferTypeCodeEnum.getCode();
        for (TransferDO transferDO : loadYearTransferDOs) {
            if (transferDO.getTypeCode().compareTo(transferTypeCode) == 0) {
                keyDataStationData = new KeyDataStationVO();
                keyDataStationData.setInstalledCapacity(transferDO.getChannelCapacity());
                keyDataStationData.setStationTypeName(transferDO.getName());
                keyDataStationVOs.add(keyDataStationData);
            }
        }
        return keyDataStationVOs;
    }

    /**
     * 获取 电源发展规划 的 单个数据体
     *
     * @param dataType                  电源发展规划数据类型
     * @param loadYear                  当前年的字面量
     * @param loadYearKeyDataStationVOs 电源 或 通道 查询结果数据
     * @param loadYearTypeCodeEnum      年类型枚举
     * @return 电源发展规划 的 单个数据体
     */
    private KeyDataPowerDevelopmentPlan getKeyDataPowerDevelopmentPlan(Integer dataType, Integer loadYear, List<KeyDataStationVO> loadYearKeyDataStationVOs, LoadYearTypeCodeEnum loadYearTypeCodeEnum) {
        KeyDataPowerDevelopmentPlan keyDataPowerDevelopmentPlan = new KeyDataPowerDevelopmentPlan();
        keyDataPowerDevelopmentPlan.setDataType(dataType);
        keyDataPowerDevelopmentPlan.setStation(loadYearKeyDataStationVOs);
        keyDataPowerDevelopmentPlan.setYear(loadYear.toString());
        BigDecimal yearInstalledCapacity = loadYearKeyDataStationVOs.stream().map(KeyDataStationVO::getInstalledCapacity).reduce(BigDecimal.ZERO, BigDecimal::add);
        keyDataPowerDevelopmentPlan.setYearInstalledCapacity(yearInstalledCapacity.stripTrailingZeros());
        keyDataPowerDevelopmentPlan.setYearTypeCode(loadYearTypeCodeEnum.getCode());

        return keyDataPowerDevelopmentPlan;
    }

    /**
     * 填充 电源发展规划 中 基础数据选项 的值
     *
     * @param keyDataPowerDevelopmentPlans 已经计算好的 电源发展规划 的结果
     * @return 填充 基础数据选项 后的 电源发展规划 的结果
     */
    private KeyDataPowerDevelopmentPlanRespVO fillBaseDataInstalledCapacity(List<KeyDataPowerDevelopmentPlan> keyDataPowerDevelopmentPlans) {
        BigDecimal currentYearBaseDataInstalledCapacity = BigDecimal.ZERO;
        BigDecimal levelYearBaseDataInstalledCapacity = BigDecimal.ZERO;
        for (KeyDataPowerDevelopmentPlan data : keyDataPowerDevelopmentPlans) {
            if (data.getYearTypeCode().compareTo(LoadYearTypeCodeEnum.LOAD_CURRENT.getCode()) == 0) {
                if (data.getDataType().compareTo(provincePowerDevelopmentPlanDataType) == 0) {
                    currentYearBaseDataInstalledCapacity = currentYearBaseDataInstalledCapacity.add(data.getYearInstalledCapacity());
                    continue;
                }
                if (data.getDataType().compareTo(transferInPowerDevelopmentPlanDataType) == 0) {
                    currentYearBaseDataInstalledCapacity = currentYearBaseDataInstalledCapacity.add(data.getYearInstalledCapacity());
                    continue;
                }
                if (data.getDataType().compareTo(transferOutPowerDevelopmentPlanDataType) == 0) {
                    currentYearBaseDataInstalledCapacity = currentYearBaseDataInstalledCapacity.subtract(data.getYearInstalledCapacity());
                }
                continue;
            }
            if (data.getDataType().compareTo(provincePowerDevelopmentPlanDataType) == 0) {
                levelYearBaseDataInstalledCapacity = levelYearBaseDataInstalledCapacity.add(data.getYearInstalledCapacity());
                continue;
            }
            if (data.getDataType().compareTo(transferInPowerDevelopmentPlanDataType) == 0) {
                levelYearBaseDataInstalledCapacity = levelYearBaseDataInstalledCapacity.add(data.getYearInstalledCapacity());
                continue;
            }
            if (data.getDataType().compareTo(transferOutPowerDevelopmentPlanDataType) == 0) {
                levelYearBaseDataInstalledCapacity = levelYearBaseDataInstalledCapacity.subtract(data.getYearInstalledCapacity());
            }
        }
        for (KeyDataPowerDevelopmentPlan data : keyDataPowerDevelopmentPlans) {
            if (data.getYearTypeCode().compareTo(LoadYearTypeCodeEnum.LOAD_CURRENT.getCode()) == 0) {
                data.setBaseDataInstalledCapacity(currentYearBaseDataInstalledCapacity);
                continue;
            }
            data.setBaseDataInstalledCapacity(levelYearBaseDataInstalledCapacity);
        }

        KeyDataPowerDevelopmentPlanRespVO keyDataPowerDevelopmentPlanRespVO = new KeyDataPowerDevelopmentPlanRespVO();
        for (KeyDataPowerDevelopmentPlan data : keyDataPowerDevelopmentPlans) {
            if (data.getYearTypeCode().compareTo(LoadYearTypeCodeEnum.LOAD_CURRENT.getCode()) == 0) {
                keyDataPowerDevelopmentPlanRespVO.setCurrentYearBasicData(data.getBaseDataInstalledCapacity().setScale(2, RoundingMode.HALF_UP));
            }
            if (data.getYearTypeCode().compareTo(LoadYearTypeCodeEnum.LOAD_LEVEL.getCode()) == 0) {
                keyDataPowerDevelopmentPlanRespVO.setLevelYearBasicData(data.getBaseDataInstalledCapacity().setScale(2, RoundingMode.HALF_UP));
            }
            this.fillKeyDataPowerDevelopmentPlanSubData(data, keyDataPowerDevelopmentPlanRespVO);
        }
        return keyDataPowerDevelopmentPlanRespVO;
    }

    /**
     * 填充 电源发展规划 中 {@link KeyDataPowerDevelopmentPlanSubData} 部分的值
     *
     * @param data                              内部处理 的 电源发展规划 的 数据体
     * @param keyDataPowerDevelopmentPlanRespVO 填充 {@link KeyDataPowerDevelopmentPlanSubData} 部分后的 电源发展规划 的结果
     */
    private void fillKeyDataPowerDevelopmentPlanSubData(KeyDataPowerDevelopmentPlan data, KeyDataPowerDevelopmentPlanRespVO keyDataPowerDevelopmentPlanRespVO) {
        KeyDataPowerDevelopmentPlanSubData planSubData = new KeyDataPowerDevelopmentPlanSubData();
        if (data.getDataType().compareTo(provincePowerDevelopmentPlanDataType) == 0 && keyDataPowerDevelopmentPlanRespVO.getProvincePower() != null) {
            planSubData = keyDataPowerDevelopmentPlanRespVO.getProvincePower();
        }
        if (data.getDataType().compareTo(transferOutPowerDevelopmentPlanDataType) == 0 && keyDataPowerDevelopmentPlanRespVO.getSendPower() != null) {
            planSubData = keyDataPowerDevelopmentPlanRespVO.getSendPower();
        }
        if (data.getDataType().compareTo(transferInPowerDevelopmentPlanDataType) == 0 && keyDataPowerDevelopmentPlanRespVO.getReceivePower() != null) {
            planSubData = keyDataPowerDevelopmentPlanRespVO.getReceivePower();
        }

        if (data.getYearTypeCode().compareTo(LoadYearTypeCodeEnum.LOAD_CURRENT.getCode()) == 0) {
            planSubData.setCurrentYear(data.getYear());
            planSubData.setCurrentYearData(data.getYearInstalledCapacity().setScale(2, RoundingMode.HALF_UP));
        }
        if (data.getYearTypeCode().compareTo(LoadYearTypeCodeEnum.LOAD_LEVEL.getCode()) == 0) {
            planSubData.setLevelYear(data.getYear());
            planSubData.setLevelYearData(data.getYearInstalledCapacity().setScale(2, RoundingMode.HALF_UP));
        }
        this.fillKeyDataPowerDevelopmentPlanSubDataTable(planSubData, data.getStation(), data.getYearTypeCode());

        if (data.getDataType().compareTo(provincePowerDevelopmentPlanDataType) == 0) {
            keyDataPowerDevelopmentPlanRespVO.setProvincePower(planSubData);
        }
        if (data.getDataType().compareTo(transferOutPowerDevelopmentPlanDataType) == 0) {
            keyDataPowerDevelopmentPlanRespVO.setSendPower(planSubData);
        }
        if (data.getDataType().compareTo(transferInPowerDevelopmentPlanDataType) == 0) {
            keyDataPowerDevelopmentPlanRespVO.setReceivePower(planSubData);
        }
    }

    /**
     * 填充 电源发展规划 中 {@link KeyDataStationVO} 部分的值
     *
     * @param planSubData  电源发展规划 中 电站 相关的数据容器
     * @param stations     电站原本的信息
     * @param yearTypeCode 用于判断 水平年 还是 现状年
     */
    private void fillKeyDataPowerDevelopmentPlanSubDataTable(KeyDataPowerDevelopmentPlanSubData planSubData, List<KeyDataStationVO> stations, Integer yearTypeCode) {
        List<KeyDataPowerDevelopmentPlanSubDataTable> tableData = new ArrayList<>();
        if (planSubData.getTableData() != null) {
            tableData = planSubData.getTableData();
        }

        for (KeyDataStationVO station : stations) {
            String typeName = station.getStationTypeName();
            BigDecimal stationInstalledCapacity = station.getInstalledCapacity();
            stationInstalledCapacity = stationInstalledCapacity.setScale(2, RoundingMode.HALF_UP);
            boolean isUpdate = false;
            for (KeyDataPowerDevelopmentPlanSubDataTable data : tableData) {
                if (!data.getTypeName().equals(typeName)) {
                    continue;
                }
                if (yearTypeCode.compareTo(LoadYearTypeCodeEnum.LOAD_CURRENT.getCode()) == 0) {
                    data.setCurrentYearData(stationInstalledCapacity);
                }
                if (yearTypeCode.compareTo(LoadYearTypeCodeEnum.LOAD_LEVEL.getCode()) == 0) {
                    data.setLevelYearData(stationInstalledCapacity);
                }
                isUpdate = true;
                break;
            }
            if (!isUpdate) {
                KeyDataPowerDevelopmentPlanSubDataTable data = new KeyDataPowerDevelopmentPlanSubDataTable();
                data.setTypeName(typeName);
                data.setCurrentYearData(BigDecimal.ZERO);
                data.setLevelYearData(BigDecimal.ZERO);
                if (yearTypeCode.compareTo(LoadYearTypeCodeEnum.LOAD_CURRENT.getCode()) == 0) {
                    data.setCurrentYearData(stationInstalledCapacity);
                }
                if (yearTypeCode.compareTo(LoadYearTypeCodeEnum.LOAD_LEVEL.getCode()) == 0) {
                    data.setLevelYearData(stationInstalledCapacity);
                }
                tableData.add(data);
            }
        }

        planSubData.setTableData(tableData);
    }

    /**
     * 转换数据到 KeyDataPowerBalanceParameterRespVO 类型
     *
     * @param data 组装好的 电力平衡关键参数 的数据
     * @return 组装好的 KeyDataPowerBalanceParameterRespVO 类型的数据
     */
    private KeyDataPowerBalanceParameterRespVO convert2KeyDataPowerBalanceParameterRespVO(KeyDataPowerBalanceParameter... data) {
        KeyDataPowerBalanceParameterRespVO powerBalanceParameterRespVO = new KeyDataPowerBalanceParameterRespVO();
        Map<String, KeyDataPowerBalanceParameterTable> tableDataMap = new HashMap<>();

        for (KeyDataPowerBalanceParameter item : data) {
            powerBalanceParameterRespVO.setLoadSideFactor(item.getLoadSideFactor());
            powerBalanceParameterRespVO.setLoadReserveFactor(item.getLoadReserveFactor());
            powerBalanceParameterRespVO.setOverhaulReserveFactor(item.getOverhaulReserveFactor());
            powerBalanceParameterRespVO.setAccidentReserveFactor(item.getAccidentReserveFactor());

            for (KeyDataStationPowerBalanceParameterVO station : item.getStation()) {
                KeyDataPowerBalanceParameterTable tempTableData = tableDataMap.computeIfAbsent(station.getStationTypeName(), k -> new KeyDataPowerBalanceParameterTable(station.getStationTypeName(), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO));

                if (item.getPowerTypeCode().compareTo(heatingPeriod) == 0) {
                    tempTableData.setHeatingPeriodData(station.getStationParameter());
                    tempTableData.setHeatingBlockedFactor(station.getBlockedFactor());
                    tempTableData.setHeatingInstalledCapacity(station.getInstalledCapacity());
                }
                if (item.getPowerTypeCode().compareTo(noHeatingPeriod) == 0) {
                    tempTableData.setNoHeatingPeriodData(station.getStationParameter());
                    tempTableData.setNoHeatingBlockedFactor(station.getBlockedFactor());
                    tempTableData.setNoHeatingInstalledCapacity(station.getInstalledCapacity());
                }
            }
        }

        List<KeyDataPowerBalanceParameterTable> tableData = tableDataMap.values().stream().sorted((Comparator.comparingInt(o -> StationCategoryCodeEnum.NAME_CODE_MAP.getOrDefault(o.getPowerType(), 1)))).collect(Collectors.toList());
        powerBalanceParameterRespVO.setPowerBalanceParameter(tableData);
        return powerBalanceParameterRespVO;
    }

    /**
     * 转换数据到 KeyDataPowerBalanceParameter 类型
     *
     * @param existedStationEnergyPercentDOs 存在的计算好的 电力平衡关键参数百分比 数据
     * @param loadYearDO                     现状年，水平年相关的数据
     * @param heatingPeriodType              供热期类型
     * @return 组装好的 KeyDataPowerBalanceParameter 类型的数据
     */
    private KeyDataPowerBalanceParameter convert2KeyDataPowerBalanceParameter(List<StationEnergyPercentDO> existedStationEnergyPercentDOs, LoadYearDO loadYearDO, Integer heatingPeriodType) {
        KeyDataPowerBalanceParameter data = new KeyDataPowerBalanceParameter();
        List<KeyDataStationPowerBalanceParameterVO> dataStations = new ArrayList<KeyDataStationPowerBalanceParameterVO>();

        if (loadYearDO.getLoadSideFactor() == null) {
            data.setLoadSideFactor(null);
        } else {
            data.setLoadSideFactor(loadYearDO.getLoadSideFactor().setScale(2, RoundingMode.HALF_UP));
        }
        if (loadYearDO.getLoadReserveFactor() == null) {
            data.setLoadReserveFactor(null);
        } else {
            data.setLoadReserveFactor(loadYearDO.getLoadReserveFactor().setScale(2, RoundingMode.HALF_UP));
        }
        if (loadYearDO.getOverhaulReserveFactor() == null) {
            data.setOverhaulReserveFactor(null);
        } else {
            data.setOverhaulReserveFactor(loadYearDO.getOverhaulReserveFactor().setScale(2, RoundingMode.HALF_UP));
        }
        if (loadYearDO.getAccidentReserveFactor() == null) {
            data.setAccidentReserveFactor(null);
        } else {
            data.setAccidentReserveFactor(loadYearDO.getAccidentReserveFactor().setScale(2, RoundingMode.HALF_UP));
        }

        data.setPowerTypeCode(heatingPeriodType);
        data.setStation(dataStations);
        for (StationEnergyPercentDO stationEnergyPercent : existedStationEnergyPercentDOs) {
            if (stationEnergyPercent.getHeatingPeriodType().compareTo(heatingPeriodType) != 0) {
                continue;
            }

            KeyDataStationPowerBalanceParameterVO dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._1.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._1.getCode()) + "（常规）");
            dataStation.setStationParameter(stationEnergyPercent.getCoalElectricity().setScale(2, RoundingMode.HALF_UP));
            dataStation.setInstalledCapacity(stationEnergyPercent.getCoalElectricityCapacity());
            dataStation.setBlockedFactor(stationEnergyPercent.getCoalElectricityFactor());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._1.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._1.getCode()) + "（供热）");
            //dataStation.setStationParameter(stationEnergyPercent.getCoalElectricityHeatCapacity().setScale(2, RoundingMode.HALF_UP));
            dataStation.setInstalledCapacity(stationEnergyPercent.getCoalElectricityHeatCapacity());
            dataStation.setBlockedFactor(stationEnergyPercent.getCoalElectricityHeatFactor());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._2.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._2.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getGasElectricity().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getGasElectricityFactor());
            dataStation.setInstalledCapacity(stationEnergyPercent.getGasElectricityCapacity());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._3.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._3.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getNuclearPower().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getNuclearPowerFactor());
            dataStation.setInstalledCapacity(stationEnergyPercent.getNuclearPowerCapacity());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._4.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._4.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getHydroPower().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getHydroPowerFactor());
            dataStation.setInstalledCapacity(stationEnergyPercent.getHydroPowerCapacity());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._5.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._5.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getPumpedStorage().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getPumpedStorageFactor());
            dataStation.setInstalledCapacity(stationEnergyPercent.getPumpedStorageCapacity());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._6.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._6.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getWindPower().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getWindPowerFactor());
            dataStation.setInstalledCapacity(stationEnergyPercent.getWindPowerCapacity());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._7.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._7.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getPhotovoltaic().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getPhotovoltaicFactor());
            dataStation.setInstalledCapacity(stationEnergyPercent.getPhotovoltaicCapacity());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._8.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._8.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getPhotothermal().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getPhotothermalFactor());
            dataStation.setInstalledCapacity(stationEnergyPercent.getPhotothermalCapacity());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._9.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._9.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getBiomassPower().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getBiomassPowerFactor());
            dataStation.setInstalledCapacity(stationEnergyPercent.getBiomassPowerCapacity());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._10.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._10.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getNewEnergy().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getNewEnergyFactor());
            dataStation.setInstalledCapacity(stationEnergyPercent.getNewEnergyCapacity());
            dataStations.add(dataStation);

            dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(StationCategoryCodeEnum._11.getCode());
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(StationCategoryCodeEnum._11.getCode()));
            dataStation.setStationParameter(stationEnergyPercent.getOther().setScale(2, RoundingMode.HALF_UP));
            dataStation.setBlockedFactor(stationEnergyPercent.getOther());
            dataStation.setInstalledCapacity(stationEnergyPercent.getOther());
            dataStations.add(dataStation);
        }

        return data;
    }

    /**
     * 转换数据到 KeyDataPowerBalanceParameter 类型
     *
     * @param essStationMap          电源相关信息
     * @param stationScheduleWeekDOs 电源中具体电站的周信息
     * @param loadYearDO             现状年，水平年相关的数据
     * @param heatingPeriodType      供热期类型
     * @return 组装好的 KeyDataPowerBalanceParameter 类型的数据
     */
    private KeyDataPowerBalanceParameter convert2KeyDataPowerBalanceParameter(Map<Long, EssStationDO> essStationMap, List<StationScheduleWeekDO> stationScheduleWeekDOs, LoadYearDO loadYearDO, Integer heatingPeriodType) {
        BigDecimal loadSideFactor = loadYearDO.getLoadSideFactor().setScale(2, RoundingMode.HALF_UP);
        BigDecimal loadReserveFactor = loadYearDO.getLoadReserveFactor().setScale(2, RoundingMode.HALF_UP);
        BigDecimal overhaulReserveFactor = loadYearDO.getOverhaulReserveFactor().setScale(2, RoundingMode.HALF_UP);
        BigDecimal accidentReserveFactor = loadYearDO.getAccidentReserveFactor().setScale(2, RoundingMode.HALF_UP);

        Integer noHeatingPeriodStartingWeek = 17;
        Integer noHeatingPeriodEnddingWeek = 39;

        Map<Integer, Map<String, BigDecimal>> stationInfos = new HashMap<>();
        for (StationCategoryCodeEnum stationCategoryCodeEnum : StationCategoryCodeEnum.values()) {
            Integer stationCategoryCode = stationCategoryCodeEnum.getCode();
            stationInfos.put(stationCategoryCode, new HashMap<String, BigDecimal>() {{
                put("denominator", BigDecimal.ZERO);
                put("numerator", BigDecimal.ZERO);
            }});
        }
        Set<Integer> existedCategoryCode = new HashSet<>();
        for (EssStationDO essStationDO : essStationMap.values()) {
            Long stationId = essStationDO.getId();
            BigDecimal stationInstalledCapacity = essStationDO.getInstalledCapacity();
            Map<String, BigDecimal> stationInfo = stationInfos.get(essStationDO.getCategoryCode());
            existedCategoryCode.add(essStationDO.getCategoryCode());
            for (StationScheduleWeekDO stationScheduleWeekDO : stationScheduleWeekDOs) {
                if (stationScheduleWeekDO.getStationId().compareTo(stationId) != 0) {
                    continue;
                }

                Integer week = stationScheduleWeekDO.getWeek();
                if ((heatingPeriodType.equals(heatingPeriod) && (week < noHeatingPeriodStartingWeek || week > noHeatingPeriodEnddingWeek)) || (!heatingPeriodType.equals(heatingPeriod) && week >= noHeatingPeriodStartingWeek && week <= noHeatingPeriodEnddingWeek)) {
                    BigDecimal denominator = stationInfo.get("denominator");
                    BigDecimal numerator = stationInfo.get("numerator");
                    numerator = numerator.add(BigDecimal.ONE.subtract(stationScheduleWeekDO.getBlockedFactor()).multiply(stationInstalledCapacity));
                    denominator = denominator.add(stationInstalledCapacity);
                    stationInfo.put("denominator", denominator);
                    stationInfo.put("numerator", numerator);
                }
            }
            stationInfos.put(essStationDO.getCategoryCode(), stationInfo);
        }
        KeyDataPowerBalanceParameter data = new KeyDataPowerBalanceParameter();
        List<KeyDataStationPowerBalanceParameterVO> dataStations = new ArrayList<KeyDataStationPowerBalanceParameterVO>();
        data.setLoadSideFactor(loadSideFactor);
        data.setLoadReserveFactor(loadReserveFactor);
        data.setOverhaulReserveFactor(overhaulReserveFactor);
        data.setAccidentReserveFactor(accidentReserveFactor);
        data.setPowerTypeCode(heatingPeriodType);
        data.setStation(dataStations);

        stationInfos.forEach((categoryCode, stationInfo) -> {
            KeyDataStationPowerBalanceParameterVO dataStation = new KeyDataStationPowerBalanceParameterVO();
            dataStation.setCategoryCode(categoryCode);
            dataStation.setStationTypeName(StationCategoryCodeEnum.getName(categoryCode));
            if (stationInfo.get("numerator").compareTo(BigDecimal.ZERO) != 0) {
                dataStation.setStationParameter(stationInfo.get("numerator").multiply(BigDecimal.valueOf(100L)).divide(stationInfo.get("denominator"), 2, RoundingMode.HALF_UP));
            } else if (defaultStationParameter.get(categoryCode) != null) {
                dataStation.setStationParameter(defaultStationParameter.get(categoryCode).setScale(2, RoundingMode.HALF_UP));
            }
            dataStations.add(dataStation);
        });

        for (StationCategoryCodeEnum stationCategoryCodeEnum : StationCategoryCodeEnum.values()) {
            Integer stationCategoryCode = stationCategoryCodeEnum.getCode();
            if (!existedCategoryCode.contains(stationCategoryCode)) {
                KeyDataStationPowerBalanceParameterVO tempDataStation = new KeyDataStationPowerBalanceParameterVO();
                tempDataStation.setCategoryCode(stationCategoryCode);
                tempDataStation.setStationTypeName(StationCategoryCodeEnum.getName(stationCategoryCode));
                if (defaultStationParameter.get(stationCategoryCode) != null) {
                    tempDataStation.setStationParameter(defaultStationParameter.get(stationCategoryCode).setScale(2, RoundingMode.HALF_UP));
                }
                dataStations.add(tempDataStation);
            }
        }

        return data;
    }


    /*//**
     * 转换数据到 StationEnergyPercentDO 类型
     *
     * @param dataStations 前端传入的 电力平衡关键参数 相关的数据
     * @return 组装好的 StationEnergyPercentDO 类型的数据
     */
    private StationEnergyPercentDO convert2StationEnergyPercentDO(List<PnlElectricityPowerRespVO> powerRespVOS, Integer heating) {
        StationEnergyPercentDO stationEnergyPercentDO = new StationEnergyPercentDO();
        for (PnlElectricityPowerRespVO v : powerRespVOS) {
            BigDecimal capacity = heating == heatingPeriod ? v.getHeatingCapacity() : v.getNonHeatingCapacity();
            BigDecimal coalElectricityFactor = heating == heatingPeriod ? v.getHeatingResistCoeff() : v.getNonHeatingResistCoeff();
            if (v.getPowerType() == StationCategoryCodeEnum.heatingCoalCode()) {
                //stationEnergyPercentDO.setCoalElectricity(v.getStationParameter());
                stationEnergyPercentDO.setCoalElectricityHeatCapacity(capacity);
                stationEnergyPercentDO.setCoalElectricityHeatFactor(coalElectricityFactor);
                continue;
            }

            if (v.getPowerType() == StationCategoryCodeEnum.noHeatingCoalCode()) {
                //stationEnergyPercentDO.setCoalElectricity(v.getStationParameter());
                stationEnergyPercentDO.setCoalElectricityCapacity(capacity);
                stationEnergyPercentDO.setCoalElectricityFactor(coalElectricityFactor);
                continue;
            }

            if (v.getPowerType().compareTo(StationCategoryCodeEnum._2.getCode()) == 0) {
                //stationEnergyPercentDO.setGasElectricity(v.getStationParameter());
                stationEnergyPercentDO.setGasElectricityCapacity(capacity);
                stationEnergyPercentDO.setGasElectricityFactor(coalElectricityFactor);
                continue;
            }
            if (v.getPowerType().compareTo(StationCategoryCodeEnum._3.getCode()) == 0) {
                //stationEnergyPercentDO.setNuclearPower(v.getStationParameter());
                stationEnergyPercentDO.setNuclearPowerCapacity(capacity);
                stationEnergyPercentDO.setNuclearPowerFactor(coalElectricityFactor);
                continue;
            }
            if (v.getPowerType().compareTo(StationCategoryCodeEnum._4.getCode()) == 0) {
                // stationEnergyPercentDO.setHydroPower(v.getStationParameter());
                stationEnergyPercentDO.setHydroPowerCapacity(capacity);
                stationEnergyPercentDO.setHydroPowerFactor(coalElectricityFactor);
                continue;
            }
            if (v.getPowerType().compareTo(StationCategoryCodeEnum._5.getCode()) == 0) {
                //stationEnergyPercentDO.setPumpedStorage(v.getStationParameter());
                stationEnergyPercentDO.setPumpedStorageCapacity(capacity);
                stationEnergyPercentDO.setPumpedStorageFactor(coalElectricityFactor);
                continue;
            }
            if (v.getPowerType().compareTo(StationCategoryCodeEnum._6.getCode()) == 0) {
                // stationEnergyPercentDO.setWindPower(v.getStationParameter());
                stationEnergyPercentDO.setWindPowerCapacity(capacity);
                stationEnergyPercentDO.setWindPowerFactor(coalElectricityFactor);
                continue;
            }
            if (v.getPowerType().compareTo(StationCategoryCodeEnum._7.getCode()) == 0) {
                // stationEnergyPercentDO.setPhotovoltaic(v.getStationParameter());
                stationEnergyPercentDO.setPhotovoltaicCapacity(capacity);
                stationEnergyPercentDO.setPhotovoltaicFactor(coalElectricityFactor);
                continue;
            }
            if (v.getPowerType().compareTo(StationCategoryCodeEnum._8.getCode()) == 0) {
                //stationEnergyPercentDO.setPhotothermal(v.getStationParameter());
                stationEnergyPercentDO.setPhotothermalCapacity(capacity);
                stationEnergyPercentDO.setPhotothermalFactor(coalElectricityFactor);
                continue;
            }
            if (v.getPowerType().compareTo(StationCategoryCodeEnum._9.getCode()) == 0) {
                //stationEnergyPercentDO.setBiomassPower(v.getStationParameter());
                stationEnergyPercentDO.setBiomassPowerCapacity(capacity);
                stationEnergyPercentDO.setBiomassPowerFactor(coalElectricityFactor);
                continue;
            }
            if (v.getPowerType().compareTo(StationCategoryCodeEnum._10.getCode()) == 0) {
                // stationEnergyPercentDO.setNewEnergy(v.getStationParameter());
                stationEnergyPercentDO.setNewEnergyCapacity(capacity);
                stationEnergyPercentDO.setNewEnergyFactor(coalElectricityFactor);
                continue;
            }
            if (v.getPowerType().compareTo(StationCategoryCodeEnum._11.getCode()) == 0) {
                //stationEnergyPercentDO.setOther(v.getStationParameter());
                stationEnergyPercentDO.setOtherCapacity(capacity);
                stationEnergyPercentDO.setOtherFactor(coalElectricityFactor);
                continue;
            }
        }

        return stationEnergyPercentDO;
    }


    /**
     * 转换数据到 StationEnergyPercentDO 类型
     *
     * @param dataStations 前端传入的 电力平衡关键参数 相关的数据
     * @return 组装好的 StationEnergyPercentDO 类型的数据
     */
    private StationEnergyPercentDO convert2StationEnergyPercentDO(List<KeyDataStationPowerBalanceParameterVO> dataStations) {
        StationEnergyPercentDO stationEnergyPercentDO = new StationEnergyPercentDO();
        for (KeyDataStationPowerBalanceParameterVO v : dataStations) {

            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._1.getCode()) == 0) {
                stationEnergyPercentDO.setCoalElectricity(v.getStationParameter());
                stationEnergyPercentDO.setCoalElectricityCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setCoalElectricityFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._2.getCode()) == 0) {
                stationEnergyPercentDO.setGasElectricity(v.getStationParameter());
                stationEnergyPercentDO.setGasElectricityCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setGasElectricityFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._3.getCode()) == 0) {
                stationEnergyPercentDO.setNuclearPower(v.getStationParameter());
                stationEnergyPercentDO.setNuclearPowerCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setNuclearPowerFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._4.getCode()) == 0) {
                stationEnergyPercentDO.setHydroPower(v.getStationParameter());
                stationEnergyPercentDO.setHydroPowerCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setHydroPowerFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._5.getCode()) == 0) {
                stationEnergyPercentDO.setPumpedStorage(v.getStationParameter());
                stationEnergyPercentDO.setPumpedStorageCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setPumpedStorageFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._6.getCode()) == 0) {
                stationEnergyPercentDO.setWindPower(v.getStationParameter());
                stationEnergyPercentDO.setWindPowerCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setWindPowerFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._7.getCode()) == 0) {
                stationEnergyPercentDO.setPhotovoltaic(v.getStationParameter());
                stationEnergyPercentDO.setPhotovoltaicCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setPhotovoltaicFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._8.getCode()) == 0) {
                stationEnergyPercentDO.setPhotothermal(v.getStationParameter());
                stationEnergyPercentDO.setPhotothermalCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setPhotothermalFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._9.getCode()) == 0) {
                stationEnergyPercentDO.setBiomassPower(v.getStationParameter());
                stationEnergyPercentDO.setBiomassPowerCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setBiomassPowerFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._10.getCode()) == 0) {
                stationEnergyPercentDO.setNewEnergy(v.getStationParameter());
                stationEnergyPercentDO.setNewEnergyCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setNewEnergyFactor(v.getBlockedFactor());
                continue;
            }
            if (v.getCategoryCode().compareTo(StationCategoryCodeEnum._11.getCode()) == 0) {
                stationEnergyPercentDO.setOther(v.getStationParameter());
                stationEnergyPercentDO.setOtherCapacity(v.getInstalledCapacity());
                stationEnergyPercentDO.setOtherFactor(v.getBlockedFactor());
                continue;
            }
        }

        return stationEnergyPercentDO;
    }

    /**
     * 插入 前端传入的 电力平衡关键参数 相关的数据
     *
     * @param projectId         项目ID
     * @param heatingPeriodType 供热期类型
     * @param dataStations      前端传入的 电力平衡关键参数 相关的数据
     */
    private void insertStationEnergyPercent(Long projectId, Integer heatingPeriodType, List<PnlElectricityPowerRespVO> dataStations) {
        StationEnergyPercentDO stationEnergyPercentDO = this.convert2StationEnergyPercentDO(dataStations, heatingPeriodType);
        stationEnergyPercentDO.setProjectId(projectId);
        stationEnergyPercentDO.setHeatingPeriodType(heatingPeriodType);
        stationEnergyPercentMapper.insert(stationEnergyPercentDO);
    }
}


