package cn.powerchina.bjy.cloud.institute.essz.controller.admin.stationwindpowerphotovoltaic.vo.PowerPhotovoltaic;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "风电、光伏曲线")
public class EssStationSceneryCurveVo implements Serializable {

    @Schema(description = "出力保证率数据")
    private List<AssuranceRate> assuranceRates;

    @Schema(description = "出力电量数据")
    private List<QuantityOfElectricity> quantityOfEnergies;

    @Schema(description = "右侧表格数据", name = "rightTableDatalist (右侧表格数据)")
    private List<RightTableData> rightTableDatalist;

    @Schema(description = "右侧表格数据默认", name = "rightTableDatalistDefault (右侧表格数据默认)")
    private List<RightTableData> rightTableDatalistDefault;

    @Schema(description = "图表", name = "电量系数-出力系数")
    private List<RightTableData> quantityRates;


    @Data
    public static class AssuranceRate implements Serializable {
//        @Schema(description = "图例展示文字")
//        private String legend = "出力保证率";

        @Schema(description = "保证率")
        private String x;

        @Schema(description = "出力系数")
        private String y;

        @Schema(description = "月")
        private Integer month;
        @Schema(description = "天")
        private Integer day;
        @Schema(description = "时")
        private String hour;
    }

    @Data
    public static class QuantityOfElectricity implements Serializable {
//        @Schema(description = "图例展示文字")
//        private String legend = "出力电量";

        @Schema(description = "电量")
        private String x;

        @Schema(description = "出力系数")
        private String y;

        @Schema(description = "月")
        private Integer month;
        @Schema(description = "天")
        private Integer day;
        @Schema(description = "时")
        private String hour;

        @JsonIgnore
        @Schema(description = "累计电能n")
        private BigDecimal tEnergy;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RightTableData implements Serializable {
        @Schema(description = "保证率")
        private Integer assuranceRates;

        @Schema(description = "出力系数")
        private String outputFactor;

        @Schema(description = "电量系数")
        private String energyOutputFactor;
    }
}
