package cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterinvest;

import cn.powerchina.bjy.cloud.framework.apilog.core.annotation.ApiAccessLog;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterinvest.vo.ParamterInvestPageReqVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterinvest.vo.ParamterInvestRespVO;
import cn.powerchina.bjy.cloud.institute.essz.controller.admin.paramterinvest.vo.ParamterInvestSaveReqVO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.paramterinvest.ParamterInvestDO;
import cn.powerchina.bjy.cloud.institute.essz.service.paramterinvest.ParamterInvestService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "8760 - 基础数据 - 电源经济性参数(投资)")
@RestController
@RequestMapping("/plan/ess/paramter-invest")
@Validated
public class ParamterInvestController {

    @Resource
    private ParamterInvestService paramterInvestService;

    @PostMapping("/create")
    @Operation(summary = "创建电源经济性参数(投资)")
    @PreAuthorize("@ss.hasPermission('ess:paramter-invest:create')")
    public CommonResult<Long> createParamterInvest(@Valid @RequestBody ParamterInvestSaveReqVO createReqVO) {
        return success(paramterInvestService.createParamterInvest(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新电源经济性参数(投资)")
    // @PreAuthorize("@ss.hasPermission('ess:paramter-invest:update')")
    public CommonResult<Boolean> updateParamterInvest(@Valid @RequestBody ParamterInvestSaveReqVO updateReqVO) {
        paramterInvestService.updateParamterInvest(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除电源经济性参数(投资)")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ess:paramter-invest:delete')")
    public CommonResult<Boolean> deleteParamterInvest(@RequestParam("id") Long id) {
        paramterInvestService.deleteParamterInvest(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得电源经济性参数(投资)")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ess:paramter-invest:query')")
    public CommonResult<ParamterInvestRespVO> getParamterInvest(@RequestParam("id") Long id) {
        ParamterInvestDO paramterInvest = paramterInvestService.getParamterInvest(id);
        return success(BeanUtils.toBean(paramterInvest, ParamterInvestRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得电源经济性参数(投资)分页")
    @PreAuthorize("@ss.hasPermission('ess:paramter-invest:query')")
    public CommonResult<PageResult<ParamterInvestRespVO>> getParamterInvestPage(@Valid ParamterInvestPageReqVO pageReqVO) {
        PageResult<ParamterInvestDO> pageResult = paramterInvestService.getParamterInvestPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ParamterInvestRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出电源经济性参数(投资) Excel")
    @PreAuthorize("@ss.hasPermission('ess:paramter-invest:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportParamterInvestExcel(@Valid ParamterInvestPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ParamterInvestDO> list = paramterInvestService.getParamterInvestPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "电源经济性参数(投资).xls", "数据", ParamterInvestRespVO.class,
                        BeanUtils.toBean(list, ParamterInvestRespVO.class));
    }

    @GetMapping("/all")
    @Operation(summary = "获得某项目所有电源经济性参数(投资)")
    @Parameter(name = "projectId", description = "工程ID", required = true, example = "4")
    // @PreAuthorize("@ss.hasPermission('ess:paramter-invest:query')")
    public CommonResult<List<ParamterInvestRespVO>> listParamterInvest(@RequestParam("projectId") Long projectId) {
        List<ParamterInvestDO> paramterInvestRespDos = paramterInvestService.listParamterInvest(projectId);
        return success(BeanUtils.toBean(paramterInvestRespDos, ParamterInvestRespVO.class));
    }
}