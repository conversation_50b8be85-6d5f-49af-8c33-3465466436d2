package cn.powerchina.bjy.cloud.institute.essz.controller.admin.loadyear.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 电力网内负荷-现状年、水平年分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LoadYearPageReqVO extends PageParam {

    @Schema(description = "工程ID", example = "")
    private Long projectId;

    @Schema(description = "年度")
    private String year;

    @Schema(description = "年类型（1-现状年、2-水平年）")
    private Integer typeCode;

    @Schema(description = "最大负荷/万kW")
    private BigDecimal maxLoad;

    @Schema(description = "用电量/亿kWh")
    private BigDecimal usePower;

    @Schema(description = "新能源规模/万kW")
    private BigDecimal newEnergy;

    @Schema(description = "风电规模/万kW")
    private BigDecimal windEnergy;

    @Schema(description = "光伏规模/万kW")
    private BigDecimal pvEnergy;

    @Schema(description = "新能源弃电率/%")
    private BigDecimal newCurtailRate;

    @Schema(description = "风电弃电率/%")
    private BigDecimal windCurtailRate;

    @Schema(description = "光伏弃电率/%")
    private BigDecimal pvCurtailRate;

    @Schema(description = "线损/%")
    private BigDecimal lineLossRate;

    @Schema(description = "负荷侧管理系数/%")
    private BigDecimal loadSideFactor;

    @Schema(description = "负荷备用系数/%")
    private BigDecimal loadReserveFactor;

    @Schema(description = "检修备用系数/%")
    private BigDecimal overhaulReserveFactor;

    @Schema(description = "事故备用系数/%")
    private BigDecimal accidentReserveFactor;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private @NotNull(
            message = "每页条数不能为空"
    ) @Min(
            value = -1L,
            message = "每页条数最小值为 1"
    ) @Max(
            value = 500L,
            message = "每页条数最大值为 500"
    ) Integer pageSize=10;

}