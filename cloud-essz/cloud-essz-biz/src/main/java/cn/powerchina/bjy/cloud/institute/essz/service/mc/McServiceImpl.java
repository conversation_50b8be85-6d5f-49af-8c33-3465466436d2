package cn.powerchina.bjy.cloud.institute.essz.service.mc;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.infra.api.config.ConfigApi;
import cn.powerchina.bjy.cloud.infra.api.config.dto.ConfigDTO;
import cn.powerchina.bjy.cloud.infra.api.file.FileApi;
import cn.powerchina.bjy.cloud.institute.essz.constants.EssConstant;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.project.EssProjectDO;
import cn.powerchina.bjy.cloud.institute.essz.dal.dataobject.station.EssStationDO;
import cn.powerchina.bjy.cloud.institute.essz.constants.ErrorCodeConstants;
import cn.powerchina.bjy.cloud.institute.essz.enums.SheetDataTypeEnum;
import cn.powerchina.bjy.cloud.institute.essz.enums.SheetInfoEnum;
import cn.powerchina.bjy.cloud.institute.essz.enums.StationStatusEnum;
import cn.powerchina.bjy.cloud.institute.essz.service.loadyear.LoadYearService;
import cn.powerchina.bjy.cloud.institute.essz.service.paramterbase.ParamterBaseService;
import cn.powerchina.bjy.cloud.institute.essz.service.pnlelectricitybase.PnlElectricityBaseService;
import cn.powerchina.bjy.cloud.institute.essz.service.pnlelectricitypower.PnlElectricityPowerService;
import cn.powerchina.bjy.cloud.institute.essz.service.project.EssProjectService;
import cn.powerchina.bjy.cloud.institute.essz.service.station.EssStationService;
import cn.powerchina.bjy.cloud.institute.essz.service.transfer.TransferService;
import cn.powerchina.bjy.cloud.institute.essz.util.ess.EssThreadLocalrHolder;
import cn.powerchina.bjy.cloud.institute.essz.util.ess.ExcelUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.converters.string.StringStringConverter;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.ttl.TtlCallable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.error;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @desc 管理中心业务层
 * @time 2024/7/17 14:20
 */
@Service
@Slf4j
@Validated
public class McServiceImpl implements McService {


    private final ThreadPoolExecutor taskSheetAnalysis;
    private final ThreadPoolExecutor taskSheetItemAnalysis;
    private final ConfigApi configApi;
    private final RedissonClient redissonClient;
    private final EssProjectService essProjectService;
    private final LoadYearService loadYearService;
    private final EssStationService essStationService;
    private final TransferService transferService;
    private final EssRedisService essRedisService;
    private final ParamterBaseService paramterBaseService;

    private final PnlElectricityBaseService pnlElectricityBaseService;
    private final PnlElectricityPowerService pnlElectricityPowerService;

    @Value("${ess.minio-bucket:}")
    private String minioBucket;

    @Autowired
    private FileApi fileApi;

    public McServiceImpl(ThreadPoolExecutor taskSheetAnalysis, ThreadPoolExecutor taskSheetItemAnalysis, ConfigApi configApi, RedissonClient redissonClient, EssProjectService essProjectService, LoadYearService loadYearService, EssStationService essStationService, TransferService transferService, EssRedisService essRedisService, ParamterBaseService paramterBaseService, PnlElectricityBaseService pnlElectricityBaseService, PnlElectricityPowerService pnlElectricityPowerService) {
        this.taskSheetAnalysis = taskSheetAnalysis;
        this.taskSheetItemAnalysis = taskSheetItemAnalysis;
        this.configApi = configApi;
        this.redissonClient = redissonClient;
        this.essProjectService = essProjectService;
        this.loadYearService = loadYearService;
        this.essStationService = essStationService;
        this.transferService = transferService;
        this.essRedisService = essRedisService;
        this.paramterBaseService = paramterBaseService;
        this.pnlElectricityBaseService = pnlElectricityBaseService;
        this.pnlElectricityPowerService = pnlElectricityPowerService;
    }

    @Override
    public CommonResult<String> getExcelTemplateUrl(String key) {
        ConfigDTO configDTO;
        String errorMsg = "获取excel模版不存在";
        try {
            configDTO = configApi.getConfigByKey(key).getData();
        } catch (Exception e) {
            log.error(errorMsg, e);
            return error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), errorMsg);
        }
        if (Objects.isNull(configDTO) || StringUtils.isEmpty(configDTO.getValue())) {
            return error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), errorMsg);
        }
        return success(configDTO.getValue());
    }


    @Override
    public CommonResult<String> upload(MultipartFile file, Long projectId, Integer dataType) {

        EssProjectDO project = essProjectService.getProject(projectId);
        if (Objects.isNull(project)) {
            throw new ServiceException(ErrorCodeConstants.ESS_PROJECT_NOT_EXISTS);
        }
        EssThreadLocalrHolder.setUserId(WebFrameworkUtils.getLoginUserId());
        //是否有错误出现：用于安全停止其他多线程的任务
        //分布式锁
        RLock lock = redissonClient.getLock("8760:" + projectId + ":edit");
        boolean locked;
        try {
            locked = lock.tryLock(1000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.error("获取分布式锁异常", e);
            throw new ServiceException(ErrorCodeConstants.TRY_LOCK_ERROR);
        }
        if (!locked) {
            log.warn("分布式锁互斥");
            throw new ServiceException(ErrorCodeConstants.PROJECT_EDIT_CONFLICT);
        }
        try {
            return dataUpload(file, project, dataType);
        } catch (ServiceException e) {
            throw e;
        } catch (Throwable e) {
            throw new ServiceException(ErrorCodeConstants.EXCEL_ERROR);
        } finally {
            lock.unlock();
            EssThreadLocalrHolder.removeUserId();
        }
    }

    private CommonResult<String> dataUpload(MultipartFile file, EssProjectDO project, Integer dataType) {
        //项目redis数据初始化
        essRedisService.redisDataInit(project);

        SheetDataTypeEnum sheetDataTypeEnum = SheetDataTypeEnum.getByCode(dataType);
        if (Objects.isNull(sheetDataTypeEnum)) {
            throw new ServiceException(ErrorCodeConstants.DATA_TYPE_NOT_EXISTS);
        }
        // 将文件内容读取到字节数组中
        byte[] fileContent;
        try {
            fileContent = file.getBytes();
        } catch (IOException e) {
            log.error("文件解析异常：", e);
            throw new ServiceException(ErrorCodeConstants.EXCEL_ERROR);
        }

        // 创建第一次读取的 ByteArrayInputStream
        try (InputStream firstInputStream = new ByteArrayInputStream(fileContent)) {
            // 获取所有的Sheet数量
            List<ReadSheet> sheets = EasyExcel.read(firstInputStream).build().excelExecutor().sheetList();
            //校验导入的excelsheet页是否正确
            if (CollectionUtil.isNotEmpty(sheets)) {
                Set<String> sheetSet = Arrays.stream(SheetInfoEnum.values()).map(SheetInfoEnum::getCode).collect(Collectors.toSet());
                sheets.forEach(item -> {
                    if (!sheetSet.contains(item.getSheetName())) {
                        throw ServiceExceptionUtil.exception(ErrorCodeConstants.EXCEL_CANNOT_RESOLVED,item.getSheetName());
                    }
                    if (!Objects.equals(sheetDataTypeEnum, SheetDataTypeEnum._0) && !Objects.equals(item.getSheetName(), sheetDataTypeEnum.getName())) {
                        throw ServiceExceptionUtil.exception(ErrorCodeConstants.EXCEL_CANNOT_RESOLVED,item.getSheetName());
                    }
                });
            }


            // 创建任务列表
            List<Callable<String>> tasks = new ArrayList<>();
            String sheetDataTypeEnumName = sheetDataTypeEnum.getName();
            // 遍历每个Sheet并读取数据
            for (ReadSheet sheet : sheets) {
                String sheetName = sheet.getSheetName();
                if (!Objects.equals(sheetDataTypeEnumName, SheetDataTypeEnum._0.getName())) {
                    if (!Objects.equals(sheetName, sheetDataTypeEnumName)) {
                        continue;
                    }
                }
                Callable<String> task = () -> {
                    // 为每个Sheet创建新的 ByteArrayInputStream
                    try {
                        SheetInfoEnum inst = SheetInfoEnum.getInst(sheetName);
                        if (Objects.isNull(inst)) {
                            return sheetName;
                        }
                        try (InputStream sheetInputStream = new ByteArrayInputStream(fileContent)) {
                            ExcelReaderBuilder excelReaderBuilder = EasyExcel.read(sheetInputStream)
                                    .registerReadListener(ExcelUtils.createListener(inst.getClazz(), inst, project, taskSheetItemAnalysis, dataType))
                                    .registerConverter(new StringStringConverter());
                            excelReaderBuilder.sheet(sheet.getSheetNo()).headRowNumber(0).doRead();
                        }
                        return sheetName;
                    } finally {
                        EssThreadLocalrHolder.removeUserId();
                    }
                };
                tasks.add(TtlCallable.get(task));
            }
            Exception exception = null;
            List<Future<String>> results = null;
            try {
                results = taskSheetAnalysis.invokeAll(tasks);
            } catch (Exception e) {
                log.error(ErrorCodeConstants.EXCEL_ERROR.getMsg(), e);
                exception = e;
            }
            for (Future<String> result : results) {
                try {
                    String s = result.get();
                } catch (Exception e) {
                    log.error(ErrorCodeConstants.EXCEL_ERROR.getMsg(), e);
                    exception = e;
                }
            }

            RBucket<DeleteDataVo> bucket = redissonClient.getBucket(EssConstant.DELETE_PROJECT_PREFIX + project.getId());
            DeleteDataVo deleteDataVo = bucket.get();

            //校验电源数据:当导入全部的时候
            List<Long> stationIdList = deleteDataVo.getNewData().getStationIdList();
            exception = validStationData(project, sheetDataTypeEnum, exception, stationIdList);

            //最终执行如果有异常
            if (Objects.nonNull(exception)) {
                //处理数据：保留旧的数据，删除新增的数据
                clearData(deleteDataVo.getNewData());
                throw exception;
            } else {
                //处理数据：删除旧的数据
                clearData(deleteDataVo.getOldData());
                //删除依赖原有数据计算的结果
                cleanCalcData(project.getId());
                log.info("所有任务已完成");
                if (Objects.nonNull(exception)) {
                    throw exception;
                }
            }

        } catch (ServiceException e) {
            String message = essRedisService.hasErrorV2(project.getId());
            if (StringUtils.isNotEmpty(message)) {
                e.setMessage(message);
            }
            throw e;
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            if (Objects.nonNull(cause)) {
                if (cause instanceof ServiceException) {
                    throw (ServiceException) cause;
                }
            }
            ServiceException serviceException = new ServiceException(ErrorCodeConstants.EXCEL_ERROR);
            String message = essRedisService.hasErrorV2(project.getId());
            if (StringUtils.isNotEmpty(message)) {
                serviceException.setMessage(message);
            }
            throw serviceException;
        } catch (ExcelAnalysisException e) {
            log.error("文件解析错误：", e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.EXCEL_ERROR);
        } catch (Throwable e) {
            log.error("系统异常：", e);
            throw new ServiceException(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }
        return success("数据解析完成");
    }

    /**
     * 清理数据
     */
    private void clearData(DeleteDataVo.DataItem dataItem) {
        List<Long> yearIdList = dataItem.getYearIdList();
        List<Long> stationIdList = dataItem.getStationIdList();
        List<Long> transferIdList = dataItem.getTransferIdList();
        Long paramBaseId = dataItem.getParamBaseId();

        if (CollectionUtil.isNotEmpty(yearIdList)) {
            loadYearService.deleteLoadYearByYearIds(yearIdList);
        }
        if (CollectionUtil.isNotEmpty(stationIdList)) {
            essStationService.deleteStationByStationIds(stationIdList);
        }
        if (CollectionUtil.isNotEmpty(transferIdList)) {
            transferService.deleteTransferByTransferIds(transferIdList);
        }
        if (Objects.nonNull(paramBaseId)) {
            paramterBaseService.deleteDataById(paramBaseId);
        }
    }


    /**
     * 清除计算结果
     */
    private boolean cleanCalcData(Long projectId) {
        //删除参数复核基础数据
        pnlElectricityBaseService.deleteByProjectId(projectId);
        //删除参数复核电源数据
        pnlElectricityPowerService.deletePnlElectricityPowerByProjectId(projectId);
        return true;
    }


    /**
     * 校验电源数据
     */
    @Nullable
    private Exception validStationData(EssProjectDO project, SheetDataTypeEnum sheetDataTypeEnum, Exception exception, List<Long> stationIdList) {
        if (sheetDataTypeEnum.equals(SheetDataTypeEnum._0) && Objects.isNull(exception)) {
            //校验：至少有一个电站的投运年-退役年包含设计水平年
            if (CollectionUtil.isEmpty(stationIdList)) {
                log.error("至少有一种电源数据");
                exception = new ServiceException(ErrorCodeConstants.POWER_STATION_DATA_ERROR);
            } else {
                boolean stationDataValid = false;
                //至少有一个投运状态的电站，投运年<=设计水平年=<退役年的电站
                List<EssStationDO> stationDOList = essStationService.selectListByIds(stationIdList);
                for (EssStationDO essStationDO : stationDOList) {
                    String serviceYear = essStationDO.getServiceYear();
                    String retireYear = essStationDO.getRetireYear();
                    String levelYear = project.getLevelYear();
                    if (StrUtil.isAllNotBlank(serviceYear, retireYear, levelYear)) {
                        Integer levelYearInt = Integer.valueOf(levelYear);
                        Integer serviceYearInt = Integer.valueOf(serviceYear);
                        Integer retireYearInt = Integer.valueOf(retireYear);
                        if (!StationStatusEnum._3.getStatusCode().equals(essStationDO.getStatus())) {
                            continue;
                        }
                        if (levelYearInt >= serviceYearInt && levelYearInt <= retireYearInt) {
                            stationDataValid = true;
                            break;
                        }
                    }
                }
                if (!stationDataValid) {
                    log.error(ErrorCodeConstants.POWER_STATION_DATA_ERROR2.getMsg());
                    exception = new ServiceException(ErrorCodeConstants.POWER_STATION_DATA_ERROR2);
                }
            }
        }
        return exception;
    }
}
