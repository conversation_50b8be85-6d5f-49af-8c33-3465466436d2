package cn.powerchina.bjy.cloud.institute.policy.controller.admin.font.policy;

import cn.hutool.core.map.MapBuilder;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.article.vo.ArticleMainPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.article.vo.ArticleMainRespVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.article.vo.ArticlePolicyPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.specification.vo.SpecificationPageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.article.ArticleMainDO;
import cn.powerchina.bjy.cloud.institute.policy.service.article.ArticleMainService;
import cn.powerchina.bjy.cloud.institute.policy.service.topic.TopicService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "政企- 前台 - 政策研究")
@RestController
@RequestMapping("/plan/policy/article")
@Validated

public class PolicyArticleController {
    @Resource
    private ArticleMainService articleMainService;

    @Resource
    public TopicService topicService;

    @GetMapping("/policySearchPage")
    @Operation(summary = "政策搜索")
    public CommonResult<PageResult<ArticleMainRespVO>> policyArticlePage(@Valid ArticleMainPageReqVO pageReqVO) {
        PageResult<ArticleMainDO> pageResult = articleMainService.policySearchPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ArticleMainRespVO.class));
    }

    @GetMapping("/policyIllustratePage")
    @Operation(summary = "政策图解分页")
    public CommonResult<PageResult<ArticleMainRespVO>> policyIllustratePage(@Valid ArticlePolicyPageReqVO pageReqVO) {
        PageResult<ArticleMainRespVO> pageResult = articleMainService.policyIllustratePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ArticleMainRespVO.class));
    }

    @GetMapping("/policyIllustrateInfo")
    @Operation(summary = "政策图解主页")
    public CommonResult<?> policyIllustrateInfo(@Valid ArticlePolicyPageReqVO pageReqVO) {
        Long topicId = topicService.findByCode(pageReqVO.getTopicCode()).getId();
        Map<Object, Object> scienceInfo = MapBuilder.create()
                .put("topImg",articleMainService.getTopImgByTopicId(topicId))
                .put("policyIllustrate", articleMainService.policyIllustratePage(pageReqVO))
                .build();
        return success(scienceInfo);
    }
}



