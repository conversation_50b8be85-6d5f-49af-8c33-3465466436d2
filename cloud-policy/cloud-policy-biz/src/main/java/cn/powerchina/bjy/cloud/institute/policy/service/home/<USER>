package cn.powerchina.bjy.cloud.institute.policy.service.home;

import cn.hutool.core.date.DateUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.home.vo.HomeImagePageReqVO;
import cn.powerchina.bjy.cloud.institute.policy.controller.admin.home.vo.HomeImageSaveReqVO;
import cn.powerchina.bjy.cloud.institute.policy.dal.dataobject.home.HomeImageDO;
import cn.powerchina.bjy.cloud.institute.policy.dal.mysql.home.HomeImageMapper;
import cn.powerchina.bjy.cloud.institute.policy.enums.HomeImageLimitEnum;
import cn.powerchina.bjy.cloud.institute.policy.enums.PublishStatusEnum;
import cn.powerchina.bjy.cloud.institute.policy.enums.HomeImageTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.institute.policy.enums.ErrorCodeConstants.ONLINE_IMAGE_DELETED_FORBIDDEN;
import static cn.powerchina.bjy.cloud.institute.policy.enums.ErrorCodeConstants.ONLINE_NUM_EXCEED_LIMIT;
import static cn.powerchina.bjy.cloud.institute.policy.enums.PublishStatusEnum.ONLINE;
import static cn.powerchina.bjy.cloud.institute.policy.enums.PublishStatusEnum.UNPUBLISHED;

/**
 * 首页轮播图 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HomeImageServiceImpl implements HomeImageService {

    @Resource
    private HomeImageMapper homeImageMapper;


    @Override
    public Long createHomeImage(HomeImageSaveReqVO createReqVO) {
        // 插入
        createReqVO.setStatus(String.valueOf(PublishStatusEnum.UNPUBLISHED.getCode()));
        HomeImageDO homeImage = BeanUtils.toBean(createReqVO, HomeImageDO.class);
        homeImageMapper.insert(homeImage);
        // 返回
        return homeImage.getId();
    }

    @Override
    public void updateHomeImage(HomeImageSaveReqVO updateReqVO) {
        // 校验存在
        validateHomeImageExists(updateReqVO.getId());
        // 更新
        HomeImageDO updateObj = BeanUtils.toBean(updateReqVO, HomeImageDO.class);
        if (updateObj.getStatus() == ONLINE.getCode()) {
            updateObj.setOnlineTime(DateUtil.toLocalDateTime(new Date()));
        }
        if(updateObj.getStatus() == UNPUBLISHED.getCode()){
            updateObj.setOnlineTime(null);
        }
        homeImageMapper.updateById(updateObj);
    }
    @Override
    public void onlineHomeImage(HomeImageSaveReqVO updateReqVO) {
        // 校验存在
        validateHomeImageExists(updateReqVO.getId());
        // 检查当前已上线的数量是否已经达到上限
        if (Objects.equals(updateReqVO.getType(), HomeImageTypeEnum.IMAGE.getCode())) {
            int count = homeImageMapper.countByStatus(PublishStatusEnum.ONLINE.getCode(),updateReqVO.getType());
            if (count >= HomeImageLimitEnum.IMAGE.getCode()) {
                throw exception(ONLINE_NUM_EXCEED_LIMIT);
            }
        }else {
        int count = homeImageMapper.countByStatus(PublishStatusEnum.ONLINE.getCode(),updateReqVO.getType());
            if (count >= HomeImageLimitEnum.TITLE.getCode()) {
            throw exception(ONLINE_NUM_EXCEED_LIMIT);
        }
        }
        // 更新
        HomeImageDO updateObj = BeanUtils.toBean(updateReqVO, HomeImageDO.class);
        updateObj.setStatus(PublishStatusEnum.ONLINE.getCode());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedNowStr = now.format(formatter);
        LocalDateTime formattedNow = LocalDateTime.parse(formattedNowStr, formatter);
        updateObj.setOnlineTime(formattedNow);
        updateObj.setOnliner(Objects.requireNonNull(SecurityFrameworkUtils.getLoginUser()).getName());
        updateObj.setOnlinerId(Objects.requireNonNull(SecurityFrameworkUtils.getLoginUser()).getId());
        homeImageMapper.updateById(updateObj);
    }
    @Override
    public void offlineHomeImage(List<HomeImageSaveReqVO> updateReqVOs) {

        for (HomeImageSaveReqVO updateReqVO : updateReqVOs) {
            // 校验存在
            validateHomeImageExists(updateReqVO.getId());
            Integer status = homeImageMapper.getStatusById(updateReqVO.getId());
            if (status != PublishStatusEnum.UNPUBLISHED.getCode()) {
                // 更新
                homeImageMapper.updateStatus(updateReqVO.getId(), PublishStatusEnum.OFFLINE.getCode());
            }
        }
    }
    @Override
    public void deleteHomeImage(List<Long> ids) {
        Set<Long> deletedIds = new HashSet<>(ids);
        for (Long id : deletedIds) {
            // 校验存在
            validateHomeImageExists(id);
            if(Objects.equals(homeImageMapper.getStatusById(id), PublishStatusEnum.ONLINE.getCode())) {
                throw exception(ONLINE_IMAGE_DELETED_FORBIDDEN);
        }
            homeImageMapper.deleteById(id);
        }
    }

    private void validateHomeImageExists(Long id) {
        if (homeImageMapper.selectById(id) == null) {
            //throw exception(HOME_IMAGE_NOT_EXISTS);
        }
    }

    @Override
    public HomeImageDO getHomeImage(Long id) {
        return homeImageMapper.selectById(id);
    }

    @Override
    public PageResult<HomeImageDO> getHomeImagePage(HomeImagePageReqVO pageReqVO) {
        PageResult<HomeImageDO> pageResult = homeImageMapper.selectPage(pageReqVO);
        return pageResult;
    }
    @Override
    public List<HomeImageDO> getHomeImageList() {
        return homeImageMapper.HomeImageList();
    }
    @Override
    public List<HomeImageDO> getHomeTitleList() {
        return homeImageMapper.HomeTitleList();
    }
}